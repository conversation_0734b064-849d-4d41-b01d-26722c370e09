# Iec61850 7 3{Ed2.0}B

## Introduction
ted information exchange.

This second edition cancels and replaces the first edition, published in 2003.
Compared to the first edition, this second edition:
defines new common data classes used for new standards defining object models for
other domains based on IEC 61850 and for the representation of statistical and historical
data,
provides clarifications and corrections to the first edition of IEC 61850-7-3.

## Normative References

The text of this standard is based on the following documents:
* FDIS
* RVD
57/1087/FDIS
57/1095/RVD
Full information on the voting for the approval of this standard can be found in the report on
voting indicated in the above table.

## Publication Information

This publication has been drafted in accordance with the ISO/IEC Directives, Part 2.
A list of all parts in the IEC 61850 series, published under the general title: Communication networks
and systems for power utility automation, can be found on the IEC website.
The  general title of the series was Communication networks and systems in substations. To
address the extension of the scope of IEC 61850, it has been changed to Communication
networks and systems for power utility automation.

## Stability Date

The committee has decided that the contents of this publication will remain unchanged until
the stability date indicated on the IEC web site under "http://webstore.iec.ch" in the data
related to the specific publication. At this date, the publication will be
reconfirmed,
withdrawn,
replaced by a revised edition, or
amended.

## Colour Inside

IMPORTANT – The 'colour inside' logo on the cover page of this publication indicates
that it contains colours which are considered to be useful for the correct
understanding of its contents. Users should therefore print this document using a
colour printer.

---

## Introduction

This document is part of a set of specifications, which details layered substation com-
munication architecture. This architecture has been chosen to provide abstract definitions of
classes and services such that the specifications are independent of specific protocol stacks
and objects. The mapping of these abstract classes and services to communication stacks is
outside the scope of IEC 61850-7-x and may be found in IEC 61850-8-x (station bus) and
IEC 61850-9-x (process bus).

## Scope

IEC 61850-7-1 gives an overview of this communication architecture. This part of IEC 61850
defines constructed attributed classes and common data classes related to applications in the
power system using IEC 61850 modeling concepts like substations, hydro power or distributed
energy resources. These common data classes are used in IEC 61850-7-4 to define
compatible dataObject classes. The SubDataObjects, DataAttributes or SubAttributes of the
instances of dataObject are accessed using services defined in IEC 61850-7-2.

## Communication Networks and Systems for Power Utility Automation

This part of IEC 61850 is used to specify the abstract common data class and constructed
attribute class definitions. These abstract definitions are mapped into concrete object
definitions that are to be used for a particular protocol (for example MMS, ISO 9506 series).
Note that there are common data classes used for service tracking, that are defined in
IEC 61850-7-2.

## Communication Networks and Systems for Power Utility Automation – Part 7-3

### Basic communication structure – Common data classes

#### Scope

This part of IEC 61850 specifies constructed attribute classes and common data classes
related to substation applications. In particular, it specifies:
* common data classes for status information,
* common data classes for measured information,
* common data classes for control,
* common data classes for status settings,
* common data classes for analogue settings and
* attribute types used in these common data classes.

#### Application

This International Standard is applicable to the description of device models and functions of
substations and feeder equipment.
This International Standard may also be applied, for example, to describe device models and
functions

## Additional Content

for:
substation to substation information exchange,
substation to control centre information exchange,
power plant to control centre information exchange,
information exchange for distributed generation, or
information exchange for metering.
Normative references
The following referenced documents are indispensable for the application of this document.
For dated references, only the edition cited applies. For undated references, the latest edition
of the referenced document (including any amendments) applies.
IEC/TS 61850-2, Communication networks and systems in substations – Part 2: Glossary
IEC 61850-7-1, Communication networks and systems for power utility automation – Part 7-1:
Basic communication structure – Principles and models1
IEC 61850-7-2, Communication networks and systems for power utility automation – Part 7-2:
Basic information and communication structure – Abstract communication service interface
(ACSI)
IEC 61850-7-4, Communication networks and systems for power utility automation – Part 7-4:
Basic communication structure – Compatible logical node classes and data object classes
———————
1  To be published.
--- Page 12 ---
61850-7-3 Ó IEC:2010
IEEE C37.118:2005, IEEE Standard for Synchrophasors for Power Systems
ISO 4217, Codes for the representation of currencies and funds
Terms and definitions
For
the
purposes
this
document,
the
terms
and
definitions
given
IEC/TS 61850-2 and IEC 61850-7-2 apply.
Abbreviated terms
CDC
common data class
dchg
trigger option for data-change
dupd
trigger option for data-update
functional constraint
qchg
trigger option for quality-change
TrgOp
trigger option
NOTE Abbreviations used for the identification of the common data classes and as names of the attributes are
specified in the specific clauses of this document and are not repeated here.
Conditions for attribute inclusion
This clause lists general conditions that specify the presence of an attribute. Table 1 gives the
conditions for presence of attributes.
Table 1 – Conditions for presence of attributes
Abbreviation
Condition
Attribute is mandatory. Attribute shall exist on any CDC type instance.
Attribute is optional. Attribute may or may not exist on any CDC type instance.
PICS_SUBST
Attribute is mandatory, if substitution is supported (for substitution, see IEC 61850-7-2),
otherwise forbidden.
GC_1
At least one of the attributes shall be present for a given instance of DataObject /
SubDataObject.
GC_2_n
All or none of the data attributes belonging to the same group (n) shall be present for a
given instance of DataObject / SubDataObject.
GC_1_EXCL
At most one of the data objects shall be present for a given instance.
GC_CON_attr
A configuration data attribute shall only be present, if the (optional) specific data attribute
(attr) to which this configuration relates is also present.
GC_2_XOR_n
All or none of a group (n) shall be present. Groups are exclusive, but one group shall be
present.
AC_LN0_M
The attribute shall be present if the DataObject NamPlt belongs to LLN0; otherwise it may
be optional.
AC_LN0_EX
The attribute shall be present only if the DataObject NamPlt belongs to LLN0 (applies to
ldNs in CDC LPL only).
AC_DLD_M
The attribute shall be present, if LN name space of this LN deviates from the LN name
space referenced by ldNs of the logical device in which this LN is contained (applies to
lnNs in CDC LPL only).
AC_DLN_M
The attribute shall be present, if the data name space of this data deviates from the data
name space referenced by either lnNs of the logical node in which the data is contained or,
if there is no lnNs, ldNs of the logical device in which the data is contained (applies to
dataNs in all CDCs only).
AC_DLNDA_M
The attribute shall be present, if CDC name space of this data deviates from the CDC
--- Page 13 ---
61850-7-3 Ó IEC:2010
Abbreviation
Condition
name space referenced by either the dataNs of the data, the lnNs of the logical node in
which the data is defined or ldNs of the logical device in which the data is contained
(applies to cdcNs and cdcName in all CDCs only).
AC_SCAV
The presence of the configuration data attribute depends on the presence of i and f of the
Analog Value of the data attribute to which this configuration attribute relates. For a given
data object, that attribute
shall be present, if both i and f are present,
shall be optional if only i is present, and
is not required if only f is present.
NOTE If only i is present in a device without floating point capabilities, the configuration
parameter may be exchanged offline.
AC_ST
The attribute is mandatory, if the controllable status class supports status information.
AC_CO_O
If the controllable status class supports control, this attribute is available and an optional
attribute.
AC_CO_SBO
If the controllable status class supports control and if the control model supports the
values "sbo-with-normal-security" or "sbo-with-enhanced-security" or both, that attribute
shall be mandatory.
AC_SG_M
The attribute is mandatory, if this data shall be member of a setting group.
AC_SG_O
The attribute is optional, if this data shall be member of a setting group.
AC_SG_C1
One of the attributes is mandatory, if this data shall be member of a setting group.
AC_NSG_M
The attribute is mandatory, if this data shall be a setting outside a setting group.
AC_NSG_O
The attribute is optional, if this data shall be a setting outside a setting group.
AC_NSG_C1
One of the attributes is mandatory, if this data shall be a setting outside a setting group.
AC_RMS_M
The attribute is mandatory when the harmonics reference type is rms.
AC_CLC_O
The attribute shall be optional, when the calculation type (according to data ClcMth) for
this LN is Peak fundamental or RMS fundamental. The attribute shall not be available, if
ClcMth is TRUE RMS.
Constructed attribute classes
6.1
General
Constructed attribute classes are defined for the use in common data classes (CDC) in
Clause 7.
IEC 61850-7-1 provides an overview of all IEC 61850-7 documents (IEC 61850-7-2,
IEC 61850-7-3, and IEC 61850-7-4). IEC 61850-7-1 also describes the basic notation used in
IEC 61850-7-3 and the description of the relations between the IEC 61850-7 documents.
NOTE The common ACSI type "TimeStamp" is specified in IEC 61850-7-2.
6.2
Quality
6.2.1
Overview
Quality type shall be as defined in Table 2.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -11-
name space referenced by either the dataNs of the data, the InNs of the logical node in
which the data is defined or IdNs of the logical device in which the data is contained
(applies to cdcNs and cdcName in all CDCs only).
AC_SCAV The presence of the configuration data attribute depends on the presence of i and f of the
Analog Value of the data attribute to which this configuration attribute relates. For a given
data object, that attribute
1) shall be present, if both i and f are present,
2) shall be optional if only ‘is present, and
3) _ is not required if only fis present
NOTE. If only jis present in a device without floating point capabilities, the configuration
parameter may be exchanged offline.
AC_ST The attribute is mandatory, if the controllable status class supports status information
AC_CO_O If the controllable status class supports control, this attribute is available and an optional
attribute.
AC_CO_SBO If the controllable status class supports control and if the control model supports the
values "sbo-with-normal-security” or "sbo-with-enhanced-security" or both, that attribute
shall be mandatory.
AC_SG_M The attribute is mandatory, if this data shall be member of a setting group.
AC_SG_O The attribute is optional, if this data shall be member of a setting group.
AC_SG_C1 One of the attributes is mandatory, if this data shall be member of a setting group.
AC_NSG_M The attribute is mandatory, if this data shall be a setting outside a setting group.
AC_NSG_O The attribute is optional, if this data shall be a setting outside a setting group.
AC_NSG_C1 One of the attributes is mandatory, if this data shall be a setting outside a setting group.
AC_RMS_M The attribute is mandatory when the harmonics reference type is rms.
AC_CLC_O The attribute shall be optional, when the calculation type (according to data CleMth) for
this LN is Peak fundamental or RMS fundamental. The attribute shall not be available, if
CleMth is TRUE RMS.
6 Constructed attribute classes
6.1 General
Constructed attribute classes are defined for the use in common data classes (CDC) in
Clause 7.
IEC 61850-7-1 provides an overview of all IEC 61850-7 documents (IEC 61850-7-2,
IEC 61850-7-3, and IEC 61850-7-4). IEC 61850-7-1 also describes the basic notation used in
IEC 61850-7-3 and the description of the relations between the IEC 61850-7 documents.
NOTE The common ACSI type "TimeStamp" is specified in IEC 61850-7-2.
6.2 Quality
6.2.1 Overview
Quality type shall be as defined in Table 2.
--- Page 14 ---
61850-7-3 Ó IEC:2010
Table 2 – Quality
Quality type definition
Attribute name
Attribute type
Value/Value range
M/O/C
PACKED LIST
validity
CODED ENUM
good | invalid | reserved | questionable
detailQual
PACKED LIST
overflow
BOOLEAN
DEFAULT FALSE
outOfRange
BOOLEAN
DEFAULT FALSE
badReference
BOOLEAN
DEFAULT FALSE
oscillatory
BOOLEAN
DEFAULT FALSE
failure
BOOLEAN
DEFAULT FALSE
oldData
BOOLEAN
DEFAULT FALSE
inconsistent
BOOLEAN
DEFAULT FALSE
inaccurate
BOOLEAN
DEFAULT FALSE
source
CODED ENUM
process | substituted
DEFAULT process
test
BOOLEAN
DEFAULT FALSE
operatorBlocked
BOOLEAN
DEFAULT FALSE
The DEFAULT value shall be applied, if the functionality of the related attribute is not
supported. The mapping may specify to exclude the attribute from the message, if it is not
supported or if the DEFAULT value applies.
Quality shall be an attribute that contains information on the quality of the information from
the server. Quality of the data is also related to the mode of a logical node. Further details
can be found in IEC 61850-7-4. The different quality identifiers are not independent. Basically,
there are the following quality identifiers:
validity;
detail quality;
source;
test;
frozen by operator.
6.2.2
Validity
Validity shall be good, questionable or invalid.
good: The value shall be marked good if no abnormal condition of the acquisition function or
the information source is detected.
invalid: The value shall be marked invalid when an abnormal condition of the acquisition
function or the information source (missing or non-operating updating devices) is detected.
The value shall not be defined under this condition. The mark invalid shall be used to indicate
to the client that the value may be incorrect and shall not be used.
EXAMPLE If an input unit detects an oscillation of one input, it will mark the related information as invalid.
questionable: The value shall be marked questionable if a supervision function detects an
abnormal behaviour, however the value could still be valid. The client shall be responsible for
determining whether or not values marked "questionable" should be used.
--- OCR Supplementary Text ---
-12- 61850-7-3 © IEC:2010
Table 2 - Quality
WOT
foo fPackepuist PO
[vality | CODED ENUM | good [invalid [reserved [questionable |__|
[detaiiqual PACKeDList fT
[overflow [BOOLEAN [DEFAULT FALSE TM
[outorange [BOOLEAN | DEFAULT FALSE ——~—SSSCSC i
[badReference [BOOLEAN [DEFAULTFALSE TM
[oscilatory [BOOLEAN | DEFAULT FALSE SSS |
[failure [BOOLEAN [DEFAULTFALSE TM
[oldData_ [BOOLEAN [DEFAULTFALSE TM
[inconsistent [BOOLEAN ____[DEFAULTFALSE_———SCSC~sSSCi id
[inaccurate [BOOLEAN [DEFAULTFALSE TM
source CODED ENUM process | substituted
DEFAULT process
list |[BooLEANJoerauttrase |
[operatorBlocked [BOOLEAN [DEFAULTFALSE | |
The DEFAULT value shall be applied, if the functionality of the related attribute is not
supported. The mapping may specify to exclude the attribute from the message, if it is not
supported or if the DEFAULT value applies.
Quality shall be an attribute that contains information on the quality of the information from
the server. Quality of the data is also related to the mode of a logical node. Further details
can be found in IEC 61850-7-4. The different quality identifiers are not independent. Basically,
there are the following quality identifiers:
e validity;
* detail quality;
* source;
e test;
e frozen by operator.
6.2.2 Validity
Validity shall be good, questionable or invalid.
good: The value shall be marked good if no abnormal condition of the acquisition function or
the information source is detected.
invalid: The value shall be marked invalid when an abnormal condition of the acquisition
function or the information source (missing or non-operating updating devices) is detected.
The value shall not be defined under this condition. The mark invalid shall be used to indicate
to the client that the value may be incorrect and shall not be used.
EXAMPLE If an input unit detects an oscillation of one input, it will mark the related information as invalid
questionable: The value shall be marked questionable if a supervision function detects an
abnormal behaviour, however the value could still be valid. The client shall be responsible for
determining whether or not values marked "questionable" should be used.
--- Page 15 ---
61850-7-3 Ó IEC:2010
6.2.3
Detail quality
The reason for an invalid or questionable value of an attribute may be specified in more detail
with further quality identifiers. If one of these identifiers is set then validity shall be set to
invalid or questionable. Table 3 shows the relation of the detailed quality identifiers with
invalid or questionable quality.
Table 3 – Relation of the detailed quality identifiers with invalid or questionable quality
detailQual
invalid
questionable
overflow
outOfRange
badReference
oscillatory
failure
OldData
inconsistent
inaccurate
overflow: this identifier shall indicate a quality issue that the value of the attribute to which the
quality has been associated is beyond the capability of being represented properly (used for
measurand information only).
EXAMPLE A measured value may exceed the range that may be represented by the selected data type, for
example the data type is a 16-bit unsigned integer and the value exceeds 65 535.
outOfRange: this identifier shall indicate a quality issue that the attribute to which the quality
has been associated is beyond a predefined range of values. The server shall decide if
validity shall be set to invalid or questionable (used for measurand information only).
EXAMPLE A measured value may exceed a predefined range, however the selected data type can still represent
the value, for example the data type is a 16-bit unsigned integer, the predefined range is 0 to 40 000, if the value is
between 40 001 and 65 535 it is considered to be out of range.
badReference: this identifier shall indicate that the value may not be a correct value due to a
reference being out of calibration. The server shall decide if validity shall be set to invalid or
questionable (used for measurand information and binary counter information only).
oscillatory: to prevent overloading of event driven communication channels, it is desirable to
detect and suppress oscillating (fast changing) binary inputs. If a signal changes in a defined
time (tosc) twice in the same direction (from 0 to 1 or from 1 to 0) then it shall be defined as an
oscillation and the detail quality identifier “oscillatory” shall be set. If a configured number of
transient changes is detected, they shall be suppressed. In this time, the validity status
"questionable” shall be set. If the signal is still in the oscillating state after the defined number
of changes, the value shall be left in the state it was in when the oscillatory flag was set. In
this case, the validity status "questionable” shall be reset and “invalid” shall be set as long as
the signal is oscillating. If the configuration is such that all transient changes should be
suppressed, the validity status “invalid” shall be set immediately in addition to the detail
quality identifier “oscillatory” (used for status information only).
failure: this identifier shall indicate that a supervision function has detected an internal or
external failure.
--- Page 16 ---
61850-7-3 Ó IEC:2010
oldData: a value shall be oldData if an update is not made during a specific time interval. The
value may be an old value that may have changed in the meantime. This specific time interval
may be defined by an allowed-age attribute.
NOTE "Fail silent" errors, where the equipment stops sending data, will cause an oldData condition. In this case,
the last received information was correct.
inconsistent: this identifier shall indicate that an evaluation function has detected an
inconsistency.
inaccurate: this identifier shall indicate that the value does not meet the stated accuracy of
the source.
EXAMPLE The measured value of power factor may be noisy (inaccurate) when the current is very small.
6.2.4
Source
Source shall give information related to the origin of a value. The value may be acquired from
the process or be a substituted value.
process: the value is provided by an input function from the process I/O or is calculated from
some application function.
substituted: the value is provided by input of an operator or by an automatic source.
NOTE 1 Substitution may be done locally or via the communication services. In the second case, specific
attributes with a FC SV are used.
NOTE 2 There are various means to clear a substitution. As an example, a substitution that was done following an
invalid condition may be cleared automatically if the invalid condition is cleared. However, this is a local issue and
therefore not within the scope of this standard.
6.2.5
Test
Test shall be an additional identifier that may be used to classify a value being a test value
and not to be used for operational purposes. The processing of the test quality in the client
shall be as described in IEC 61850-7-4. The bit shall be completely independent from the
other bits within the quality descriptor.
6.2.6
Frozen by operator
operatorBlocked: this identifier shall be set if further update of the value has been blocked by
an operator. The value shall be the information that was acquired before blocking. If this
identifier is set, then the identifier oldData of detailQual shall also be set.
The operator shall use the data attribute blkEna to block the update of the value.
NOTE Both an operator as well as an automatic function may freeze communication updating as well as input
updating. In both cases, detailQual.oldData will be set. If the blocking is done by an operator, then the identifier
operatorBlocked is set additionally. In that case, an operator activity is required to clear the condition.
EXAMPLE An operator may freeze the update of an input, to save the old value before the auxiliary supply is
switched off.
--- Page 17 ---
61850-7-3 Ó IEC:2010
6.2.7
Quality in the client server context
Communication
network
Client
Server
Input
unit
Information
source
Invalid /
questionable
overFlow
outOfRange
badReference
oscillatory
failure
Questionable
oldData
Substituted
IEC   808/03
Figure 1 – Quality identifiers in a single client-server relationship
The quality identifier shall reflect the quality of the information in the server, as it is supplied
to the client. Figure 1 shows potential sources that may influence the quality in a single
client-server relationship. "Information source" is the (hardwired) connection of the process
information to the system. The information may be invalid or questionable as indicated in
Figure 1. Further abnormal behaviour of the information source may be detected by the input
unit. In that case, the input unit may keep the old data and flag it accordingly.
In a multiple client-server relationship, as shown in Figure 2, information may be acquired
over a communication link (with client B). If that communication link is broken, client B will
detect that error situation and qualify the information as questionable/old data.
Communication
network
Client A
Server A
Input
unit
Information
source
Questionable
oldData
Client B
Communication
network
IEC   809/03
Figure 2 – Quality identifiers in a multiple client-server relationship
In the multiple client-server relationship, the quality of the data received from server A shall
reflect both the quality of the server B (acquired with client B) as well as its own quality.
Therefore, handling of prioritisation of quality from different levels may require further
specification beyond that included in this standard. For the identifier validity, the value invalid
shall dominate over the value questionable, since this is the worst case. For the identifier
source, the higher level of the multiple client-server relationship shall dominate over the lower
level.
EXAMPLE Let A be the higher level and B the lower level. The quality from server B is invalid. If now the
communication fails (questionable, oldData) between server B and client B, the quality will remain invalid and not
become questionable, since the last information was not correct. Server A therefore will report the information as
invalid.
--- Page 18 ---
61850-7-3 Ó IEC:2010
6.2.8
Relation between quality identifiers
Validity and source have a prioritized relation. If source is in the “process” state, then validity
shall determine the quality of the origin value. If source is in the “substitute” state, then
validity shall be overruled by the definition of the substituted value. This is an important
feature, since substitution is used to replace invalid values with substituted values that may
be used by the client such as good values.
EXAMPLE 1 If both questionable and substituted are set, this means that the substituted value is questionable.
This may happen if, in a hierarchical configuration, a substitution is performed at the lowest level and the
communication fails on a higher level.
EXAMPLE 2 If an invalid value is substituted, the invalid field will be cleared and the substituted field will be set
to indicate the substitution.
The quality identifier operatorBlocked is independent of the other quality identifiers.
EXAMPLE 3 An oscillating input may cause the invalid field to be set. Due to the continuing changes in the value,
many reports are generated, loading the communication network. An operator may block the update of the input. In
this case, the field operatorBlocked will also be set.
An example for the interaction between the quality identifiers and the impact of multiple
client-server relation is shown in Figure 3. In this example, it is assumed that a bay level
device acts as a client of the process level server and as a server to the station level client.
NOTE This is one example of a multiple client-server relationship; other multiple client-server relationships may
exist, but the behaviour will not change.
In case A, the input is blocked, the quality of the information is marked as questionable and
oldData.
In case B, a substitution is done at process level. Now, the quality of the information to the
next higher level (the bay level) is marked as substituted (but good).
In case C, the communication between process and bay level fails. Between bay level
and station level, the information is still marked as substituted. In addition, questionable and
oldData is set to indicate that the (substituted) information may be old.
In case D, a new substitution is made at bay level. Now the quality of the information to the
next higher level is marked as substituted (and good) and is independent from the first
substitution.
--- Page 19 ---
61850-7-3 Ó IEC:2010
Input is blocked
Validity = quest
(oldData)
Validity = quest
(oldData)
Input is blocked
Substitution
Station
level
Bay
level
Process
level
Communication
failure
Substituted,
validity = quest
(oldData)
Input is blocked
Substitution
Communication
failure
Substituted
Substitution
Input is blocked
Substitution
Substituted
Substituted
Station
level
Bay
level
Process
level
Case A
Case B
Case C
Case D
IEC   2550/10
Key
CL  client
Se  server
Figure 3 – Interaction of substitution and validity
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -17-
Case A Case B
Station
Validity = quest Substituted
(oldData)
Bay <> eed
Validity = quest Substituted
(oldData)
Process Ge> se
DZ ZW
Input is blocked Input is blocked
Case C Case D
Station
ie >) 2)
Substituted, Substituted
validity = quest
(oldData)
Bay Css) CSe) Substitution
Communication Communication
failure failure
Process Ge) Ge)
level Ge) ‘Substitution Ge) Substitution
ZZ Z
Input is blocked Input is blocked
IEC 2550/10
Key
CL client
Se server
Figure 3 - Interaction of substitution and validity
--- Page 20 ---
61850-7-3 Ó IEC:2010
6.3
Analogue value
Analogue value type shall be as defined in Table 4.
Table 4 – Analogue value
AnalogueValue type definition
Attribute name
Attribute type
Value/Value range
M/O/C
INT32
integer value
GC_1
FLOAT32
floating point value
GC_1
Analogue values may be represented as a basic type INTEGER (attribute i) or as FLOATING
POINT (attribute f). At least one of the attributes shall be used. If both i and f exist, the
application in the server shall insure that both values remain consistent. The latest value set
by the communication service shall be used to update the other value. As an example, if xxx.f
is written, the application shall update xxx.i accordingly.
The measured values represent primary process values.
i: The value of i shall be an integer representation of the measured value. The formula to
convert between i and the process value (pVal) shall be:
offset
scaleFacto
pVal
It shall be true within acceptable error when i, scaleFactor, offset and f are all present.
f: The value of f shall be the floating point representation of the measured value. The formula
to convert between f and the process value shall be:
iplier
units.mult
pVal
NOTE The reason for both integer and floating point representation is so that IEDs without FLOATING POINT
capabilities are enabled to support analogue values. In this case, the scaleFactor and offset may be exchanged
offline between clients and servers.
6.4
Configuration of analogue value
Configuration of analogue value type shall be as defined in Table 5.
Table 5 – Configuration of analogue value
ScaledValueConfig type definition
Attribute name
Attribute type
Value/value range
M/O/C
scaleFactor
FLOAT32
offset
FLOAT32
This constructed attribute class shall be used to configure the INTEGER value representation
of the analogue value. The formula for conversion between integer and floating point value is
given in 6.3.
scaleFactor: the value of scaleFactor shall be the scaling factor.
--- Page 21 ---
61850-7-3 Ó IEC:2010
offset: the value of offset shall be the offset.
NOTE If a server does not support transmission of FLOAT32 values, the client may retrieve these values from the
SCL file.
6.5
Range configuration
Range configuration type is used to configure the limits that define the range of a measured
value and shall be as defined in Table 6.
Table 6 – Range configuration
RangeConfig type definition
Attribute name
Attribute type
Value/Value range
M/O/C
hhLim
AnalogueValue
hLim
AnalogueValue
lLim
AnalogueValue
llLim
AnalogueValue
min
AnalogueValue
max
AnalogueValue
limDb
INT32U
0 … 100 000
hhLim, hLim, lLim, llLim: These attributes shall be the configuration parameters used in the
context with the range attribute as defined in Clause 8.
min: the min (minimum) attribute shall represent the minimum process measurement for which
values of i or f are considered within process limits. If the value is lower, q shall be set
accordingly (validity = questionable, detailQual = outOfRange).
max: the max (maximum) attribute shall represent the maximum process measurement for
which values of i or f are considered within process limits. If the value is higher, q shall be set
accordingly (validity = questionable, detailQual = outOfRange).
limDb: The value is used to introduce a hysteresis in the calculation of range. Range is
immediately set to the higher value, when a high limit has been crossed (to the lower value,
when a low limit has been crossed). However, range is only set back to the lower value, when
the value of the high limit minus limDb has been crossed (to the higher value when the value
of the low limit plus limDb has been crossed). The value shall represent the percentage
between max and min in units of 0,001 %. If limDb is not present, no hysteresis calculation is
made.
6.6
Step position with transient indication
Step position with transient indication type is for example used to indicate the position of tap
changers and shall be as defined in Table 7.
Table 7 – Step position with transient indication
ValWithTrans type definition
Attribute name
Attribute type
Value/Value range
M/O/C
posVal
INT8
–64 … 63
transInd
BOOLEAN
--- Page 22 ---
61850-7-3 Ó IEC:2010
The posVal shall contain the step position, the transInd shall indicate that the equipment is in
a transient state.
6.7
Pulse configuration
Pulse configuration type is used to configure the output pulse generated with a command and
shall be as defined in Table 8.
Table 8 – Pulse configuration
PulseConfig type definition
Attribute name
Attribute type
Value/Value range
M/O/C
cmdQual
ENUMERATED
pulse | persistent
onDur
INT32U
offDur
INT32U
numPls
INT32U
cmdQual: this identifier shall define if the control output is a pulse output or if it is a persistent
output. If it is set to pulse, then the duration of the pulse shall be defined with the identifiers
onDur, offDur and numPls. If it is set to persistent, the output stays in the state indicated in
the operate service.
onDur, offDur, numPls: as the result of receiving an Operate service, a pulsed output may be
generated to the on or off input of a switching device. The shape of this output is defined by
onDur, offDur and numPls according to Figure 4. NumPls shall specify the number of pulses
that are generated. onDur shall specify the on duration of the pulse, offDur specifies the
duration between two pulses. onDur and offDur shall be specified in ms; a value of 0 ms shall
specify that the duration is locally defined.
onDur
offDur
numPls
IEC   811/03
Figure 4 – Configuration of command output pulse
6.8
Originator
Originator type shall be as defined in Table 9.
--- Page 23 ---
61850-7-3 Ó IEC:2010
Table 9 – Originator
Originator type definition
Attribute name
Attribute type
Value/Value range
M/O/C
orCat
ENUMERATED
not-supported | bay-control | station-control |
remote-control | automatic-bay | automatic-
station | automatic-remote | maintenance |
process
orIdent
OCTET STRING64
orCat: The originator category shall specify the category of the originator. An explanation of
the values for orCat is given in Table 10.
Table 10 – Values for orCat
Value
Explanation
not-supported
That value shall not be used
bay-control
Control operation issued from an operator using a client located at bay level
station-control
Control operation issued from an operator using a client located at station level
remote-control
Control operation from a remote operater outside the substation (for example network control
center)
automatic-bay
Control operation issued from an automatic function at bay level
automatic-station
Control operation issued from an automatic function at station level
automatic-remote
Control operation issued from a automatic function outside of the substation
maintenance
Control operation issued from a maintenance/service tool
process
Status change occurred without control action (for example external trip of a circuit breaker or
failure inside the breaker)
orIdent: the originator identification shall show the identification of the originator. The value of
NULL shall be reserved to indicate that the originator of a particular action is not known.
6.9
Unit definition
Unit type shall be as defined in Table 11.
Table 11 – Unit
Unit type definition
Attribute name
Attribute type
Value/Value range
M/O/C
SIUnit
ENUMERATED
According to Tables A.1 to A.4 in Annex A
multiplier
ENUMERATED
According to Table A.5 in Annex A
SIUnit: shall define the SI unit according to Annex A.
multiplier: shall define the multiplier value according to Annex A. The default value is 0 (i.e.
multiplier = 1).
6.10 Vector definition
Vector type shall be as defined in Table 12.
Table 12 – Vector
Vector type definition
Attribute name
Attribute type
Value/Value range
M/O/C
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -21-
Table 9 - Originator
Originator type definition
Attribute name Attribute type Value/Value range M/OIC
orCat ENUMERATED not-supported | bay-control | station-control |
remote-control | automatic-bay | automatic-
station | automatic-remote | maintenance |
process
orCat: The originator category shall specify the category of the originator. An explanation of
the values for orCat is given in Table 10.
Table 10 - Values for orCat
not-supported That value shall not be used
bay-control Control operation issued from an operator using a client located at bay level
station-control Control operation issued from an operator using a client located at station level
remote-control Control operation from a remote operater outside the substation (for example network control
center)
automatic-bay Control operation issued from an automatic function at bay level
automatic-station | Control operation issued from an automatic function at station level
process Status change occurred without control action (for example external trip of a circuit breaker or
failure inside the breaker)
orldent: the originator identification shall show the identification of the originator. The value of
NULL shall be reserved to indicate that the originator of a particular action is not known.
6.9 Unit definition
Unit type shall be as defined in Table 11.
Table 11 — Unit
Unit type definition
Attribute name Attribute type Value/Value range M/O/C
SlUnit ENUMERATED According to Tables A.1 to A.4 in Annex A [Mi
multiplier ENUMERATED According to Table A.5 in Annex A fo sd
SlUnit: shall define the SI unit according to Annex A.
multiplier: shall define the multiplier value according to Annex A. The default value is 0 (i.e.
multiplier = 1).
6.10 Vector definition
Vector type shall be as defined in Table 12.
Table 12 - Vector
Vector type definition
Attribute name Attribute type Value/Value range M/oIc
--- Page 24 ---
61850-7-3 Ó IEC:2010
Vector type definition
Attribute name
Attribute type
Value/Value range
M/O/C
mag
AnalogueValue
ang
AnalogueValue
–180 < n £ +180
AC_CLC_O
mag: the magnitude of the complex value.
ang: the angle of the complex value. The SIUnit shall be degrees and the unit multiplier is 1.
The angle reference is defined in the context where the Vector type is used.
6.11 Point definition
Point type shall be as defined in Table 13 and is used to represent points in a two- or three-
dimensional coordinates system.
Table 13 – Point
Point type definition
Attribute name
Attribute type
Value/Value range
M/O/C
xVal
FLOAT32
yVal
FLOAT32
zVal
FLOAT32
xVal: the x value of a point.
yVal: the y value of a point.
zVal: the z value of a point.
6.12 CtlModels definition
CtlModels type is defined as follows:
ENUMERATED (status-only | direct-with-normal-security | sbo-with-normal-security | direct-
with-enhanced-security | sbo-with-enhanced-security)
Details are provided in Clause 8.
6.13 SboClasses definition
SboClasses type is defined as follows:
ENUMERATED (operate-once | operate-many)
Details are provided in Clause 8.
6.14 Cell
Cell type is used to define a rectangle area in a two-dimensional environment and shall be
defined as in Table 14. Cell type can as well be used to describe a range within a one-
dimensional environment. For details, see Figure 5.
--- OCR Supplementary Text ---
-22- 61850-7-3 © IEC:2010
mag: the magnitude of the complex value.
ang: the angle of the complex value. The SIUnit shall be degrees and the unit multiplier is 1.
The angle reference is defined in the context where the Vector type is used.
6.11 Point definition
Point type shall be as defined in Table 13 and is used to represent points in a two- or three-
dimensional coordinates system.
Table 13 - Point
[wetted
[wid omrse iP SCiSCC*d
xVal: the x value of a point.
yVal: the y value of a point.
zVal: the z value of a point.
6.12 CtlModels definition
CtlModels type is defined as follows:
ENUMERATED (status-only | direct-with-normal-security | sbo-with-normal-security | direct-
with-enhanced-security | sbo-with-enhanced-security)
Details are provided in Clause 8.
6.13 SboClasses definition
SboClasses type is defined as follows:
ENUMERATED (operate-once | operate-many)
Details are provided in Clause 8.
6.14 Cell
Cell type is used to define a rectangle area in a two-dimensional environment and shall be
defined as in Table 14. Cell type can as well be used to describe a range within a one-
dimensional environment. For details, see Figure 5.
--- Page 25 ---
61850-7-3 Ó IEC:2010
Table 14 – Cell
Cell type definition
Attribute name
Attribute type
Value/Value range
M/O/C
xStart
FLOAT32
xEnd
FLOAT32
yStart
FLOAT32
yEnd
FLOAT32
xStart: the x value of the lower left corner of the square.
xEnd: the x value of the upper right corner of the square. That component shall not be present
to indicate infinity in the direction of the x axis.
yStart: The y value of the lower left corner of the square. That component shall not be
present, if only a one-dimensional range needs to be described.
yEnd: The y value of the upper right corner of the square. That component shall not be
present, if only a one-dimensional range needs to be described or to indicate infinity in the
direction of the y axis.
xEnd / yEnd
xStart / yStart
Figure 5 – Cell definition
6.15 CalendarTime definition
CalendarTime type is used to define a time setting in reference to the calendar and shall be
as defined in Table 15. That constructed attribute class allows the specification of times like
the last day of the month or the second Sunday in March at 03.00h.
IEC   2551/10
Customer:

It appears that you have provided a large block of text from the IEC 61850 standard, which describes the Common Data Classes (CDCs) for various types of measured values in electrical power systems.

To assist you better, could you please specify what you would like me to help with? For example:

* You want me to summarize the main points of each CDC?
* You need help understanding a specific aspect of one of the CDCs?
* You'd like me to extract specific information from the text?

Please let me know how I can assist you further.

ttribute
name
Leela 1-1 ean
IEC 61850-7-2)
Peewee eee [|
Vea | Vother | Aother | Synchrophasor
[a fvisipte strineass| oc | [tet
STRING255
[eden [VISIBLE STRINGZ55 [EX [|S ARENA
[edcName [VISIBLE STRING255| ex || SY ADD |
[datas |visiete strinc2ss | ex [| AC NM |
With regard to data attributes of the CDC CMV, the following additional specifications apply.
e The data attribute angRef of phsAB, phsBC and phsCA shall not be used. Instead, the
attribute angRef defined with the CDC DEL shall be used.
e The values of phsAB.t, phsBC.t and phsCA.t are identical. They specify the time at which
the values for phsAB, phsBC and phsCA have been simultaneously acquired or
determined.
--- Page 40 ---
61850-7-3 Ó IEC:2010
7.4.7
Sequence (SEQ)
Table 35 defines the common data class “sequence”. This class is a collection of sequence
components of a value.
Table 35 – Sequence
SEQ class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
SubDataObject
CMV
CMV
CMV
DataAttribute
measured attributes
seqT
ENUMERATED
pos-neg-zero | dir-quad-zero
configuration, description and extension
phsRef
ENUMERATED
dchg
A | B | C
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 29.
With regard to data attributes of the CDC CMV, the following additional specifications apply.
The values of c1.t, c2.t and c3.t are identical. They specify the time at which the values
for c1, c2 and c3 have been calculated.
--- OCR Supplementary Text ---
— 38- 61850-7-3 © IEC:2010
7.4.7 Sequence (SEQ)
Table 35 defines the common data class “sequence”. This class is a collection of sequence
components of a value.
Table 35 - Sequence
[SEQ cass
attribute
name
Lani aaa
IEC 61850-7-2)
fen CMV
fee CM
[seat [ENUMERATED [WX | | pos-neg-zeroldi-quadzero =< SCO —*d
[phsRet [ENUWERATED | CF [dng [AlBIG SCS SSCSC=*
[¢_fvisiBte strine2ss | oc [ [tet
ieee, Te PT
STRING255
[edeNs |visiBLe stRinGass | ex [fC DLN
[edeName [VISIBLE STRING255 |_ex || _———————SSSS——*S ALND AM |
[dataNs |visipte strinc2ss | ex [oO] AC LN
With regard to data attributes of the CDC CMV, the following additional specifications apply.
e The values of c1.t, c2.t and c3.t are identical. They specify the time at which the values
for c1, c2 and c3 have been calculated.
--- Page 41 ---
61850-7-3 Ó IEC:2010
7.4.8
Harmonic value (HMV)
Table 36 defines the common data class for non-phase-related harmonic values. This class is
a collection of values that represent the harmonic or interharmonic content of a process value.
Table 36 – Harmonic value
HMV class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
SubDataObject
har
ARRAY 0..numHar OF CMV
DataAttribute
configuration, description and extension
numHar
INT16U
dchg
numCyc
INT16U
dchg
evalTm
INT16U
dchg
smpRate
INT32U
dchg
frequency
FLOAT32
dchg
nominal frequency
hvRef
ENUMERATED
dchg
fundamental | rms | absolute
rmsCyc
INT16U
dchg
AC_RMS_M
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 29.
NOTE Harmonics for a single circuit have phase angles (optional) but need no reference for the angle (angRef),
since by convention, the reference is always the fundamental frequency (index 1).
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 - 39-
7.4.8 Harmonic value (HMV)
Table 36 defines the common data class for non-phase-related harmonic values. This class is
a collection of values that represent the harmonic or interharmonic content of a process value.
Table 36 — Harmonic value
[MV cs
attribute
name
So Eee |
IEC 61850-7-2)
[har ARRAY O.numHarOF CMV
[numHar Jintisu TC acho Joo
[numGye_fiNTIU———~| oF [dng [> —SSSCSCSCSC~Ss SS id
[evaitm fiNTIU | oF [dng [| SCS Sd
[smpRate[wTs2u | cr [ang | SSCS SY
[frequency [FLOAT#2 | CF _[dehg [nominal Wequeney _——~—S~S~SSCiC*
[hvRef_ [ENUMERATED [CF [dchg fundamental |rms| absolute |
[rmscyefwTieu | cF [ang | SSSS*d CARS|
[¢_fvisiBte strineass | pc | [tet
fees PP
STRING255
[edeNs |visiBLe stRiNG2ss | ex [| AC DLA
[edcName | VISIBLE STRING255 | ex |_| __——————SCS~S AC _D ENA |
[datas |visipte strinc2ss | ex [| AC NM |
NOTE Harmonics for a single circuit have phase angles (optional) but need no reference for the angle (angRef),
since by convention, the reference is always the fundamental frequency (index 1).
--- Page 42 ---
61850-7-3 Ó IEC:2010
7.4.9
Harmonic value for WYE (HWYE)
Table 37 defines the common data class “harmonic value for WYE”. This class is a collection
of simultaneous measurements (or evaluations) of values that represent the harmonic or
interharmonic content of a process value in a three-phase system with phase to ground
values.
Table 37 – Harmonic values for WYE
HWYE class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
SubDataObject
phsAHar
ARRAY 0..numHar OF CMV
phsBHar
ARRAY 0..numHar OF CMV
phsCHar
ARRAY 0..numHar OF CMV
neutHar
ARRAY 0..numHar OF CMV
netHar
ARRAY 0..numHar OF CMV
resHar
ARRAY 0..numHar OF CMV
DataAttribute
configuration, description and extension
numHar
INT16U
dchg
numCyc
INT16U
dchg
evalTm
INT16U
dchg
angRef
ENUMERATED
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc |
Vca | Vother | Aother | Synchrophasor
smpRate
INT32U
dchg
frequency
FLOAT32
dchg
fundamental frequency
hvRef
ENUMERATED
dchg
fundamental | rms | absolute
rmsCyc
INT16U
dchg
AC_RMS_M
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 29.
--- OCR Supplementary Text ---
-40- 61850-7-3 © IEC:2010
7.4.9 Harmonic value for WYE (HWYE)
Table 37 defines the common data class “harmonic value for WYE”. This class is a collection
of simultaneous measurements (or evaluations) of values that represent the harmonic or
interharmonic content of a process value in a three-phase system with phase to ground
values.
Table 37 — Harmonic values for WYE
attribute
name
So ee |
IEC 61850-7-2)
[phsAHar [ARRAY O..numHarOF CMV TM
[pnsBHar [ARRAY .numHar OF CMS
[phscHar [ARRAY 0.numHar OF CMV SSCS
[neutHar [ARRAY O.numHarOF CMV
[netHar [ARRAY 0. numHar OF CMS
[resHar__ [ARRAY O.numHarOF CMV
[numHar fintisu Tc acho foo
[pumcyefwTieu | CF |aeng[>0. SSS
fevartm fintisu_— fc [achg JO
per emer i Rae S|
Vea | Vother | Aother | Synchrophasor
[smpRate[wTs2U——*d; F feehe | SSCSC~C~“~iSSC“‘“ SSC*d
[frequency [FLOAT#2 | CF [deng [fundamental Vequeney —~—S—S~i| SSC id
[nvRef____ [ENUMERATED | CF [dchg [fundamental |rms |absouute +o |
[rmscyefwrteu | cr [ang | SSSS*d CARS|
[¢_ ss fvisiBte strine2ss | pc | [tet Tt
i A
STRING255
[edeNs |visiBte strinGass | ex [fC DLA
[edcName |visiBLe stRING2ss | ex [| AC _DLNDA
[datas [VISIBLE STRING255 | ex [|S ACU
--- Page 43 ---
61850-7-3 Ó IEC:2010
7.4.10
Harmonic value for DEL (HDEL)
Table 38 defines the common data class “harmonic value for delta”. This class is a collection
of simultaneous measurements (or evaluations) of values that represent the harmonic or
interharmonic content of a process value in a three-phase system with phase to phase values.
Table 38 – Harmonic values for delta
HDEL class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
SubDataObject
phsABHar
ARRAY 0..numHar OF CMV
phsBCHar
ARRAY 0..numHar OF CMV
phsCAHar
ARRAY 0..numHar OF CMV
DataAttribute
configuration, description and extension
numHar
INT16U
dchg
numCyc
INT16U
dchg
evalTm
INT16U
dchg
angRef
ENUMERATED
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc |
Vca | Vother | Aother | Synchrophasor
smpRate
INT32U
dchg
frequency
FLOAT32
dchg
nominal frequency
hvRef
ENUMERATED
dchg
fundamental | rms | absolute
rmsCyc
INT16U
dchg
AC_RMS_M
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 29.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -41-
7.4.10 Harmonic value for DEL (HDEL)
Table 38 defines the common data class “harmonic value for delta”. This class is a collection
of simultaneous measurements (or evaluations) of values that represent the harmonic or
interharmonic content of a process value in a three-phase system with phase to phase values.
Table 38 —- Harmonic values for delta
attribute
name
hana =r ean
IEC 61850-7-2)
[phsABHar [ARRAY O..numHarOF CMV
[phsBCHar [ARRAY O.numHarOF CMV
[phsCAHar [ARRAY 0.numHar OF CMV SSS
[number INTIS ———SC*dYC Sse [50 SSCSCSC~C~“—SCS~—~—SC‘“a
[pumcyefwrieu | CF |aeng[>0 SSS
fevartm fintisu_— fc [dchg fT
fare [ethene [oo SreLTRIe eee [|
Vea | Vother | Aother | Synchrophasor
[smpRate [int32u_ [cr [dchg [OT
[frequency | FLOATS2 | CF [dchg [nominal frequency TM
[nvRef____ [ENUMERATED | CF _[deng [fundamental |rms Tabsoute [| __o _|
[rmsCyc Jintasu TC [achg [OAC RS
[o_*|visiwLE STRINGS [po | |Tx« —~—SCS;7«73 }>PC«iPSCi |
a a
STRING255
[edeNs __|visipte strincass | ex [| AC_DLNDA_
[edcName |visiBLe stRING2ss | ex [| AC _DLNDA_M
[ates [VISIBLE STRING255 | ex | |S ALN
--- Page 44 ---
61850-7-3 Ó IEC:2010
7.5
Common data class specifications for controls
7.5.1
Application of services
Table 39 defines the basic controllable status information template. In particular, it defines the
inheritance and specialization of services defined in IEC 61850-7-2.
Table 39 – Basic controllable status information template
Basic controllable status information template
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status / measured attributes and control mirror
substitution and blocked
configuration, description and extension
parameters for control services
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes
with a functional constraint as specified below.
Service model of
IEC 61850-7-2
Service
Service
applies to Attr
with FC
Remark
GenCommonDataClass
model
SetDataValues
GetDataValues
GetDataDefinition
GetDataDirectory
DC, CF, SV, BL
ALL
ALL
ALL
Data set model
GetDataSetValues
SetDataSetValues
ALL
DC, CF, SV, BL
Reporting model
GSE model
Sampled values model
Report
SendGOOSEMessage
SendGSSEMessage
SendMSVMessage
SendUSVMessage
ALL
ST, MX
ST, MX
ST, MX
As specified within the data set that is used
to define the content of the message
Control model
Select
SelectWithValue
Cancel
Operate
CommandTermination
TimeActivatedOperate
All common data classes for controllable status information include both the control and the
related status information.
NOTE The service parameter of the control, which belongs to the control model defined in IEC 61850-7-2, is
included here, since the type is defined by the CDC.
--- OCR Supplementary Text ---
-42- 61850-7-3 © IEC:2010
7.5 Common data class specifications for controls
7.5.1 Application of services
Table 39 defines the basic controllable status information template. In particular, it defines the
inheritance and specialization of services defined in IEC 61850-7-2.
Table 39 - Basic controllable status information template
Basic controllable status information template
Data Type TrgOp Value/Value range MIOIC
attribute
name
DataName _| Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
Status / measured attributes and control mirror
substitution and blocked
configuration, description and extension
parameters for control services
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes
with a functional constraint as specified below.
Service model of Service Service Remark
IEC 61850-7-2 applies to Attr
with FC
GenCommonDataClass | SetDataValues DC, CF, SV, BL
model GetDataValues ALL
GetDataDefinition ALL
GetDataDirectory ALL
Data set model GetDataSetValues ALL
SetDataSetValues DC, CF, SV, BL
Reporting model Report ALL As specified within the data set that is used
GSE model SendGOOSEMessage ST, MX to define the content of the message
SendGSSEMessage ST
Sampled values model | SendMSVMessage ST, MX
SendUSVMessage ST, MX
Control model Select
SelectWithValue
Cancel
Operate
CommandTermination
TimeActivatedOperate
All common data classes for controllable status information include both the control and the
related status information.
NOTE The service parameter of the control, which belongs to the control model defined in IEC 61850-7-2, is
included here, since the type is defined by the CDC.
--- Page 45 ---
61850-7-3 Ó IEC:2010
7.5.2
Controllable single point (SPC)
Table 40 defines the common data class “controllable single point”.
Table 40 – Controllable single point
SPC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
stVal
BOOLEAN
dchg
FALSE | TRUE
AC_ST
Quality
qchg
AC_ST
TimeStamp
AC_ST
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
BOOLEAN
FALSE | TRUE
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
pulseConfig
PulseConfig
dchg
AC_CO_O
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
BOOLEAN
off (FALSE) | on (TRUE)
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -43-
7.5.2 Controllable single point (SPC)
Table 40 defines the common data class “controllable single point”.
Table 40 - Controllable single point
[SPC chs
attribute
name
a ||
IEC 61850-7-2)
[eign [Orginator iT st [| SSCS O_O
[ottvum fiwtau_ —————~([st|[o.as5 SCS A
[a fowalty ist fachg | SSSSSSCS~SdC ag
[t___ Timestamp fst Pst
[seid [BootcaN | st |gehg | SCS SCY
fopRevd [BOOLEAN [OR dchg |
fopok _fBooLeAN, [OR dchg PO
[topok [Timestamp [or |_| —SSSCSC~ir iY
[subEna [BOOLEAN | sv (| | SSSSCSCSCSCSCSCSCTC~*d*SC#PCS_SUBST_|
[subval_ [BOOLEAN | _sv_|_|FALSEITRUE————_—|-PICS_suBST |
[subQ [Quality sv PT rics _suest |
[subiD__|visieE stRINGSa [sv [|S cs |
[bikEna [pooteaN, | eT
[puiseGonfig [PulseGonig | CF [acho | SSCS C_O_
[ctiModel | CtModels_ [CR dong PO
[sboTimeout_finTs2u | GF [ach | SCS AC COO
[sboclass _[SboClasses | CF [acho | ——~—SCSCSCSSSS AC
[opertimesut wrs2u | cr |écng | —————SCSCSCSCSSS ACO |
[s____*|visiBESTRING?Z5 [0c | _[Txt —~—SCS<;7«;7 3 SPECirPSCtC~i |
[ewe fe
STRING255
[edeNs |visiBte strinGass [ex [AC DLA |
[edcName |visiBLe stRING2ss [ex [AC _DLNDAM |
[ateNs [VISIBLE STRING255 |_Ex |_| SSAC NM
--- Page 46 ---
61850-7-3 Ó IEC:2010
7.5.3
Controllable double point (DPC)
Table 41 defines the common data class “controllable double point”.
Table 41 – Controllable double point
DPC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
stVal
CODED ENUM
dchg
intermediate-state | off | on | bad-state
Quality
qchg
TimeStamp
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
CODED ENUM
intermediate-state | off | on | bad-state
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
pulseConfig
PulseConfig
dchg
AC_CO_O
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
BOOLEAN
off (FALSE) | on (TRUE)
The value bad-state means that the server cannot detect if the position is open, close or in
intermediate state.
--- OCR Supplementary Text ---
-44- 61850-7-3 © IEC:2010
7.5.3 Controllable double point (DPC)
Table 41 defines the common data class “controllable double point”.
Table 41 - Controllable double point
[DPC class
attribute
name
a ||
IEC 61850-7-2)
[eign [Orginator iT st [| SSCS O_O
[ottvum fiwtau [st | [0.285 SCS A |
[stval_ [CODED ENUM [ST __[dchg [intermediate-state | off on |bad-state [M_ |
[a foway ST acne |
[t___ | Timestamp fst Pm
[seid [BOoLeAN | st |gehg | SCS OY
fopRevd [BOOLEAN [OR dchg |
fopok _fBooLeAN, [OR dchg PO
[topok [Timestamp [or |_| —SSSCSC~ir iY
[subEna [BOOLEAN —*+[ sv ~(| | SSSSCSCSCSCSCSCC~*Y;COPCS_SUBST_|
[subval__ [CODED ENUM | _SV__| | intermediate-stato | off on [bad-state_| PICS_SUBST_|
[subQ [Quality sv PT rics _suest |
[subiD__|visieE stRINGSa [sv [|S cs |
[bikEna [pooteaN, | eT
[puiseGonfig [PulseGonig | CF [acho | SSCS C_O_
[ctiModel | CtModels_ [CR dong PO
[sboTimeout_finTs2u | GF [ach | SCS AC COO
[sboclass _[SboClasses | CF [acho | ——~—SCSCSCSSSS AC
[opertimesut wrs2u | cr |écng | —————SCSCSCSCSSS ACO |
[s____*|visiBESTRING?Z5 [0c | _[Txt —~—SCS<;7«;7 3 SPECirPSCtC~i |
[ewe fe
STRING255
[edeNs |visiBte strinGass [ex [AC DLA |
[edcName |visiBLe stRING2ss [ex [AC _DLNDAM |
[ateNs [VISIBLE STRING255 |_Ex |_| SSAC NM
The value bad-state means that the server cannot detect if the position is open, close or in
intermediate state.
--- Page 47 ---
61850-7-3 Ó IEC:2010
7.5.4
Controllable integer status (INC)
Table 42 defines the common data class “controllable integer status”.
Table 42 – Controllable integer status
INC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
stVal
INT32
dchg
Quality
qchg
TimeStamp
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
INT32
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
minVal
INT32
dchg
maxVal
INT32
dchg
stepSize
INT32U
dchg
1 … (maxVal – minVal)
operTimeout
INT32U
dchg
AC_CO_O
units
Unit
dchg
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
INT32
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -45-
7.5.4 Controllable integer status (INC)
Table 42 defines the common data class “controllable integer status”.
Table 42 — Controllable integer status
attribute
name
a ||
IEC 61850-7-2)
[eign [Orginator iT st [| SSCS O_O
[ottvum fiwtau ~~ st | ‘eae SSSCSC~SCS~S A
[stval fint32 Tt dong PO
[a foualty [st facng | SSS
[t___ | Timestamp fst Pm
[seid [BOoLeAN | st |gehg | SCS OY
fopRevd [BOOLEAN [OR dchg |
fopok _fBooLeAN, [OR dchg PO
[topok [Timestamp [or |_| —SSSCSC~ir iY
[subEna [BOOLEAN —*+[ sv | | SSSSCSCSCSCSCSCSCSTC“*déSC}PCS_SUBST_|
[subval_fints2———S«dT sv || SSCS SUBST |
[sua [Quality sv PT rics _suest |
[subiD__|visieE stRINGSa [sv [|S cs |
[bikEna [pooteaN, | eT
[eitode! [Ctodeis | Cr [acho | SSCS SS id
[sboTimeout JiNT32U [CR dohg PO COC
[sbociass _[SboCiasses | CF [ach | —~—SCSCSCSCSSS AC |
[inva fiNTs2_————~| cr [ach | SCS SSC *d
[manval | wTs2——~—*d; Ser eon | SSC SY
[stepsize_[INTs2U____[ GF |dcho [1 (manVal- minva) ——SCSCS~sCSC~SSC*
[operimeout [inT32u [CR dchg [OC COC
[units funk_——————*d cr faorg | SSCS
[¢_fvisiBte strine2ss | pc | [text
Be |stnesss | P|
STRING255
[edeNs |visiBte strinGass [ex [PAC DLN |
[edcName | VISIBLE STRING255| ex |_| _—————SCS ACD |
[dates [VISIBLE STRING255 [ex [|S CAC NM
[etal ng
--- Page 48 ---
61850-7-3 Ó IEC:2010
7.5.5
Controllable enumerated status (ENC)
Table 43 defines the common data class “controllable enumerated status”.
Table 43 – Controllable enumerated status
ENC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
stVal
ENUMERATED
dchg
Quality
qchg
TimeStamp
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
ENUMERATED
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
ENUMERATED
--- OCR Supplementary Text ---
- 46 - 61850-7-3 © IEC:2010
7.5.5 Controllable enumerated status (ENC)
Table 43 defines the common data class “controllable enumerated status”.
Table 43 —- Controllable enumerated status
[ENC class
attribute
name
a ||
IEC 61850-7-2)
[eign [Orginator iT st [| SSCS O_O
[ottvum fiwtau ————~i| st | [e285 SCS A
[stval [ENUMERATED [st [dchg [OT
[a fowalty st fachg | SSS
[t___ | Timestamp fst Pm
[seid [BOoLeAN | st |gehg | SCS OY
fopRevd [BOOLEAN [OR dchg |
fopok _fBooLeAN, [OR dchg PO
[topok [Timestamp [or |_| —SSSCSC~ir iY
[subEna [BOOLEAN —~+[ sv =~ | SSSCSCSCSCSCSCSCCC“*d*SCOPCS_SUBST_|
[subval__ [ENUMERATED | _sv_ |__| SCS SSB |
[sua [Quality sv PT rics _suest |
[subiD__|visieE stRINGSa [sv [|S cs |
[bikEna [pooteaN, | eT
[eitode! [Ctodeis | Cr [acho | SSCS SS id
[sboTimeout JiNT32U [CR dohg PO COC
[sbocless _[SboCiasses__| CF [ach | ——~—S—SCSCSCSCSCSS AC |
[operTimesut [INTS2U | CF _|dcha | ——~—~—SCSCSCSCSCSCSS AC |
[a__|VISI@LESTRINGZES| oc | [Tew —~SCS—SSSC‘ SY
a a
STRING255
[edens | VisiBLe sTRING255 [EX [| ——————S—SSS—S—~S AND
[edeName |visipte striNG2ss [| ex [PAC _DLNDA_M |
[dataNs |visiete strinc2ss| ex | AC NM
[eva SSCENUMERATED [SSCS
--- Page 49 ---
61850-7-3 Ó IEC:2010
7.5.6
Binary controlled step position information (BSC)
Table 44 defines the common data class “binary controlled step position information”.
Table 44 – Binary controlled step position information
BSC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
valWTr
ValWithTrans
dchg
AC_ST
Quality
qchg
AC_ST
TimeStamp
AC_ST
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
ValWithTrans
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
persistent
BOOLEAN
dchg
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
minVal
INT8
dchg
maxVal
INT8
dchg
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
CODED ENUM
stop | lower | higher | reserved
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -47-
7.5.6 Binary controlled step position information (BSC)
Table 44 defines the common data class “binary controlled step position information”.
Table 44 - Binary controlled step position information
attribute
name
— ce |
IEC 61850-7-2)
[engin [Onginator [st | | SSS ROO
[ottvum [intau_ ———S*i st | «dea SSCSCSC~SCS~S AC
[vaiwtr [ValWithTrans [St [dchg [ST
[a [Qualty Sid ST achg | SSS as |
[t_[Timestamp fst | Pst
[sSela[ BOOLEAN | st [gong | SCS | SCY
fopRevd [BOOLEAN | OR fdchg PO
fopok _|BooteAN [OR dchg PO
[topok [Timestamp | or | | ———SSSCS—C~ir id
[subEna [BOOLEAN _-| sv | | SSCS BT |
[subval___[ValwithTrans_[_sv_| |S st |
[sua [Quality sv PT rics suet |
[subiD__[wisiste sTRINGS?_[ sv |_| SCS cs |
[bikEna[pooteaN, | eT PT
[persistent [BOOLEAN | CF [acho | SSCS i'd
[ctiModel__[CtiModeis [CR dchg PO
[sboTimeout_[inTs2U | GF _|dchg | SCS ACC
[sboclass _[Sbociesses | CF |dcha | ——~—SCSCSCSCSSSSS AC
[minval [Te ——~—S*d er eorg | SSS
[maxval_ [NTs | GF |dchg |S | id
[operimeout [iNT32U [Cr dchg [OA COC
[a isiBLESTRING?5| oc | [Tea ——SCS SCY
Y _fsawstes =| ||
STRING255
[edens __|visipte sTRiNG2ss [Ex |_| _————S—SCS—S—* AND |
[edcName [VISIBLE STRING255 [ex | AC _DLNDAM
[dataNs | VISIBLE STRING255|_Ex |_| —————S SCAN |
--- Page 50 ---
61850-7-3 Ó IEC:2010
7.5.7
Integer controlled step position information (ISC)
Table 45 defines the common data class “integer controlled step position information”.
Table 45 – Integer controlled step position information
ISC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
valWTr
ValWithTrans
dchg
AC_ST
Quality
qchg
AC_ST
TimeStamp
AC_ST
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
ValWithTrans
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
minVal
INT8
dchg
maxVal
INT8
dchg
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
INT8
–64 … 63
--- OCR Supplementary Text ---
- 48 - 61850-7-3 © IEC:2010
7.5.7 Integer controlled step position information (ISC)
Table 45 defines the common data class “integer controlled step position information”.
Table 45 - Integer controlled step position information
[BSC ets
attribute
name
a |
IEC 61850-7-2)
[engin [Orginator iT st if | SSCS OO
[ottvum fiwtau ———~iY st | ‘(oassSSCSCSC~SCS AC 0
[vaiwtr |ValwithTrans [St [dehg [TT
[a fowalty dst faeng | SSS |
[t___ Timestamp fst TP est
[seid [BooLeaN | st |aeng | SCS SCY
fopRevd [BOOLEAN [OR [dehg PO
fopok _fBooLeAN [OR dehg [OO
[topok [Timestamp [or | | Sid id
[subEna [BOOLEAN —*+[ sv-~(| | SSSSCSCSCSCSCSCSCSSSTTC“C*d*SCOPCS SUBST
[subval___[Vaiwithtrans_[_sv_| | ———————~*Yics_ sus _|
[subQ [Quality sv PT ics _sust |
[subiD__[visiuE stRINGSa_[_sv_[__|————————*Yics_suBsT_|
[bikEna[pooteaN, | eT PT
[eitwode! [Ctodeis | CF [acho | SSCS id
[sboTimeout JinT32u [CR dehg [OA COL
[sboclass _[SboClasses | CF [den | ——~—~—SCSCSCSCS ACC
[minval [INTs ———~iY CF (adohg | SCSC~—SC‘CSCSCSSCCC*'
[manval [wre | cr Jaen | SCS OY
[operTimesut [INTS2U | GF [dena |S BO]
[¢_fvisiBte strine2ss [pc | [tet
fees, et PT
STRING255
[edeNs |visiBte strinG2ss [ex [PAC DLA
[edeName [VISIBLE STRING255 |_Ex |_| ————S—S—~S ALND |
[dataNs |visiete strinc2ss| ex | AC NM
--- Page 51 ---
61850-7-3 Ó IEC:2010
7.5.8
Controllable analogue process value (APC)
Table 46 defines the common data class “controllable analogue process value”.
Table 46 – Controllable analogue process value
APC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
measured attributes and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
mxVal
AnalogueValue
dchg
AC_ST
Quality
qchg
AC_ST
TimeStamp
AC_ST
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and  blocked
subEna
BOOLEAN
PICS_SUBST
subVal
AnalogueValue
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
units
Unit
dchg
see Annex A
INT32U
dchg
0 … 100 000
sVC
ScaledValueConfig
dchg
AC_SCAV
minVal
AnalogueValue
dchg
maxVal
AnalogueValue
dchg
stepSize
AnalogueValue
dchg
0 … (maxVal – minVal)
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
AnalogueValue
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -49-
7.5.8 Controllable analogue process value (APC)
Table 46 defines the common data class “controllable analogue process value”.
Table 46 - Controllable analogue process value
[APC class
attribute
name
IEC 61850-7-2)
[engin [Orginator i wx P| SSSCSCS™S~S OO
[ottvum fiwteu | Mx | [0.285 —SCSC~C~S~S A |
[mxVal | AnalogueValue [MX [dchg [A ST
[a fowalty YK gong fds |
[t___ | Timestamp | mx TP est
[seid [BOOLEAN | wx |aeng | SCS SCY
fopRevd [BOOLEAN | OR [dohg [Ot
fopok _|BooLeAN | OR [dehg [OO
[topok [Timestamp | or |__| SSCS Sid
[subEna [BOOLEAN ‘| sv | | SCS CS SUT |
[subval__[Anaioguevalue | _sv_| |__| Pics suasT_|
[subQ [Quality sv PT ics _“sust |
[subiD__[visieuE stRINGsa_[_sv_ |__| Pics suasT_|
[bikEna[pooteaN, |e
[eitwede! [CtWodeis | cr [acho | SSCS id
[sboTimeout JinT32U [CR dehg [OA COL
[sboclass _[SboClasses___| CF [deh [| ——~—~—S—SCSCSCSCSCS ACC
[units [Unt | GF [dong [seeAnnexA——SC~S—~—SSC‘SCC*d
[ao |wrsau | cr faeng fo. 100000 SSS
[svo_|SealedValueGonfig[ cr [acho [| —=——SCSCS CASA]
[minVal__[AnalogueValue [CF [dehg [oO
[manval[AnalogueVaiue | cr feeng | SCS SY
[stepSize | AnalogueValue | CF__[dehg [0 ... (maxVal—minval) |
[opertimesut [wrs2u | cr Jeong | SSSCS~d CTO
[¢ fvisipte strineass | oc | [tet
STRING255
[edens [Visi sTRING255 [Ex [| —————SSSS~S ARENA |
[edeName [VISIBLE STRING255| ex [| SY CAC DLN |
[sates [VISIBLE STRING255 |_ex |_| SCS AL
[etlval Sf Anatoguevalue PO
--- Page 52 ---
61850-7-3 Ó IEC:2010
7.5.9
Binary controlled analog process value (BAC)
Table 47 defines the common data class “binary controlled analog process value”.
Table 47 – Binary controlled analog process value
BAC class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
Status and control mirror
origin
Originator
AC_CO_O
ctlNum
INT8U
0..255
AC_CO_O
mxVal
AnalogueValue
dchg
AC_ST
Quality
qchg
AC_ST
TimeStamp
AC_ST
stSeld
BOOLEAN
dchg
opRcvd
BOOLEAN
dchg
opOk
BOOLEAN
dchg
tOpOk
TimeStamp
substitution and blocked
subEna
BOOLEAN
PICS_SUBST
subVal
AnalogueValue
PICS_SUBST
subQ
Quality
PICS_SUBST
subID
VISIBLE STRING64
PICS_SUBST
blkEna
BOOLEAN
configuration, description and extension
persistent
BOOLEAN
dchg
ctlModel
CtlModels
dchg
sboTimeout
INT32U
dchg
AC_CO_O
sboClass
SboClasses
dchg
AC_CO_O
units
Unit
dchg
see Annex A
INT32U
dchg
0 … 100 000
sVC
ScaledValueConfig
dchg
AC_SCAV
minVal
AnalogueValue
dchg
maxVal
AnalogueValue
dchg
stepSize
AnalogueValue
dchg
1 … (maxVal – minVal)
operTimeout
INT32U
dchg
AC_CO_O
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 39.
parameters for control services
Service parameter name
Service parameter type
Value/Value range
ctlVal
CODED ENUM
stop | lower | higher | reserved
--- OCR Supplementary Text ---
-50- 61850-7-3 © IEC:2010
7.5.9 Binary controlled analog process value (BAC)
Table 47 defines the common data class “binary controlled analog process value”.
Table 47 - Binary controlled analog process value
attribute
name
— ce |
IEC 61850-7-2)
[engin [Onginator [wx | | SSS OO
[ottvum fintau (| wx | _[o.285SCSC~C~S~S~S AC |
[mxVal__[AnalogueValue [MX [dehg [A ST
[a faulty gong [SSS ns
[t_ [Timestamp | mx TP est
[sSela[ BOOLEAN | Mx faeng [SCS SCY
fopRevd [BOOLEAN | OR [dehg [OO
[opok fBooteAN | OR dehg [OT
[topok [Timestamp [or | | Sid id
[subEna [BOOLEAN | sv | |S‘ |
[subval__[Analoguevatue | _sv_| |__| PIcs_suasT_|
[sua [Quality sv PT ics _“sust |
[subiD__[wisiste sTRINGS?_[ sv [|S Ss |
[bikEna—[pooteaN, | eT
[persistent [BOOLEAN | CF [acho | SSCS SSC i'd
[ctiModel__[CtiModeis [CR dehg PO
[sboTimeout_[inTs2U | GF [deh [SCS ACO
[sboCless _[Sbociesses | CF [dena | ——~—SCSCSCSCSCSCSS AC CO
[units [unt ~*~ er facng [see Annex SSSSCSCSC~dSSCi CY
[3a fints2u[ cr [ache [0... 109000 ——~—SC~CSSSCSC id
[sve | ScaledValueConfig [cr [dehg [AC sca |
[minVal | AnalogueValue | cr feeng | SCS SCY
[maxval___[AnalogueValue | cF_[dehg [| Tt
[stepsize | AnalogueValue | CF [aeng |? (wanVal-minva)——~—SidY SSCS
foperTimeout [inTa2u__ | CR dehg | TA COL
[¢_[visiBle sTRiNG2ss | pc [| [Tet
a a a
STRING255
[edeNs | VISIBLE sTRING2S5 [Ex [| SSS ACN |
[edeName [VISIBLE STRING255 |_Ex [|S ALND |
[dataNs [visigLe sTRiNG2ss | ex | [AC NM
--- Page 53 ---
61850-7-3 Ó IEC:2010
7.6
Common data class specifications for status settings
7.6.1
Application of services
Table 48 defines the basic controllable status settings template. In particular, it defines the
inheritance and specialization of services defined in IEC 61850-7-2.
Table 48 – Basic status setting template
Basic controllable status information template
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
setting
configuration, description and extension
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes
with a functional constraint as specified below.
Service model of
IEC 61850-7-2
Service
Service
applies to Attr
with FC
Remark
GenCommonDataClass
model
SetDataValues
GetDataValues
GetDataDefinition
GetDataDirectory
DC, CF, SP
ALL except SE
ALL
ALL
Data set model
GetDataSetValues
SetDataSetValues
ALL except SE
DC, CF
Reporting model
GSE model
Report
SendGOOSEMessage
ALL except SE
As specified within the data set that is used
to define the content of the message
Setting group control
model
SetEditSGValues
GetEditSGValues
SE, SG
7.6.2
Single point setting (SPG)
Table 49 defines the common data class “single point setting”.
Table 49 – Single point setting
SPG class
Data
attribute
name
Type
TrgOp
Value/Value range
M/O/C
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
setting
setVal
BOOLEAN
dchg
off (FALSE) | on (TRUE)
AC_NSG_M
setVal
BOOLEAN
SG, SE
off (FALSE) | on (TRUE)
AC_SG_M
configuration, description and extension
VISIBLE STRING255
Text
UNICODE
STRING255
cdcNs
VISIBLE STRING255
AC_DLNDA_M
cdcName
VISIBLE STRING255
AC_DLNDA_M
dataNs
VISIBLE STRING255
AC_DLN_M
Services
As defined in Table 48.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -51-
7.6 Common data class specifications for status settings
7.6.1 Application of services
Table 48 defines the basic controllable status settings template. In particular, it defines the
inheritance and specialization of services defined in IEC 61850-7-2.
Table 48 - Basic status setting template
Basic controllable status information template
Data Type TrgOp Value/Value range MIOIC
attribute
name
DataName _| Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
configuration, description and extension
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes
with a functional constraint as specified below.
Service model of Service Service Remark
IEC 61850-7-2 applies to Attr
with FC
GenCommonDataClass | SetDataValues DC, CF, SP
model GetDataValues ALL except SE
GetDataDefinition ALL
GetDataDirectory ALL
Data set model GetDataSetValues ALL except SE
SetDataSetValues DC, CF
Reporting model Report ALL except SE | As specified within the data set that is used
GSE model SendGOOSEMessage sp to define the content of the message
Setting group control__| SetEditSGValues SE
model GetEditSGValues SE, SG
7.6.2 Single point setting (SPG)
Table 49 defines the common data class “single point setting”.
Table 49 - Single point setting
Data Type FC | TrgOp Value/Value range M/OIC
attribute
name
DataName _| Inherited from GenDataObject Class or from GenSubDataObject Class (see
IEC 61850-7-2)
DataAttribute
BOOLEAN off (FALSE) | on (TRUE) AC_NSG_M
BOOLEAN SG, SE |_| off (FALSE) | on (TRUE) AC_SG_M
configuration, description and extension
du UNICODE DC
STRING255
VISIBLE STRINGZS5 [EX | [_—————S—SSSS——

It appears that you have provided a large block of text from the IEC 61850 standard, which defines the data attributes and their semantics for use in substation automation systems.

To assist you with your question, could you please clarify what specific aspect of this standard you would like me to help with? Are you looking for an explanation of a particular data attribute or its semantics? Or perhaps you have a question about how to implement these data attributes in a specific system?

I'll do my best to provide a concise and helpful response.

an alternate solution, the dead band calculation may use the
integral of the change of instMag. The algorithm used is a local issue.
NOTE 5 This value mag is typically used to create reports for analogue values. Such a report
sent "by exception" is not comparable to the transfer of sampled measured values as
supported by the CDC SAV.
NOTE 6 The data attribute mag explained here is not the same like the data attribute
component mag of the common data attribute type vector. Therefore in particular the value
instCVal.mag is NOT a deadbanded value.
magSVC
Scaled value configuration for magnitude. Shall be used to configure the scaled value
representation of the magnitude of the attributes instCVal, cVal, rangeC of the CDC CMV.
max
Maximum process measurement for which values of i or f are considered within process limits.
If the value is higher, q shall be set accordingly (validity = questionable, detailQual =
outOfRange).
maxPts
The maximal number of points that is supported to be set as number of points for a given curve
setting or as a number of cells for a histogram.
maxVal
Defines together with minVal the setting range for ctlVal (CDC INC, BSC, ISC), setVal (CDC
ING) or setMag (CDC APC, ASG).
min
Minimum process measurement for which values of i or f are considered within process limits.
If the value is lower, q shall be set accordingly (validity = questionable, detailQual =
outOfRange).
minVal
Defines together with maxVal the setting range for ctlVal (CDC INC, BSC, ISC), setVal (CDC
ING) or setMag (CDC APC, ASG).
model
Vendor specific product name.
mRID
Master resource ID – unique identification of an asset or device.
mxVal
Measured analogue process value. The return information with the current value of the
controllable analogue process value. The value can be dead banded for reporting.
name
The name of the IED (if DPL is used in the context of a LPHD) or of a device like a circuit
breaker (if used for the data EEName).
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -67-
Data attribute
name
instCVal Instant value of a vector type value.
Magnitude of the instantaneous value of a measured value.
NOTE 3 The presence of the attribute instMag is optional, that only affects the visibility of that
value to the communication. The instantaneous value may be required for the internal
behaviour of the function, e.g. to perform the deadband calculation as explained with the
attribute mag.
latitude Geographical position of device in WGS84 coordinates — latitude.
Logical device name space. For details, see IEC 61850-7-1.
Logical node name space. For details, see IEC 61850-7-1.
Location, where the equipment is installed
longitude Geographical position of device in WGS84 coordinates — longitude
Deadbanded value. Shall be based on a dead band calculation from the instantaneous value
(modelled as instMag) as illustrated below. The value of mag shall be updated to the current
instantaneous value when the value has changed according the configuration parameter db. If
db=0, the value of mag is identical to the value of instMag.
instMag
mag
mag
lec 2556/10
NOTE 4 The drawing above is an example. There may be other algorithms providing a
comparable result; for example as an alternate solution, the dead band calculation may use the
integral of the change of instMag. The algorithm used is a local issue.
NOTE 5 This value mag is typically used to create reports for analogue values. Such a report
sent "by exception" is not comparable to the transfer of sampled measured values as
supported by the CDC SAV.
NOTE 6 The data attribute mag explained here is not the same like the data attribute
component mag of the common data attribute type vector. Therefore in particular the value
instCVal.mag is NOT a deadbanded value.
magsVC Scaled value configuration for magnitude. Shall be used to configure the scaled value
8 representation of the magnitude of the attributes instCVal, cVal, rangeC of the CDC CMV.
Maximum process measurement for which values of i or f are considered within process limits.
max If the value is higher, q shall be set accordingly (validity = questionable, detailQual =
outOfRange).
The maximal number of points that is supported to be set as number of points for a given curve
maxPts
setting or as a number of cells for a histogram.
Val Defines together with minVal the setting range for ctlVal (CDC INC, BSC, ISC), setVal (CDC
maxVal ING) or setMag (CDC APC, ASG).
Minimum process measurement for which values of i or f are considered within process limits
min If the value is lower, q shall be set accordingly (validity = questionable, detailQual =
outOfRange).
Val Defines together with maxVal the setting range for ctlVal (CDC INC, BSC, ISC), setVal (CDC
minVal ING) or setMag (CDC APC, ASG).
Val Measured analogue process value. The return information with the current value of the
mxVal controllable analogue process value. The value can be dead banded for reporting.
name The name of the IED (if DPL is used in the context of a LPHD) or of a device like a circuit
breaker (if used for the data EEName).
--- Page 70 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
net
Net current. Net current is the algebraic sum of the instantaneous values of currents flowing
through all live conductors (sum over phase currents) and neutral of a circuit at a point of the
electrical installation. For further details, see phsA (WYE).
netHar
This array shall contain the harmonic and subharmonics or interharmonic values related to net
current. For further details, see Har.
neut (WYE)
Value of the measured phase neutral. If a direct measurement of this value is not available, it
is acceptable to substitute an estimate computed by creating the algebraic sum of the
instantaneous values of currents flowing through all live conductors. In that case, 'neut' is
identical to 'res'. For further details, see phsA (WYE).
neut
(ACT, ACD)
Start event with earth current.
neutHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
neutral. For further details, see Har.
numCyc
Number of cycles of power frequency, which are used for harmonic, subharmonic and
interharmonic calculation. For further details, see har.
numHar
Number of harmonic and subharmonics or interharmonic values that can be accessed. The
range of the numHar value shall be 1 or greater. The array element "1" shall represent the first
harmonic value. The value 0 shall refer to the dc component. The maximal value for numHar
may be calculated as follows:
numCycl
evalTm
frequency
smpRate
numHar
numPts
Number of points or cells used to define a curve or a histogram.
operTimeout
This attribute specifies the timeout used to supervise an operation according the control model
defined in IEC 61850-7-2. When operTimeout expires without an indication of a new valid
state, the command action shall be terminated. In the control models with enhanced security, a
negative command termination is sent as response. The value shall be in ms.
operTmPhsA
Operation Time for Phase A. Is used for point on wave switching.
operTmPhsB
Operation Time for Phase B. Is used for point on wave switching.
operTmPhsC
Operation Time for Phase C. Is used for point on wave switching.
opRcvd
Indication that a operate command for a controllable data object has been received. Used for
testing purposes together with opOk and tOpOk in particular when the LN mode is TEST-
BLOCKED.
IED
Control service
Wired output
ctlVal
opRcvd
opOk
tOpOk
IEC   2557/10
The command is received by the IED as a control service or as a GOOSE message with a data
object that is interpreted as a operate request on the controllable object. The command is then
processed. If the command is accepted, the wired output would be activated. The data attribute
opOk confirms that the command has been accepted and reflects the timing of the wired
output; i.e. the duration of that signal is determined by the CF attribute pulseConfig if the
output is a pulse. The data attribute tOpOk is a timestamp indicating when the output would be
activated.
opOk
Indication that an operate command for a controllable data object has been evaluated and
accepted. For details, see opRcvd.
--- Page 71 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
origin
Origin contains information related to the originator of the last change of the process value of
the controllable object.
If the initiator of a change of the process value is not known, origin.orCat shall be set to
process and origin.orIdent shall be set to NULL.
Substitution shall not affect the value of origin.
originSrc
originSrc contains the information related to the originator of a control action forwarded by a
GOOSE message.
owner
Owner of the device.
paramRev
Uniquely identifies the parameter revision of a logical device or logical node instance.
ParamRev has to be changed at least on any change of a parameter (FC=SE or FC=SP) within
this logical device or logical node. How this is detected and performed is left to the user. For
further details, see as well Annex C.
The change of ParamRev shall be done with the following semantic:
if the parameter change is done in the IED only through communication services or
through the local HMI, the value shall be increased by one;
if the parameter change is done in the configuration file, the value shall be increased by
10 000.
persistent
Configures the control output. If set to FALSE, the operate service results in the change of
exactly one step higher or lower. If set to TRUE, the operate service initiates the persistent
activation of the output. The output shall be deactivated by an operate service with the value
stop or by a local timeout. A client may repeat sending the operate service in order to retrigger
the output.
If persitent is set to TRUE, ctlModel shall be set to direct-with-normal-security.
phsA (WYE)
Value of phase A. In the WYE class, values for phsA, phsB, phsC neut, net and res have been
simultaneously acquired or determined. It shall be assumed that any jitter between the
acquisition times dedicated for phsA, phsB, phsC neut, net and res is neglectable. The jitter for
simultaneity shall be as indicated in the time quality field.
The relation between the phase values and neutral, net and residual is illustrated in the
following drawing:
neut
Ground
Ires
Inet
Inet
Neutral
point
Inet = IA + IB + IC + Ineut
Ires = IA + IB + IC
Zearth
Ineut
Ires = 0 if not grounded and no fault
Ires > 0 and Inet = 0 if not grounded and fault (phase asymmetry)
Inet > 0 if grounded and fault (phase asymmetry)
IEC   2558/10
phsA
(ACT, ACD)
Trip or start event of phase A.
phsAB
Value of phase A to phase B measurement. In the DEL class, values for phsAB, phsBC and
phsCA have been simultaneously acquired or determined. It shall be assumed that any jitter
between the acquisition times dedicated for phsAB, phsBC and phsCA is neglectable. The jitter
for simultaneity shall be as indicated in the time quality field.
phsABHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase A to phase B. For further details, see Har.
phsAHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase A. For further details, see Har.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -69-
Data attribute
name
Origin contains information related to the originator of the last change of the process value of
the controllable object.
origin If the initiator of a change of the process value is not known, origin.orCat shall be set to
process and origin.orldent shall be set to NULL.
Substitution shall not affect the value of origin.
originSre originSrc contains the information related to the originator of a control action forwarded by a
GOOSE message.
Owner of the device.
Uniquely identifies the parameter revision of a logical device or logical node instance.
ParamRev has to be changed at least on any change of a parameter (FC=SE or FC=SP) within
this logical device or logical node. How this is detected and performed is left to the user. For
further details, see as well Annex C.
paramRev The change of ParamRev shall be done with the following semantic:
— if the parameter change is done in the IED only through communication services or
through the local HMI, the value shall be increased by one;
- if the parameter change is done in the configuration file, the value shall be increased by
10 000.
Configures the control output. If set to FALSE, the operate service results in the change of
exactly one step higher or lower. If set to TRUE, the operate service initiates the persistent
activation of the output. The output shall be deactivated by an operate service with the value
persistent stop or by a local timeout. A client may repeat sending the operate service in order to retrigger
the output.
If persitent is set to TRUE, ctIModel shall be set to direct-with-normal-security.
Value of phase A. In the WYE class, values for phsA, phsB, phsC neut, net and res have been
simultaneously acquired or determined. It shall be assumed that any jitter between the
acquisition times dedicated for phsA, phsB, phsC neut, net and res is neglectable. The jitter for
simultaneity shall be as indicated in the time quality field.
The relation between the phase values and neutral, net and residual is illustrated in the
following drawing:
go La A
S c
phsA (WYE) Inet “D NW,
hes
z OS neut
ary Toot wy,
Ground = Inet
IEC 2558/10
phsA
(ACT, ACD) Trip or start event of phase A.
Value of phase A to phase B measurement. In the DEL class, values for phsAB, phsBC and
hsAB phsCA have been simultaneously acquired or determined. It shall be assumed that any jitter
Pp between the acquisition times dedicated for phsAB, phsBC and phsCA is neglectable. The jitter
for simultaneity shall be as indicated in the time quality field.
hsABHal This array shall contain the harmonic and subharmonics or interharmonic values related to
pi r phase A to phase B. For further details, see Har.
hsAHar This array shall contain the harmonic and subharmonics or interharmonic values related to
pi phase A. For further details, see Har.
--- Page 72 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
phsB (WYE)
Value of phase B. For further details, see phsA (WYE).
phsB
(ACT, ACD)
Trip or start event of phase B.
phsBC
Value of phase B to phase C measurement. For further details, see phsAB.
phsBCHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase B to phase C. For further details, see Har.
phsBHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase B. For further details, see Har.
phsC (WYE)
Value of phase C. For further details, see phsA (WYE).
phsC
(ACT, ACD)
Trip or start event of phase C.
phsCA
Value of phase C to phase A measurement. For further details, see phsAB.
phsCAHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase C to phase A. For further details, see Har.
phsCHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
phase C. For further details, see Har.
phsRef
Indicates which phase has been used as reference for the transformation of phase values to
sequence values.
phsToNeut
This configuration parameter indicates that the WYE class is used for phase to neutral values
instead of phase to ground values. The data attribute neut will always indicate the neutral to
ground value.
pointZ
Position of the curve on z-axis.
primeOper
Primary operator of device.
pulseConfig
Used to configure the output pulse generated with the command if applicable.
pulsQty
Magnitude of the counted value per count. actVal/frVal and pulsQty are used to calculate
the value:
value = actVal ´ pulsQty
value = frVal ´ pulsQty
purpose
Description of the purpose of the object reference.
Quality of the attribute(s) representing the value of the data. For the different CDCs, q applies
to the following data attributes:
CDC
data attribute q applies to
SPS
stVal
DPS
stVal
INS
stVal
ENS
stVal
ACT
general, phsA, phsB, phsC, neut
ACD
general, dirGeneral, phsA, dirPhsA, phsB, dirPhsB, phsC,
dirPhsC, neut, dirNeut
BCR
actVal, frVal
HST
hstCnt
VSS
stVal
instMag, Mag, range
CMV
instCMag, cMag, range
SAV
instMag
HMV
Har
HWYE
phsAHar, phsBHar, phsCHar, neutHar, netHar, resHar
HDEL
phsABHar, phsBCHar, phsCAHar
SPC
stVal
DPC
stVal
INC
stVal
ENC
stVal
BSC
valWTr
ISC
valWTr
APC
mxVal
BAC
mxVal
--- OCR Supplementary Text ---
-70- 61850-7-3 © IEC:2010
Data attribute
name
phsB (WYE) Value of phase B. For further details, see phsA (WYE).
phsB
(ACT, ACD) Trip or start event of phase B.
phsBC Value of phase B to phase C measurement. For further details, see phsAB
hsBCHar This array shall contain the harmonic and subharmonics or interharmonic values related to
p phase B to phase C. For further details, see Har.
hsBHar This array shall contain the harmonic and subharmonics or interharmonic values related to
p phase B. For further details, see Har.
phsC (WYE) Value of phase C. For further details, see phsA (WYE).
phsC
(ACT, ACD) Trip or start event of phase C.
phsCA Value of phase C to phase A measurement. For further details, see phsAB
hsCAHar This array shall contain the harmonic and subharmonics or interharmonic values related to
m phase C to phase A. For further details, see Har.
hsCHar This array shall contain the harmonic and subharmonics or interharmonic values related to
p phase C. For further details, see Har.
hsRef Indicates which phase has been used as reference for the transformation of phase values to
p sequence values
phsToNeut This configuration parameter indicates that the WYE class is used for phase to neutral values
instead of phase to ground values. The data attribute neut will always indicate the neutral to
ground value.
Primary operator of device.
pulseConfig Used to configure the output pulse generated with the command if applicable.
Magnitude of the counted value per count. actVal/frVal and pulsQty are used to calculate
the value:
pulsQty
value = actVal x pulsQty
value = frVal x pulsQty
Description of the purpose of the object reference.
Quality of the attribute(s) representing the value of the data. For the different CDCs, q applies
to the following data attributes:
data attribute q applies to
general, phsA, phsB, phsC, neut
ACD general, dirGeneral, phsA, dirPhsA, phsB, dirPhsB, phsC,
dirPhsC, neut, dirNeut
actVal, frVal
hstCnt
instCMag, cMag, range
HWYE phsAHar, phsBHar, phsCHar, neutHar, netHar, resHar
HDEL phsABHar, phsBCHar, phsCAHar
--- Page 73 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
range
Range in which the current value of instMag or instCVal.mag is. It may be used to issue an
event if the current value changes and transitions to another range. Range shall be used in the
context with configuration attributes like hhLim, hLim, lLim, llLim, min and max as shown
below.
hhLim
llLim
hLim
lLim
normal
high
low
low-low
high-high
min
max
questionable
questionable
range
validity
outOfRange
outOfRange
detail-qual
good
good
good
good
good
high-high
low-low
NOTE 7 The use of algorithms to filter events based on transition from one range to another
is a local issue.
NOTE 8 This value with the trigger option “data-change” as described in 61850-7-2 may be
used to report an event to the client.
rangeAng
Range in which the current value of instCVal.ang is. For further details, see range.
rangeAngC
Configuration parameters as used in the context with the rangeAng attribute.
rangeC
Configuration parameters as used in the context with the range attribute.
res
Residual current. Residual current is the algebraic sum of the instantaneous values of currents
flowing through all live conductors (i.e. sum over phase currents) of a circuit at a point of the
electrical installation. For further details, see phsA (WYE).
resHar
This array shall contain the harmonic and subharmonics or interharmonic values related to
residual current. For further details, see Har.
rmsCyc
Number of cycles of power frequency, which are used for the calculation of rms values.
sboClass
Specifies the SBO-class according to the control model of IEC 61850-7-2 that corresponds to
the behaviour of the data. The following values are defined:
operate-once:
Following an operate request, the control object shall return in
the unselected state.
operate-many:
Following an operate request, the control object shall remain in
the ready state, as long as sboTimeout did not expire.
sboTimeout
Specifies the timeout between a select and an operate command according to the control
model of IEC 61850-7-2. The value shall be in ms.
secondOper
Secondary operator of device.
seqT
This attribute shall specify the type of the sequence. The following values are used:
value
pos-neg-zero
pos
neg
zero
dir-quad-zero
dir
quad
zero
serNum
Serial number.
setCal
The value of a time setting, if the time is set with a calendar time.
--- Page 74 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
setCharact
This attribute shall describe the curve characteristic. The values are defined below. Each curve
is of the form x = f(y). There are three options to describe f(y):
characteristic = 1 … 16: As a formula based on up to 6 parameters A, B, C, D, E and F.
The formula is standardized by ANSI or IEC. ANSI and IEC also specify the values for A,
B, C, D, E and F in that case, the corresponding attributes (setParA, ..., set ParF) are
read-only.
characteristic = 17 … 32: As a definable formula based on up to 6 parameters A, B, C, D,
E and F. In that case, it may be possible that the parameters may be modified. The
specification of the formula is a local issue. The actual shape of the curve may be read out
using a dedicated data of the CDC CSD.
characteristic = 33 … 48: As a definable curve specified as an array of n (x,y) pairs. The
specification of the array can be performed using data of CDC = CSG where applicable.
Otherwise it is a local issue. The actual shape of the curve may be read out using a
dedicated data of the CDC CSD.
value
curve characteristic
ANSI Extremely Inverse
ANSI Very Inverse
ANSI Normal Inverse
ANSI Moderately Inverse
ANSI Definite Time (Definite Time Over Current = default)
Long-Time Extremely Inverse
Long-Time Very Inverse
Long-Time Inverse
IEC Normal Inverse
IEC Very Inverse
IEC Inverse
IEC Extremely Inverse
IEC Short-Time Inverse
IEC Long-Time Inverse
IEC Definite Time
Reserved
Definable curve 1 based on formula [x=f(y,A,B,C,D, E, F)]
Definable curve 16 based on formula [x=f(y,A,B,C,D, E, F)]
Vendor specific curve 1 defined by n pairs (x,y)
Vendor specific curve 16 defined by n pairs (x,y)
setMag
The value of an analogue setting or set point.
setParA
Attribute used to set the parameter A of the setting curve (see detailed description under
setCharact).
setParB
Attribute used to set the parameter B of the setting curve (see detailed description under
setCharact).
setParC
Attribute used to set the parameter C of the setting curve (see detailed description under
setCharact).
setParD
Attribute used to set the parameter D of the setting curve (see detailed description under
setCharact).
setParE
Attribute used to set the parameter E of the setting curve (see detailed description under
setCharact).
setParF
Attribute used to set the parameter F of the setting curve (see detailed description under
setCharact).
setSrcCB
The value of the object reference to the control block indicating from where the object referred
to with setSrcRef shall be received.
setSrcRef
The value of the object reference setting. The attribute may be used to reference e.g. a logical
node instance or a data object instance.
setTm
The value of a time setting, if the time is set with a time stamp.
setTstCB
The value of the object reference to the control block indicating from where the object referred
to with setTstRef shall be received. For details, see tstEna.
setTstRef
The value of the object reference setting used when tstEna is true for testing purpose as an
alternate reference to the reference set with setSrcRef. For details, see tstEna.
--- OCR Supplementary Text ---
-72- 61850-7-3 © IEC:2010
Data attribute
name
This attribute shall describe the curve characteristic. The values are defined below. Each curve
is of the form x = f(y). There are three options to describe f(y):
1) characteristic = 1 ... 16: As a formula based on up to 6 parameters A, B, C, D, E and F.
The formula is standardized by ANSI or IEC. ANSI and IEC also specify the values for A,
B, C, D, E and F in that case, the corresponding attributes (setParA, ..., set ParF) are
read-only.
2) characteristic = 17 ... 32: As a definable formula based on up to 6 parameters A, B, C, D,
E and F. In that case, it may be possible that the parameters may be modified. The
specification of the formula is a local issue. The actual shape of the curve may be read out
using a dedicated data of the CDC CSD.
3) characteristic = 33 ... 48: As a definable curve specified as an array of n (x,y) pairs. The
specification of the array can be performed using data of CDC = CSG where applicable.
Otherwise it is a local issue. The actual shape of the curve may be read out using a
dedicated data of the CDC CSD.
[value | curvecharacteristic
[4 ANS! Extremely Inverse
ANSI Very Inverse
‘ANSI Normal Inverse
setCharact [4 | ANSI Moderately Inverse
ANSI Definite Time (Definite Time Over Current = default
[6 | Long-Time Extremely Inverse
Long-Time Very Inverse
[e [| Long-Time Inverse
[9 [EC Normal Inverse
[10 | IEC Very Inverse
[41 IEC Inverse
IEC Extremely Inverse
IEC Short-Time Inverse
IEC Long-Time Inverse
IEC Definite Time
[16 | Reserved
Definable curve 1 based on formula [x=f(y.A.B,C.D, E, F
ee |
Definable curve 16 based on formula [x=fy.A.B,C.D, E, F)
Vendor specific curve 1 defined by n pairs (x,
ee |
[48__| Vendor specific curve 16 defined by n pairs (x,
Attribute used to set the parameter A of the setting curve (see detailed description under
setParA
setCharact).
Attribute used to set the parameter B of the setting curve (see detailed description under
setParB
setCharact).
{Parc Attribute used to set the parameter C of the setting curve (see detailed description under
setPar setCharact).
Attribute used to set the parameter D of the setting curve (see detailed description under
setParD
setCharact).
Attribute used to set the parameter E of the setting curve (see detailed description under
setParE
setCharact).
Attribute used to set the parameter F of the setting curve (see detailed description under
setParF
setCharact).
The value of the object reference to the control block indicating from where the object referred
setSrcCB
to with setSrcRef shall be received.
The value of the object reference setting. The attribute may be used to reference e.g. a logical
setSrcRef
node instance or a data object instance.
The value of a time setting, if the time is set with a time stamp.
setTstCB The value of the object reference to the control block indicating from where the object referred
to with setTstRef shall be received. For details, see tstEna.
tTstRef The value of the object reference setting used when tstEna is true for testing purpose as an
setT sine! alternate reference to the reference set with setSrcRef. For details, see tstEna.
--- Page 75 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
setVal
The value of a status setting.
sev
Severity of the last violation detected. The values are:
value
unknown
Severity cannot be determined.
critical
Severity is critical in terms of safe operation or data
considered critical and privileged access was attempted.
major
Severity is major in terms of safe operation or data
considered of major importance and privileged access was
attempted.
minor
Severity is minor in the sense that access control was
denied to data considered privileged.
warning
Is less severe than minor.
smpRate (HMV,
HWYE, HDEL)
Determines according to the sampling theorem the highest possible harmonic or interharmonic
detectable. The minimum is 2 ´ frequency. The value shall represent the number of samples
per nominal period. In the case of a d.c. system, the value shall represent the number of
samples per s.
smpRate (MV,
CMV, W YE,
DEL)
Sampling rate that has been used to determine the analogue values. The value shall represent
the number of samples per nominal period. In the case of a d.c. system, the value shall
represent the number of samples per s.
stepSize
Defines the step between individual values that ctlVal (CDC INC, APC, BAC), setVal (CDC
ING) or setMag (CDC ASG) will accept.
strTm
Starting time of the freeze process. If the current time is later than the start time, the first
freeze shall occur at the next freeze interval (frPd) expiration, computed from the start time
setting.
stSeld
The controllable data is in the status "selected".
stVal
Status value of the data.
subCVal
Value used to substitute the data attribute instCVal.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -73-
Data attribute
name
The value of a status seting
Severity of the last violation detected. The values are:
[value PT
Severity cannot be determined.
critical Severity is critical in terms of safe operation or data
considered critical and privileged access was attempted.
sev major ‘Severity is major in terms of safe operation or data
considered of major importance and privileged access was
attempted.
minor Severity is minor in the sense that access control was
denied to data considered privileged.
Is less severe than minor.
Determines according to the sampling theorem the highest possible harmonic or interharmonic
smpRate (HMV, | detectable. The minimum is 2 x frequency. The value shall represent the number of samples
HWYE, HDEL) | per nominal period. In the case of a d.c. system, the value shall represent the number of
samples per s
smpRate (MV, | Sampling rate that has been used to determine the analogue values. The value shall represent
CMV, WYE, the number of samples per nominal period. In the case of a d.c. system, the value shall
DEL) represent the number of samples per s.
stepSize Defines the step between individual values that ctlVal (CDC INC, APC, BAC), setVal (CDC
Ps ING) or setMag (CDC ASG) will accept.
Starting time of the freeze process. If the current time is later than the start time, the first
strTm freeze shall occur at the next freeze interval (frPd) expiration, computed from the start time
setting.
stSeld The controllable data is in the status "selected".
Status value of the data
subCVal Value used to substitute the data attribute instCVal.
--- Page 76 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
subEna
Used to enable substitution. If this attribute is set to true, the attribute(s) representing the
value of the data instance shall always be set to the same value as the attribute(s) used to
store the substitution value of the data. If this attribute is set to false, the attribute(s)
representing the value of the data instance shall be based on the process value. For the
different CDCs, subEna applies to the following data attributes:
CDC
data attribute subEna applies to
SPS
stVal and subVal, q and subQ
DPS
stVal and subVal, q and subQ
INS
stVal and subVal, q and subQ
ENS
stVal and subVal, q and subQ
instMag and subMag, q and subQ
CMV
instCVal and subCVal, q and subQ
SPC
stVal and subVal, q and subQ
DPC
stVal and subVal, q and subQ
INC
stVal and subVal, q and subQ
ENC
stVal and subVal, q and subQ
BSC
valWTr and subVal, q and subQ
ISC
valWTr and subVal, q and subQ
APC
mxVal and subVal; q and subQ
BAC
mxVal and subVal; q and subQ
In the typical use case for substitution, an operator on the client side enters manually a value
for a DataAttribute located in a specific device. The client sets the DataAttribute to the value
entered. If a client accesses the value of that DataAttribute (for example, using a
GetDataValue service or subscribing to a report), the client shall receive the manual entered
(substituted) value instead of the value determined by the process.
The concept of substitution is shown below. Usually, input from the process or the result of the
calculation from a function provides the value of a DataAttribute (in that case, the source is
called “process”). In case of substitution, the value of a DataAttribute may be provided by an
operator making use of a client. This selection of the source of the value (substitution value or
process value) shall be controlled by the service SetDataValue (“xy.subEna” <TRUE>) to
substitute or SetDataValue (“xy.subEna” <FALSE>) to unsubstitute. The service SetDataValue
(“xy.subVal” <value for substitution>) shall be used to set the substituted value. There may be
cases where a local automatic function disables substitution, for example, if blocking of
information exchange is disabled or communication is no longer interrupted.
xy.stVal
Example: Common data class
"SPS" (see IEC 61850-7-3)
xy.q.validity
xy.q.detail-qual
xy.q.source
TRUE = substituted
FALSE = process
xy.subVal = value for substitution
xy.subQ = good
xy.subID = {}
xy.subEna
Values visible
through ACSI
”Switch" controlled by service:
SetDataValue ”xy.subEna” <TRUE>
Value locally stored for
substitution; set by service:
SetDataValue ”xy.subVal” <value>
SetDataValue ”xy.subQ” <value>
SetDataValue ”xy.subID” <value>
Values
determined  by
the process
xy.subEna
IEC   2559/10
It is the responsibility of the client application, in particular in the case of multiple attributes to
be substituted, to set all relevant substitution values before enabling substitution. While
substitution is enabled, changing of all substitution-related atttibutes is allowed but it is the
responsibility of the implementation to avoid inconsistent transient value combination.
subID
Shows the address of the device that made the substitution. The value of null shall be used if
subEna is false or if the device is not known.
subMag
Value used to substitute the data attribute instMag.
subQ
Value used to substitute the data attribute q.
--- OCR Supplementary Text ---
-74- 61850-7-3 © IEC:2010
Data attribute
name
Used to enable substitution. If this attribute is set to true, the attribute(s) representing the
value of the data instance shall always be set to the same value as the attribute(s) used to
store the substitution value of the data. If this attribute is set to false, the attribute(s)
representing the value of the data instance shall be based on the process value. For the
different CDCs, subEna applies to the following data attributes:
data attribute subEna applies to
stVal and subVal, q and subQ
stVal and subVal, gq and subQ.
stVal and subVal, q and subQ
stVal and subVal, q and subQ
instMag and subMag, q and subQ
instCVal and subCVal, q and subQ
stVal and subVal, g and subQ
stVal and subVal, g and subQ.
stVal and subVal, q and subQ
stVal and subVal, q and subQ
valWTr and subVal, q and subQ
valWTr and subVal, q and subQ
mxVal and subVal; g and subQ
mxVal and subVal; q and subQ.
In the typical use case for substitution, an operator on the client side enters manually a value
for a DataAttribute located in a specific device. The client sets the DataAttribute to the value
entered. If a client accesses the value of that DataAttribute (for example, using a
GetDataValue service or subscribing to a report), the client shall receive the manual entered
(substituted) value instead of the value determined by the process.
The concept of substitution is shown below. Usually, input from the process or the result of the
calculation from a function provides the value of a DataAttribute (in that case, the source is
called “process"). In case of substitution, the value of a DataAttribute may be provided by an
subEna operator making use of a client. This selection of the source of the value (substitution value or
process value) shall be controlled by the service SetDataValue (“xy.subEna” <TRUE>) to
substitute or SetDataValue (“xy.subEna” <FALSE>) to unsubstitute. The service SetDataValue
(‘xy.subVal” <value for substitution>) shall be used to set the substituted value. There may be
cases where a local automatic function disables substitution, for example, if blocking of
information exchange is disabled or communication is no longer interrupted.
[ xy subena
FTRUE = substituted
~ [>|
Example: Common data class
"SPS" (see IEC 61850-7-3)
lec 2559/10
It is the responsibility of the client application, in particular in the case of multiple attributes to
be substituted, to set all relevant substitution values before enabling substitution. While
substitution is enabled, changing of all substitution-related atttibutes is allowed but it is the
responsibility of the implementation to avoid inconsistent transient value combination.
Shows the address of the device that made the substitution. The value of null shall be used if
subEna is false or if the device is not known
[subMag | Value used to substitute the data attribute instMag
[sa | Value used to substitute the data attribute q.
--- Page 77 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
subVal
Value used to substitute the attribute representing the value of the data instance. For the
different CDCs, subVal is used to substitute the following data attributes:
CDC
data attribute subVal is used to substitute
SPS
stVal
DPS
stVal
INS
stVal
ENS
stVal
SPC
stVal
DPC
stVal
INC
stVal
ENC
stVal
BSC
valWTr
ISC
valWTr
APC
mxVal
BAC
mxVal
sVC
Scaled value configuration. Shall be used to configure the scaled value representation. For the
different CDCs, sVC applies to the following data attributes and service parameters:
CDC
data attribute sVC applies to
instMag, mag, subMag, rangeC
SAV
instMag, min, max
APC
mxVal, subVal, minVal, maxVal, stepSize; service
parameter ctlVal
BAC
mxVal, subVal, minVal, maxVal, stepSize
ASG
setMag, minVal, maxVal, stepSize
swRev
SW-revision.
Timestamp of the last change in one of the attribute(s) representing the value of the data or in
the q attribute. For the different CDCs, t applies to the following data attributes:
CDC
data attribute t applies to
SPS
stVal
DPS
stVal
INS
stVal
ENS
stVal
ACT
general, phsA, phsB, phsC, neut
ACD
general, dirGeneral, phsA, dirPhsA, phsB, dirPhsB, phsC,
dirPhsC, neut, dirNeut
SEC
cnt
BCR
actVal
HST
hstCnt
VSS
stVal
mag, range
CMV
cVal, range
SAV
instMag
HMV
Har
HWYE
phsAHar, phsBHar, phsCHar, neutHar, netHar, resHar
HDEL
phsABHar, phsBCHar, phsCAHar
SPC
stVal
DPC
stVal
INC
stVal
ENC
stVal
BSC
valWTr
ISC
valWTr
APC
mxVal
BAC
mxVal
tOpOk
The time stamp with the time, when an output of a controllable object is activated following a
control command. For details, see opRcvd.
--- OCR Supplementary Text ---
61850-7-3 © IEC:2010 -75-
Data attribute
Value used to substitute the attribute representing the value of the data instance. For the
different CDCs, subVal is used to substitute the following data attributes:
supval
Scaled value configuration. Shall be used to configure the scaled value representation. For the
different CDCs, sVC applies to the following data attributes and service parameters:
svc
APC mxVal, subVal, minVal, maxVal, stepSize; service
[Re | eamotereavar eves sensisi sens |
Timestamp of the last change in one of the attribute(s) representing the value of the data or in
the q attribute. For the different CDCs, t applies to the following data attributes
dirPhsC, neut, dirNeut
control command. For details, see opRevd.
--- Page 78 ---
61850-7-3 Ó IEC:2010
Data attribute
name
Semantics
tstEna
Switch between original data source (as defined with setSrcRef and setSrcCB) for a reference
and test data source (as defined with setTstRef and setTstCB). The concept is explained in the
following drawing.
LN xxxx
InRef1 (ORG)
- setSrcRef
- setTstRef
- tstEna
- …
Input1
Out
Funct
LN yyyy
Out
LN xxxx
(Test Fkt)
SPCSO1
tstEna=TRUE
IEC   2560/10
In a normal operation, the LN xxxx receives as an input the signal Out from LN yyyy. The data
attribute xxxx.InRef1.setSrcRef points to yyyy.Out. For functional testing of the LN xxxx, a
logical node GTST may be used to generate test patterns. In that case, the LN xxxx shall
receive the input from LN GTST; e.g. the signal SPCSO1. This is indicated by the data attribute
xxx.InRef1.setTstRef. By setting xxx.InRef1.tstEna to TRUE, the LN xxxx will start receiving
the signal InRef1 from GTST instead of yyyy.
units
Units of the attribute(s) representing the value of the data. For the different CDCs, units
applies to the following data attributes and service parameters:
CDC
data attribute units applies to
BCR
actVal, frVal, pulsQty
instMag, mag, subMag, rangeC
CMV
instCVal.mag, cVal.mag, subCVal.mag, rangeC
SAV
instMag, min, max
HST
hstVal
APC
mxVal, subVal, minVal, maxVal, stepSize; service
parameter ctlVal
BAC
mxVal, subVal, minVal, maxVal, stepSize
ASG
setMag, minVal, maxVal, stepSize
valRev
Uniquely identifies the revision of the preconfiguration of configuration values (FC= CF) in a
logical device or logical node instance through a SCL file. ValRev has to be changed at least
on any change of preconfigured values within an SCL file for this logical device or logical node.
How this is detected and performed is left to the user. For further details, see as well Annex C.
The change of ValRev shall be done with the following semantic:
if the value change is done in the IED only through communication services or through
the local HMI, the value shall be increased by one,
if the value change is done in the configuration file, the value shall be increased by 10'000.
valWTr
Value with transient indication.
vendor
Name of the vendor.
Description of the value of the x-axis of a curve.
xDU
Description of the value of the x-axis of a curve in UNICODE.
xUnits
Unit of the x-axis of a curve.
Description of the value of the y-axis of a curve.
yDU
Description of the value of the x-axis of a curve in UNICODE.
yUnits
Unit of the y-axis of a curve.
Description of the value of the z-axis of a curve.
zDU
Description of the value of the x-axis of a curve in UNICODE.
--- OCR Supplementary Text ---
-76- 61850-7-3 © IEC:2010
Data attribute
name
Switch between original data source (as defined with setSrcRef and setSrcCB) for a reference
and test data source (as defined with setTstRef and setTstCB). The concept is explained in the
following drawing.
tstEna .
tstEna=TRUE 7
EC 2560/10
In anormal operation, the LN xxxx receives as an input the signal Out from LN yyyy. The data
attribute xxxx.InRef1.setSrcRef points to yyyy.Out. For functional testing of the LN xxxx, a
logical node GTST may be used to generate test patterns. In that case, the LN xxxx shall
receive the input from LN GTST; e.g. the signal SPCSO1. This is indicated by the data attribute
xxx.InRef1.setTstRef. By setting xxx.InRef1.tstEna to TRUE, the LN xxxx will start receiving
the signal InRef1 from GTST instead of yyyy.
Units of the attribute(s) representing the value of the data. For the different CDCs, units
applies to the following data attributes and service parameters:
[CDC —_—i| data attribute units applies to
actVal, frVal, pulsQt
instMag, mag, subMag, rangeC
instCVal.mag, cVal.mag, subCVal.mag, rangeC
hstVal
“APC mxVal, subVal, minVal, maxVal, stepSize; service
parameter ctlVVal
mxVal, subVal, minVal, maxVal, stepSize_
setMag, minVal, maxVal, stepSize
Uniquely identifies the revision of the preconfiguration of configuration values (FC= CF) in a
logical device or logical node instance through a SCL file. ValRev has to be changed at least
on any change of preconfigured values within an SCL file for this logical device or logical node.
How this is detected and performed is left to the user. For further details, see as well Annex C.
valRev The change of ValRev shall be done with the following semantic:
— if the value change is done in the IED only through communication services or through
the local HMI, the value shall be increased by one,
— if the value change is done in the configuration file, the value shall be increased by 10'000.
Value with transient indication.
Name of the vendor.
[x0 __| Description of the

It appears that you have provided a large block of text in French, which is the content of a standard ISO/IEC 61850-7-3 document. This document provides specifications for the communication between intelligent electronic devices (IEDs) in electrical substations.

The text describes various classes of data and their attributes, including:

1. Quality: This class defines the quality of the data, including validity, detail quality, source control, test, and operator-controlled.
2. Analogue value: This class represents a measured or calculated analogue value, such as voltage, current, or power.
3. Measured value: This class represents a measured value, which can be a single value or a complex value with multiple attributes.
4. Sampled value: This class represents a sampled value, which is a discrete-time representation of an analogue signal.
5. Controllable single point (SPC): This class represents a controllable single-point setting, such as a switch or a relay.
6. Controllable double point (DPC): This class represents a controllable double-point setting, such as a circuit breaker or a contactor.
7. Status settings: This class represents the settings for status-related attributes, such as alarm thresholds and display formats.

The document also defines various data types, including:

1. Integer
2. Enumerated
3. Boolean
4. String
5. Date and time

The text is organized into sections, each of which describes a specific class or attribute. The sections are numbered from 6 to 7.3.11.

If you have any specific questions about the content of this document, I'll be happy to help.

---

# Images and Diagrams

## Page 1

![Image 1 from page 1](images/iec61850-7-3{ed2.0}b_page1_img1_b56c5e21.jpeg)

**Image Type:** Diagram
**Description:** The image appears to be a grayscale technical diagram featuring a series of intersecting lines and curves. The lines are thin and white, creating a network-like structure against a light gray background. The lines are arranged in a way that suggests a pattern or a design, possibly related to a mathematical or geometric concept. The overall appearance is abstract and could be used in various technical contexts, such as in the field of mathematics, physics, or engineering.

**Key Elements:**
- Intersecting white lines forming a network.
- Curved lines that create a dynamic pattern.
- Light gray background providing contrast to the white lines.
- No specific labels or annotations are present in the image.

**Extracted Text:** No text detected

![Image 2 from page 1](images/iec61850-7-3{ed2.0}b_page1_img2_4462fda8.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white space with no discernible technical elements, components, or data. It lacks any text, symbols, or graphical elements that would typically be found in a technical diagram.

**Key Elements:** None

**Extracted Text:** No text detected

This image does not provide any technical information or data that can be analyzed or described further. It is a completely blank white space, which suggests it might be a placeholder or an error in the document. If this is part of a larger technical document, it is recommended to check the surrounding pages or sections for the intended technical content.

![Image 3 from page 1](images/iec61850-7-3{ed2.0}b_page1_img3_a6298b33.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with no additional elements or annotations. It consists of a single horizontal line that spans the entire width of the image. There are no branches, nodes, or other graphical elements that would suggest a more complex diagram such as a flowchart, network diagram, or schematic.

**Key Elements:**
- A single horizontal line
- No additional elements or annotations

**Extracted Text:** No text detected

This image is likely a placeholder or a basic representation of a concept that has not been fully developed or annotated. Further context or additional information would be needed to provide a more detailed technical analysis.

![Image 4 from page 1](images/iec61850-7-3{ed2.0}b_page1_img4_c7e85650.png)

**Image Type:** Diagram
**Description:** The image appears to be a simple, blank white diagram with no visible elements, components, or text. It is likely a placeholder or a starting point for a more detailed technical illustration that has not been filled in yet.
**Key Elements:** None
**Extracted Text:** No text detected

This image is currently empty and does not contain any technical information or data. It is likely intended as a template or a starting point for a more detailed technical diagram or illustration. If you have additional images or details to add, please provide them so that a comprehensive technical analysis can be conducted.

![Image 5 from page 1](images/iec61850-7-3{ed2.0}b_page1_img5_8ba88129.png)

**Image Type:** Diagram
**Description:** The image appears to be a simple, blank white diagram with no visible elements, components, or data. It lacks any text, symbols, or graphical representations that would indicate its purpose or content.
**Key Elements:** None
**Extracted Text:** No text detected

Given the lack of any discernible content, the image does not provide any technical information or data for analysis. If this is part of a larger document or presentation, it might be a placeholder or a section that has not been filled in yet. For further analysis, additional context or information would be required.

![Image 6 from page 1](images/iec61850-7-3{ed2.0}b_page1_img6_ec15285e.jpeg)

**Image Type:** Diagram
**Description:** The image appears to be a simple line diagram with a gradient background. The gradient transitions from a darker shade at the top to a lighter shade at the bottom. There are no distinct shapes, symbols, or text elements present in the image. The lines at the bottom suggest a possible representation of a grid or a series of parallel lines, but they are not connected or labeled in a way that indicates a specific technical function or data representation.

**Key Elements:**
- Gradient background: Darker at the top, lighter at the bottom.
- Parallel lines at the bottom: Suggest a grid or a series of lines, but no further details are provided.

**Extracted Text:** No text detected

![Image 7 from page 1](images/iec61850-7-3{ed2.0}b_page1_img7_7944118b.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white diagram. There are no visible elements, shapes, or text to describe or analyze. It seems to be a placeholder or a starting point for a more detailed diagram that has not yet been drawn or added.

**Key Elements:** None

**Extracted Text:** No text detected

This image does not contain any technical elements or data to analyze further. It is likely a placeholder or a blank space intended for a more detailed technical diagram or illustration.

![Image 8 from page 1](images/iec61850-7-3{ed2.0}b_page1_img8_5c5e6eb8.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with a white background. It consists of a single horizontal line that spans the width of the image. There are no additional elements, labels, or annotations present in the diagram.

**Key Elements:**
- A single horizontal line
- No text or labels
- No other components or features

**Extracted Text:** No text detected

This image is minimalistic and lacks any specific technical details or annotations that would provide further context or information. It seems to be a placeholder or a starting point for a more detailed technical diagram.

![Image 9 from page 1](images/iec61850-7-3{ed2.0}b_page1_img9_979bd150.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white diagram with no visible elements, text, or annotations. It is likely a placeholder or a starting point for a more detailed technical illustration.

**Key Elements:** 
- No elements or components are present in the image.
- No text or annotations are visible.

**Extracted Text:** No text detected

This image is currently devoid of any technical content or information. It serves as a blank canvas for future technical documentation or design. If you have additional images or details to include, please provide them for a more comprehensive analysis.

![Image 10 from page 1](images/iec61850-7-3{ed2.0}b_page1_img10_89ac3880.jpeg)

**Image Type:** Logo

**Description:** The image depicts a logo for the International Electrotechnical Commission (IEC). The logo consists of a blue square with the letters "IEC" in white, positioned in the upper right corner. Below the letters, there are three horizontal lines, and to the right of these lines, there is a small white circle.

**Key Elements:**
- Blue square background
- White "IEC" text
- Three horizontal lines
- Small white circle

**Extracted Text:** IEC

This logo is commonly used to represent the International Electrotechnical Commission, an organization that develops international standards for electrical and electronic products and systems.

![Image 11 from page 1](images/iec61850-7-3{ed2.0}b_page1_img11_dd4e7469.png)

**Image Type:** Blank Image
**Description:** The image appears to be a completely blank white space with no discernible content, elements, or text. It lacks any technical details, diagrams, or data that could be analyzed or described.
**Key Elements:** None
**Extracted Text:** No text detected

Since the image is completely blank, there is no technical content to analyze or describe. If you have another image or a different section of the document, please provide it for a detailed analysis.

![Image 12 from page 1](images/iec61850-7-3{ed2.0}b_page1_img12_b41b382b.png)

**Image Type:** Blank Image
**Description:** The image appears to be a completely blank white space with no discernible content, elements, or text. There are no technical elements, components, or data present in the image.
**Key Elements:** None
**Extracted Text:** No text detected

Since the image is completely blank, there is no technical analysis or description to provide. If you have another image or a different section of the document, please provide it for analysis.

![Image 13 from page 1](images/iec61850-7-3{ed2.0}b_page1_img13_6fb7a753.jpeg)

**Image Type:** Schematic

**Description:** The image appears to be a technical schematic, likely representing a network or system architecture. It features a series of interconnected lines and curves, which could symbolize data flow, connections, or pathways within a system. The lines are arranged in a way that suggests a layered or hierarchical structure, possibly indicating different levels or stages of a process.

**Key Elements:**
- Interconnected lines: These lines represent connections or pathways within the system.
- Curved and straight lines: The varying shapes and directions of the lines suggest different types of connections or stages in a process.
- Layered structure: The arrangement of lines in layers could indicate a hierarchical or multi-stage process.
- Abstract representation: The image is abstract and does not contain any specific symbols or labels that would provide more detailed information about the system or process it represents.

**Extracted Text:** No text detected

This image is likely used in technical documentation to illustrate a concept or process, but without additional context or labels, the specific details of the system or process it represents are not clear.

![Image 14 from page 1](images/iec61850-7-3{ed2.0}b_page1_img14_e13baaf4.jpeg)

**Image Type:** Schematic

**Description:** The image appears to be a technical schematic, likely representing a network or a system architecture. It features a series of interconnected lines and shapes that suggest a flow or connection pattern. The design is abstract and uses a grayscale color scheme, which is common in technical drawings to emphasize clarity and focus on the structure rather than color.

**Key Elements:**
- **Curved Lines:** These lines represent connections or pathways within the system. They are arranged in a way that suggests a hierarchical or layered structure.
- **Grid Patterns:** There are grid-like patterns in the background, which might represent a coordinate system or a grid layout for organizing the schematic.
- **Circular Elements:** There are circular shapes, possibly indicating nodes or points of interest within the system.
- **Linear Elements:** Straight lines are also present, which could represent direct connections or boundaries within the system.

**Extracted Text:** No text detected

This image is likely used in a technical document to illustrate a network or system architecture, providing a visual representation of the connections and structure within the system. The absence of text suggests that the focus is purely on the visual representation of the system.

![Image 15 from page 1](images/iec61850-7-3{ed2.0}b_page1_img15_1f906368.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document. It is designed to provide an overview of the document's content and its relevance to the field of power utility automation. The document is part of the IEC (International Electrotechnical Commission) series, specifically IEC 61850-7-3, which deals with communication networks and systems for power utility automation. The cover page includes the edition number (Edition 2.0) and the publication date (2010-12).

**Key Elements:**
- **Title:** "INTERNATIONAL STANDARD"
- **Standard Number:** IEC 61850-7-3
- **Edition Number:** Edition 2.0
- **Publication Date:** 2010-12
- **Document Type:** Communication networks and systems for power utility automation
- **Specific Part:** Part 7-3: Basic communication structure – Common data classes
- **Language:** The document is available in both English and French.
- **Customer Information:** Phil Young, TriangleMicroworks, with order number WS-2011-009821.
- **Copyright Notice:** The document is copyrighted by IEC, Geneva, Switzerland, with all rights reserved.

**Extracted Text:**
```
IEC 61850-7-3
Edition 2.0 2010-12
INTERNATIONAL STANDARD
Communication networks and systems for power utility automation —
Part 7-3: Basic communication structure — Common data classes
Réseaux et systèmes de communication pour l'automatisation des systèmes électriques —
Partie 7-3: Structure de communication de base — Classes de données communes
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 2

![Image 1 from page 2](images/iec61850-7-3{ed2.0}b_page2_img16_6c9b810e.jpeg)

**Image Type:** Warning Symbol

**Description:** The image depicts a standard warning symbol, commonly used in technical documentation and safety notices. It consists of a yellow triangle with a black border, and inside the triangle is a black exclamation mark, which is universally recognized as a symbol for caution or warning.

**Key Elements:**
- **Shape:** Triangle
- **Color:** Yellow (background) and Black (border and exclamation mark)
- **Symbol:** Exclamation mark

**Extracted Text:** No text detected

This image is typically used to indicate a potential hazard or cautionary message in technical documents, ensuring that the reader is aware of the importance of the information that follows.

---

## Page 3

![Image 1 from page 3](images/iec61850-7-3{ed2.0}b_page3_img17_98960f15.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white diagram with no visible elements, text, or annotations. It is likely a placeholder or a starting point for a technical document, possibly intended to be filled with content or graphics later.

**Key Elements:** 
- No elements or components are present in the image.
- No text or annotations are visible.

**Extracted Text:** 
No text detected

This image is currently empty and lacks any technical details or information. It is likely meant to be used as a template or a placeholder for further technical documentation.

![Image 2 from page 3](images/iec61850-7-3{ed2.0}b_page3_img18_b6146368.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with a white background. It consists of a single horizontal line that spans the entire width of the image. There are no additional elements, labels, or annotations present in the image.

**Key Elements:**
- A single horizontal line
- No text or labels
- No other components or features

**Extracted Text:** No text detected

This image is minimalistic and lacks any technical details or annotations that would provide further context or information. It seems to be a placeholder or a blank space in a larger document or presentation.

![Image 3 from page 3](images/iec61850-7-3{ed2.0}b_page3_img19_d7909f13.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with two horizontal lines intersecting at a point. The lines are straight and evenly spaced, suggesting a basic representation of a coordinate system or a simple geometric structure.

**Key Elements:**
- Two horizontal lines intersecting at a point.
- The lines are evenly spaced and straight.
- The intersection point is central to the diagram.

**Extracted Text:** No text detected

This diagram could be used to represent a basic coordinate system, a simple geometric structure, or a schematic for a technical document. The lack of additional context or labels makes it difficult to determine its exact purpose.

![Image 4 from page 3](images/iec61850-7-3{ed2.0}b_page3_img20_d03e59d9.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with a white background. It consists of a single horizontal line that spans the width of the image. There are no additional elements, labels, or annotations present in the diagram.

**Key Elements:**
- A single horizontal line
- No text or labels
- No additional components or features

**Extracted Text:** No text detected

This image is likely a placeholder or a blank diagram, possibly used as a template or a starting point for a more detailed technical illustration. Further context or additional elements would be necessary to provide a more comprehensive analysis.

![Image 5 from page 3](images/iec61850-7-3{ed2.0}b_page3_img21_5a2c3163.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with a white background. It consists of a single horizontal line that spans the entire width of the image. There are no additional elements, labels, or annotations present in the diagram.

**Key Elements:**
- A single horizontal line
- No text or labels
- No additional graphical elements or annotations

**Extracted Text:** No text detected

This image is likely a placeholder or a blank diagram, possibly used as a template or a starting point for a more detailed technical illustration. Further context or additional elements would be necessary to provide a more comprehensive analysis.

![Image 6 from page 3](images/iec61850-7-3{ed2.0}b_page3_img22_426facc5.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with no additional elements or annotations. It consists of a single horizontal line that spans the entire width of the image. There are no branches, nodes, or other graphical elements present, which suggests it might be a placeholder or a basic representation of a concept that has not been fully developed or annotated.

**Key Elements:**
- A single horizontal line
- No additional elements or annotations
- No text or labels

**Extracted Text:** No text detected

This image is likely a placeholder or a basic representation of a concept that has not been fully developed or annotated. It could be used in a technical document to indicate a point of reference or a starting point for further elaboration.

![Image 7 from page 3](images/iec61850-7-3{ed2.0}b_page3_img23_15eeef30.jpeg)

**Image Type:** Logo

**Description:** The image depicts a logo for the International Electrotechnical Commission (IEC). The logo consists of the letters "IEC" in a sans-serif font, prominently displayed in white against a blue background. Below the letters, there are three horizontal lines, which are part of the IEC's logo design, and a small white circle to the right of the lines.

**Key Elements:**
- The letters "IEC" in white.
- The blue background.
- Three horizontal lines below the letters.
- A small white circle to the right of the lines.

**Extracted Text:** No text detected

![Image 8 from page 3](images/iec61850-7-3{ed2.0}b_page3_img24_41f268ec.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple line diagram with no additional elements or annotations. It consists of a single horizontal line that spans the entire width of the image. There are no branches, nodes, or other graphical elements present, which suggests it might be a placeholder or a basic representation of a concept or a step in a process.

**Key Elements:**
- A single horizontal line
- No additional elements or annotations
- No text or labels

**Extracted Text:** No text detected

This image is likely used as a placeholder or a basic representation in a technical document, possibly to indicate a step or a concept that will be elaborated upon in subsequent sections.

![Image 9 from page 3](images/iec61850-7-3{ed2.0}b_page3_img25_d5f12b3b.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white diagram with no visible elements, text, or annotations. It is likely a placeholder or a starting point for a more detailed technical diagram that has not yet been filled in or created.

**Key Elements:** None

**Extracted Text:** No text detected

This image is currently empty and does not contain any technical elements or data. It is likely intended as a template or a space for future technical documentation.

![Image 10 from page 3](images/iec61850-7-3{ed2.0}b_page3_img26_c7e85650.png)

**Image Type:** Diagram
**Description:** The image appears to be a simple, blank white diagram with no visible elements, components, or text. It is likely a placeholder or a starting point for a technical document, possibly intended to be filled with content later.
**Key Elements:** None
**Extracted Text:** No text detected

This image is currently empty and does not contain any technical information or data. It is likely meant to be used as a template or a starting point for a more detailed technical diagram or illustration.

![Image 11 from page 3](images/iec61850-7-3{ed2.0}b_page3_img27_8ba88129.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank white space with no discernible technical elements, components, or data. It lacks any text, symbols, or graphical representations that would typically be found in a technical diagram.

**Key Elements:** None

**Extracted Text:** No text detected

This image does not provide any technical information or data that can be analyzed or described further. If you have another image or a different section of the document, please provide it for analysis.

![Image 12 from page 3](images/iec61850-7-3{ed2.0}b_page3_img28_fb5818eb.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document published by the International Electrotechnical Commission (IEC). It is titled "IEC 61850-7-3" and is part of a series related to communication networks and systems for power utility automation. The document is in its second edition, published in 2010-12. The cover page includes the IEC logo, the edition information, and a copyright notice.

**Key Elements:**
- **Title:** IEC 61850-7-3
- **Edition:** 2.0 (2010-12)
- **Standard Description:** Communication networks and systems for power utility automation – Part 7-3: Basic communication structure – Common data classes
- **Language:** The document is available in both English and French.
- **Publisher:** International Electrotechnical Commission (IEC)
- **ISBN:** 978-2-88912-258-5
- **Price Code:** XD
- **ICS Code:** 33.200 (International Classification for Standards)
- **Copyright Notice:** The document is copyrighted by the IEC, Geneva, Switzerland, and all rights are reserved.

**Extracted Text:**
```plaintext
IEC 61850-7-3
Edition 2.0 2010-12

INTERNATIONAL
ELECTROTECHNICAL
COMMISSION
COMMISSION
ELECTROTECHNIQUE
INTERNATIONALE

Communication networks and systems for power utility automation –
Part 7-3: Basic communication structure – Common data classes
Réseaux et systèmes de communication pour l'automatisation des systèmes électriques –
Partie 7-3: Structure de communication de base – Classes de données communes

INTERNATIONAL
ELECTROTECHNICAL
COMMISSION
COMMISSION
ELECTROTECHNIQUE
INTERNATIONALE

PRICE CODE
CODE PRIX
XD

ICS 33.200
ISBN 978-2-88912-258-5

Customer Real Name: John Doe
Order No: W1234567890
Order Date: 2023-01-01
Copyright © 2010-2012 by the International Electrotechnical Commission (IEC), Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 9

![Image 1 from page 9](images/iec61850-7-3{ed2.0}b_page9_img29_32688e06.png)

**Image Type:** Document Page
**Description:** This image is a page from a technical standard document, specifically from the IEC (International Electrotechnical Commission) series, as indicated by the copyright and document identifier (61850-7-3 © IEC:2010). The page appears to be part of a larger document, as it references other parts of the series and provides information about the voting process for the approval of the standard.

**Key Elements:**
- Document identifier: 61850-7-3 © IEC:2010
- Reference to other parts of the IEC 61850 series
- Information about the voting process for the approval of the standard
- Reference to the ISO/IEC Directives, Part 2
- General title of the series: "Communication networks and systems in substations"
- Revised title of the series: "Communication networks and systems for power utility automation"
- Decision on the stability of the publication's contents until a certain date
- Possible future states of the publication: reconfirmed, withdrawn, replaced by a revised edition, or amended
- Importance of color printing for the document

**Extracted Text:**
61850-7-3 © IEC:2010 -7- The text of this standard is based on the following documents: Full information on the voting for the approval of this standard can be found in the report on voting indicated in the above table. This publication has been drafted in accordance with the ISO/IEC Directives, Part 2. A list of all parts in the IEC 61850 series, published under the general title: Communication networks and systems for power utility automation, can be found on the IEC website. The general title of the series was Communication networks and systems in substations. To address the extension of the scope of IEC 61850, it has been changed to Communication networks and systems for power utility automation. The committee has decided that the contents of this publication will remain unchanged until the stability date indicated on the IEC web site under "http://webstore.iec.ch" in the data related to the specific publication. At this date, the publication will be * reconfirmed, + withdrawn, + replaced by a revised edition, or * amended. IMPORTANT - The ‘colour inside’ logo on the cover page of this publication indicates that it contains colours which are considered to be useful for the correct understanding of its contents. Users should therefore print this document using a colour printer.

---

## Page 10

![Image 1 from page 10](images/iec61850-7-3{ed2.0}b_page10_img30_d9df165c.png)

**Image Type:** Document Page
**Description:** The image is a page from a technical document, specifically from a set of specifications related to IEC 61850, which is a standard for communication in substation automation systems. The page is titled "INTRODUCTION" and provides an overview of the layered substation communication architecture defined by IEC 61850. It discusses the abstract definitions of classes and services, the mapping of these definitions to communication stacks, and the use of common data classes for various applications in the power system.

**Key Elements:**
- Layered substation communication architecture
- Abstract definitions of classes and services
- Independence from specific protocol stacks and objects
- Mapping of abstract classes and services to communication stacks
- Common data classes related to substations, hydro power, and distributed energy resources
- Use of IEC 61850 modeling concepts
- Definition of compatible dataObject classes
- Accessing SubDataObjects, DataAttributes, or SubAttributes using services defined in IEC 61850-7-2
- Specification of abstract common data class and constructed attribute class definitions
- Mapping of abstract definitions into concrete object definitions for specific protocols (e.g., MMS, ISO 9506 series)
- Common data classes for service tracking defined in IEC 61850-7-2

**Extracted Text:**
```
-8- 61850-7-3 © IEC:2010 INTRODUCTION

This document is part of a set of specifications, which details layered substation communication architecture. This architecture has been chosen to provide abstract definitions of classes and services such that the specifications are independent of specific protocol stacks and objects. The mapping of these abstract classes and services to communication stacks is outside the scope of IEC 61850-7-x and may be found in IEC 61850-8-x (station bus) and IEC 61850-9-x (process bus).

IEC 61850-7-1 gives an overview of this communication architecture. This part of IEC 61850 defines constructed attributed classes and common data classes related to applications in the power system using IEC 61850 modeling concepts like substations, hydro power or distributed energy resources. These common data classes are used in IEC 61850-7-4 to define compatible dataObject classes. The SubDataObjects, DataAttributes or SubAttributes of the instances of dataObject are accessed using services defined in IEC 61850-7-2.

This part of IEC 61850 is used to specify the abstract common data class and constructed attribute class definitions. These abstract definitions are mapped into concrete object definitions that are to be used for a particular protocol (for example MMS, ISO 9506 series).

Note that there are common data classes used for service tracking, that are defined in IEC 61850-7-2.
```

---

## Page 13

![Image 1 from page 13](images/iec61850-7-3{ed2.0}b_page13_img31_dfdca0e2.png)

**Image Type:** Table

**Description:** The image is a technical table from IEC 61850-7-3, which outlines the conditions and attributes for various configuration data attributes in the context of IEC 61850 standards. The table is structured to provide detailed conditions under which certain attributes are mandatory, optional, or not required, depending on the presence of other attributes or conditions.

**Key Elements:**
- Abbreviation: Each row starts with an abbreviation that represents a specific configuration attribute.
- Condition: The column provides the conditions under which the corresponding attribute is present, optional, or not required.

**Extracted Text:**

| Abbreviation | Condition |
|--------------|-----------|
| AC_SCAV      | The presence of the configuration data attribute depends on the presence of i and f of the Analog Value of the data attribute to which this configuration attribute relates. For a given data object, that attribute 1) shall be present, if both i and f are present, 2) shall be optional if only ‘is present, and 3) is not required if only fis present. NOTE. If only jis present in a device without floating point capabilities, the configuration parameter may be exchanged offline. |
| AC_ST        | The attribute is mandatory, if the controllable status class supports status information |
| AC_CO_O      | If the controllable status class supports control, this attribute is available and an optional attribute. |
| AC_CO_SBO    | If the controllable status class supports control and if the control model supports the values "sbo-with-normal-security” or "sbo-with-enhanced-security" or both, that attribute shall be mandatory. |
| AC_SG_M      | The attribute is mandatory, if this data shall be member of a setting group. |
| AC_SG_O      | The attribute is optional, if this data shall be member of a setting group. |
| AC_SG_C1     | One of the attributes is mandatory, if this data shall be member of a setting group. |
| AC_NSG_M     | The attribute is mandatory, if this data shall be a setting outside a setting group. |
| AC_NSG_O     | The attribute is optional, if this data shall be a setting outside a setting group. |
| AC_NSG_C1    | One of the attributes is mandatory, if this data shall be a setting outside a setting group. |
| AC_RMS_M     | The attribute is mandatory when the harmonics reference type is rms. |
| AC_CLC_O     | The attribute shall be optional, when the calculation type (according to data CleMth) for this LN is Peak fundamental or RMS fundamental. The attribute shall not be available, if CleMth is TRUE RMS. |

**Additional Information:**
- The table is part of a larger document that discusses constructed attribute classes for use in common data classes (CDC) in Clause 7.
- IEC 61850-7-1 provides an overview of all IEC 61850-7 documents and describes the basic notation used in IEC 61850-7-3.
- The common ACSI type "TimeStamp" is specified in IEC 61850-7-2.
- The document also includes a section on quality, with an overview of quality types as defined in Table 2.

This table is crucial for engineers and technical professionals working with IEC 61850 standards, as it provides a clear and structured reference for understanding the conditions under which various configuration attributes are applicable.

---

## Page 17

![Image 1 from page 17](images/iec61850-7-3{ed2.0}b_page17_img32_a5e4aa99.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that illustrates the quality identifiers in both a single client-server relationship and a multiple client-server relationship. It uses a combination of text and graphical elements to explain how the quality of information is identified and handled in these contexts.

**Key Elements:**
- **Client**: Represents the entity requesting information.
- **Server**: Represents the entity providing information.
- **Communication Network**: The medium through which information is exchanged.
- **Input Unit**: The component that receives and processes the information.
- **Information Source**: The connection of the process information to the system.
- **Quality Identifiers**: Indicators of the quality of the information, such as "Invalid," "Questionable," "Old Data," and "Substituted."

**Extracted Text:**

61850-7-3 © IEC:2010 - 15 - 6.2.7 Quality in the client server context

Figure 1 — Quality identifiers in a single client-server relationship

The quality identifier shall reflect the quality of the information in the server, as it is supplied to the client. Figure 1 shows potential sources that may influence the quality in a single client-server relationship. "Information source" is the (hardwired) connection of the process information to the system. The information may be invalid or questionable as indicated in Figure 1. Further abnormal behaviour of the information source may be detected by the input unit. In that case, the input unit may keep the old data and flag it accordingly.

In a multiple client-server relationship, as shown in Figure 2, information may be acquired over a communication link (with client B). If that communication link is broken, client B will detect that error situation and qualify the information as questionable/old data.

Figure 2 — Quality identifiers in a multiple client-server relationship

In the multiple client-server relationship, the quality of the data received from server A shall reflect both the quality of the server B (acquired with client B) as well as its own quality. Therefore, handling of prioritisation of quality from different levels may require further specification beyond that included in this standard. For the identifier validity, the value invalid shall dominate over the value questionable, since this is the worst case. For the identifier source, the higher level of the multiple client-server relationship shall dominate over the lower level.

EXAMPLE Let A be the higher level and B the lower level. The quality from server B is invalid. If now the communication fails (questionable, oldData) between server B and client B, the quality will remain invalid and not become questionable, since the last information was not correct. Server A therefore will report the information as invalid.

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved. This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel: +41 22 919 02 11

---

## Page 19

![Image 1 from page 19](images/iec61850-7-3{ed2.0}b_page19_img33_f216cbf2.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the interaction of substitution and validity in a system, as described in Figure 3. The diagram is divided into four cases (Case A, Case B, Case C, and Case D) to show different scenarios involving station, bay, and process levels. Each case includes a client (CL) and a server (Se) interaction, with various conditions such as substitution, validity, and input blocking.

**Key Elements:**
- **Cases:** Case A, Case B, Case C, Case D
- **Levels:** Station level, Bay level, Process level
- **Components:**
  - **CL (Client):** Represents the client component in the system.
  - **Se (Server):** Represents the server component in the system.
- **Conditions:**
  - **Substitution:** Indicates a substitution of data or state.
  - **Validity:** Refers to the validity of the data or state.
  - **Input is blocked:** Indicates that input is not allowed or is blocked.
  - **Communication failure:** Indicates a failure in communication between components.
- **Flow:** The diagram shows the flow of data and conditions from the station level to the process level, with interactions between the client and server at each level.

**Extracted Text:**
```
61850-7-3 © IEC:2010 -17- Case A Case B
Station Validity = quest Substituted (oldData)
Bay <> eed Validity = quest Substituted (oldData)
Process Ge> se DZ ZW Input is blocked Input is blocked
Case C Case D
Station ie >) 2) Substituted, Substituted validity = quest (oldData)
Bay Css) CSe) Substitution Communication Communication failure failure
Process Ge) Ge) level Ge) ‘Substitution Ge) Substitution ZZ Z Input is blocked Input is blocked
IEC 2550/10
Key
CL client
Se server
Figure 3 - Interaction of substitution and validity
```

This technical analysis provides a clear understanding of the diagram's purpose and the conditions it illustrates, which can be useful for technical documentation and search.

---

## Page 20

![Image 1 from page 20](images/iec61850-7-3{ed2.0}b_page20_img34_9b3e130f.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically from a section detailing the configuration and representation of analogue values in a system. It includes definitions, formulas, and configurations related to the conversion between integer and floating-point representations of measured values.

**Key Elements:**
- **Analogue Value Representation:** The document discusses how analogue values can be represented as either an integer (attribute i) or a floating-point value (attribute f).
- **Conversion Formulas:** Formulas are provided for converting between the integer representation (i) and the process value (pVal), and between the floating-point representation (f) and the process value (pVal).
- **Configuration Table:** Table 5 defines the configuration of the analogue value type, including attributes such as scaleFactor and offset.
- **Error Handling:** The document notes that the conversion should be true within acceptable error margins when all attributes (i, scaleFactor, offset, and f) are present.
- **ScaleFactor and Offset:** These are defined as attributes used to scale the integer and floating-point representations of the measured values.

**Extracted Text:**

```
18- 61850-7-3 © IEC:2010

6.3 Analogue value
Analogue value type shall be as defined in Table 4.

Table 4 – Analogue value

Analogue values may be represented as a basic type INTEGER (attribute i) or as FLOATING POINT (attribute f). At least one of the attributes shall be used. If both i and f exist, the application in the server shall insure that both values remain consistent. The latest value set by the communication service shall be used to update the other value. As an example, if xxx.f is written, the application shall update xxx.i accordingly.

The measured values represent primary process process values.

i: The value of i shall be an integer representation of the measured value. The formula to convert between i and the process value (pVal) shall be:
pVal = (i x scaleFactor) + offset

It shall be true within acceptable error when i, scaleFactor, offset and f are all present.

f: The value of f shall be the floating point representation of the measured value. The formula to convert between f and the process value shall be:
pVal = f x 10^units_multiplier

NOTE The reason for both integer and floating point representation is so that IEDs without FLOATING POINT capabilities are enabled to support analogue values. In this case, the scaleFactor and offset may be exchanged offline between clients and servers.

6.4 Configuration of analogue value
Configuration of analogue value type shall be as defined in Table 5.

Table 5 – Configuration of analogue value

ScaledValueConfig type definition

Attribute name | Attribute type | Value/value range | M/O/C
scaleFactor | FLOAT32 | | M
offset | FLOAT32 | | M

This constructed attribute class shall be used to configure the INTEGER value representation of the analogue value. The formula for conversion between integer and floating point value is given in 6.3.

scaleFactor: the value of scaleFactor shall be the scaling factor.
```

This document provides a comprehensive technical description of how to configure and convert between integer and floating-point representations of analogue values, ensuring consistency and error-free conversion within a system.

---

## Page 22

![Image 1 from page 22](images/iec61850-7-3{ed2.0}b_page22_img35_6b6bbe47.png)

**Image Type:** Technical Document Page

**Description:** This image is a page from a technical document, specifically from a standard or specification related to pulse configuration and command output. It includes definitions, tables, and diagrams that detail how to configure pulse outputs in response to commands. The document is structured with sections and subsections, each providing specific information about the configuration parameters and their definitions.

**Key Elements:**
- **Section 6.7: Pulse Configuration** - Defines the PulseConfig type used to configure the output pulse generated with a command.
- **Table 8: Pulse Configuration** - Lists the attributes and their definitions for PulseConfig type.
- **cmdQual Attribute** - Defines whether the control output is a pulse output or a persistent output.
- **onDur, offDur, numPls Attributes** - Define the duration and number of pulses for a pulsed output.
- **Figure 4: Configuration of Command Output Pulse** - A diagram illustrating the configuration of the output pulse shape based on onDur, offDur, and numPls.
- **Section 6.8: Originator** - Defines the Originator type as per Table 9.

**Extracted Text:**

```
20- 61850-7-3 © IEC:2010

The posVal shall contain the step position, the transInd shall indicate that the equipment is in a transient state.

6.7 Pulse configuration
Pulse configuration type is used to configure the output pulse generated with a command and shall be as defined in Table 8.

Table 8 – Pulse configuration configuration

PulseConfig type definition

| Attribute name | Attribute type | Value/Value range | M/O/C |
|----------------|----------------|-------------------|--------|
| cmdQual        | ENUMERATED     | pulse | persistent | M |
| onDur          | INT32U         |                  | M      |
| offDur         | INT32U         |                  | M      |
| numPls         | INT32U         |                  | M      |

cmdQual: this identifier shall define if the control output is a pulse output or if it is a persistent output. If it is set to pulse, then the duration of the pulse shall be defined with the identifiers onDur, offDur and numPls. If it is set to persistent, the output stays in the state indicated in the operate service.

onDur, offDur, numPls: as the result of receiving an Operate service, a pulsed output may be generated to the on or off input of a switching device. The shape of this output is defined by onDur, offDur and numPls according to Figure 4. NumPls shall specify the number of pulses that are generated. onDur shall specify the on duration of the pulse, offDur specifies the duration between two pulses. onDur and offDur shall be specified in ms; a value of 0 ms shall specify that the duration is locally defined.

1 2
numPls

Figure 4 – Configuration of command output pulse

6.8 Originator
Originator type shall be as defined in Table 9.
```

This document is part of a larger technical specification, likely related to industrial automation or control systems, as indicated by the IEC (International Electrotechnical Commission) standard reference.

---

## Page 23

![Image 1 from page 23](images/iec61850-7-3{ed2.0}b_page23_img36_53575287.png)

**Image Type:** Table

**Description:** The image is a technical document page containing definitions and explanations for various attributes related to the originator, unit, and vector types in a system. It includes tables and explanations for each attribute, their types, and their value ranges. The document is structured to provide a clear definition of each term and its usage within the system.

**Key Elements:**
- **Table 9 – Originator:** Defines the originator type and its attributes, including `orCat` and `orldent`.
- **Table 10 – Values for orCat:** Lists the possible values for `orCat` and their explanations.
- **Table 11 – Unit:** Defines the unit type and its attributes, including `SIUnit` and `multiplier`.
- **Table 12 – Vector:** Defines the vector type and its attributes.
- **Attribute Definitions:** Each table includes columns for `Attribute name`, `Attribute type`, `Value/Value range`, and `M/O/C` (which likely stands for Mandatory/Optional/Conditional).

**Extracted Text:**

```
61850-7-3 © IEC:2010 -21- Table 9 – Originator
Originator type definition
Attribute name Attribute type Value/Value range M/O/C
orCat ENUMERATED not-supported | bay-control | station-control | remote-control | automatic-bay | automatic-station | automatic-remote | maintenance | process M
orldent OCTET STRING64 M

orCat: The originator category shall specify the category of the originator. An explanation of the values for orCat is given in Table 10.

Table 10 – Values for orCat
Value Explanation
not-supported That value shall not be used
bay-control Control operation issued from an operator using a client located at bay level
station-control Control operation issued from an operator using a client located at station level
remote-control Control operation issued from a remote operator outside the substation (for example network control center)
automatic-bay Control operation issued from an automatic function at bay level
automatic-station Control operation issued from an automatic function at station level
process Status change occurred without control action (for example external trip of a circuit breaker or failure inside the breaker)

orldent: the originator identification shall show the identification of the originator. The value of NULL shall be reserved to indicate that the originator of a particular action is not known.

6.9 Unit definition
Unit type shall be as defined in Table 11.

Table 11 – Unit
Unit type definition
Attribute name Attribute type Value/Value range M/O/C
SIUnit ENUMERATED According to Tables A.1 to A.4 in Annex A M
multiplier ENUMERATED According to Table A.5 in Annex A O

SIUnit: shall define the SI unit according to Annex A.
multiplier: shall define the multiplier value according to Annex A. The default value is 0 (i.e. multiplier = 1).

6.10 Vector definition
Vector type shall be as defined in Table 12.

Table 12 – Vector
Vector type definition
Attribute name Attribute type Value/Value range M/O/C
```

This document is part of a larger technical specification, likely related to a control system or automation framework, and provides detailed definitions for various attributes used in the system's configuration and operation.

---

## Page 24

![Image 1 from page 24](images/iec61850-7-3{ed2.0}b_page24_img37_cdc4e4fb.png)

**Image Type:** Technical Document Page

**Description:** This image is a page from a technical document, specifically from a standard or specification document related to IEC (International Electrotechnical Commission) standards. The document appears to be focused on defining various types of data structures and their attributes, particularly in the context of vector, point, and cell definitions. The content is structured into sections with numbered subsections, each defining a specific type or attribute.

**Key Elements:**
- **Vector type definition:** Defines attributes such as `mag` (magnitude) and `ang` (angle) with their respective types and value ranges.
- **Point definition:** Defines the `Point` type, which is used to represent points in a two- or three-dimensional coordinate system.
- **CtlModels definition:** Defines the `CtlModels` type, which is an enumerated type with various status and security options.
- **SboClasses definition:** Defines the `SboClasses` type, which is also an enumerated type with operational options.
- **Cell definition:** Defines the `Cell` type, which is used to define a rectangle area in a two-dimensional environment and can describe a range within a one-dimensional environment.

**Extracted Text:**

```
-22- 61850-7-3 © IEC:2010
mag: the magnitude of the complex value.
ang: the angle of the complex value. The SIUnit shall be degrees and the unit multiplier is 1. The angle reference is defined in the context where the Vector type is used.
6.11 Point definition
Point type shall be as defined in Table 13 and is used to represent points in a two- or three-dimensional coordinates system.
Table 13 - Point
Point type definition
Attribute name | Attribute type | Value/Value range | M/O/C
xVal | FLOAT32 | M
yVal | FLOAT32 | M
zVal | FLOAT32 | O
xVal: the x value of a point.
yVal: the y value of a point.
zVal: the z value of a point.
6.12 CtlModels definition
CtlModels type is defined as follows:
ENUMERATED (status-only | direct-with-normal-security | sbo-with-normal-security | direct-with-enhanced-security | sbo-with-enhanced-security)
Details are provided in Clause 8.
6.13 SboClasses definition
SboClasses type is defined as follows:
ENUMERATED (operate-once | operate-many)
Details are provided in Clause 8.
6.14 Cell
Cell type is used to define a rectangle area in a two-dimensional environment and shall be defined as in Table 14. Cell type can as well be used to describe a range within a one-dimensional environment. For details, see Figure 5.
```

This document is part of a larger technical specification, likely related to standards for data representation and handling in a specific domain, such as electrical engineering or automation systems.

---

## Page 25

![Image 1 from page 25](images/iec61850-7-3{ed2.0}b_page25_img38_f112fbb5.png)

**Image Type:** Table and Diagram

**Description:** The image consists of a table and a diagram that defines the attributes and types of a "Cell" in a technical context. The table provides a detailed definition of the attributes associated with a cell, including their types and value ranges. The diagram visually represents the cell with labeled coordinates for the corners.

**Key Elements:**
- **Table 14 – Cell:**
  - **Attribute Name:** xStart, xEnd, yStart, yEnd
  - **Attribute Type:** FLOAT32
  - **Value/Value Range:** Not specified in the table
  - **M/O/C:** M (Mandatory), O (Optional), C (Conditional)
- **Diagram:**
  - A square with labeled coordinates: xStart, yStart, xEnd, yEnd.

**Extracted Text:**
```
61850-7-3 © IEC:2010 -23- Table 14 – Cell

Cell type definition

Attribute name | Attribute type | Value/Value range | M/O/C
---------------------------------------------------------
xStart         | FLOAT32       |                   | M
xEnd           | FLOAT32       |                   | O
yStart         | FLOAT32       |                   | O
yEnd           | FLOAT32       |                   | O

xStart: the x value of the lower left corner of the square.
xEnd: the x value of the upper right corner of the square. That component shall not be present to indicate infinity in the direction of the x axis.
yStart: The y value of the lower left corner of the square. That component shall not be present, if only a one-dimensional range needs to be described.
yEnd: The y value of the upper right corner of the square. That component shall not be present, if only a one-dimensional range needs to be described or to indicate infinity in the direction of the y axis.

Figure 5 – Cell definition

6.15 CalendarTime definition
CalendarTime type is used to define a time setting in reference to the calendar and shall be as defined in Table 15. That constructed attribute class allows the specification of times like the last day of the month or the second Sunday in March at 03.00h.
```

**Technical Analysis:**
The image provides a structured definition of a "Cell" in a technical document, likely related to a standard or specification by the International Electrotechnical Commission (IEC). The table outlines the attributes of a cell, specifying their types (FLOAT32) and their mandatory, optional, or conditional nature (M, O, C). The diagram visually represents the cell with coordinates for its corners, aiding in the understanding of the cell's dimensions and position. The extracted text further clarifies the definitions of the attributes and introduces the concept of a "CalendarTime" type, which is used to define time settings in relation to the calendar. This type allows for the specification of times such as the last day of the month or the second Sunday in March at 03:00.

---

## Page 26

![Image 1 from page 26](images/iec61850-7-3{ed2.0}b_page26_img39_b40a13de.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically Table 15 titled "CalendarTime." It defines the attributes and their types, value ranges, and meanings for a CalendarTime type. The table is structured to provide a detailed definition of the CalendarTime type, which is used to represent time settings based on calendar elements.

**Key Elements:**
- **Attribute Name:** Lists the names of the attributes.
- **Attribute Type:** Specifies the type of each attribute (e.g., INT16U, ENUMERATED).
- **Value/Value Range:** Provides the possible values or ranges for each attribute.
- **M/O/C:** Indicates whether the attribute is mandatory (M), optional (O), or conditional (C).

**Extracted Text:**

```
Table 15 – CalendarTime
CalendarTime type definition

Attribute name | Attribute type | Value/Value range | M/O/C
--------------|----------------|-------------------|------
occ            | INT16U         | Time, WeekDay, WeekOfYear, DayOfMonth, DayOfYear | M
occType        | ENUMERATED     | Time, WeekDay, WeekOfYear, DayOfMonth, DayOfYear | M
occPer         | ENUMERATED     | Hour, Day, Week, Month, Year | M
weekDay        | ENUMERATED     | reserved, Monday, Tuesday, ... Sunday | M
month          | ENUMERATED     | reserved, January, February, ... December | M
day            | INT8U          | 1..31             | M
hr             | INT8U          | 0..23             | M
mn             | INT8U          | 0..59             | M

occ: Occurrence of a calendar element. The value 0 is used to indicate the last. For the identification of week numbers, week number 01 shall always be the first week in January (according to definition of UN / CEFACT).

occType: the kind of calendar element that is used for the occurrence.

occPer: the repetition period of a calendar-based time setting.

weekDay: the weekday.

month: the month.

day: the day.

hr: the hour.

mn: the minute.

The semantic interpretation of the attributes is given in Table 16.

Table 16 – Semantic interpretation of calendar time settings

occPer | occType | interpretation of calendar time settings
--------|---------|-----------------------------------------
HourPer | Time    | At <mn> minute every every hour
Day     | Time    | At <hr>, <mn> every day
Week    | WeekDay | At <weekDay>, <hr>, <mn> every week
Month   | WeekDay | At <occ>, <weekDay>, <hr>, <mn> every month
Month   | DayOfMonth | At <occ>, <hr>, <mn> every month
Year    | WeekDay | At <occ>, <weekDay>, <hr>, <mn> every year
Year    | WeekOfYear | At week <occ>, <weekDay>, <hr>, <mn> every month
Year    | DayOfYear | At <occ>, <hr>, <mn> every year
```

This table provides a comprehensive definition of the CalendarTime type, which is crucial for understanding how to interpret and use calendar-based time settings in a technical context.

---

## Page 34

![Image 1 from page 34](images/iec61850-7-3{ed2.0}b_page34_img40_f3cb5dd6.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the structure and specifications of a "Basic measurand information template" as defined in IEC 61850-7-3. The table is divided into several sections, including data attributes, types, functional constraints (FC), trigger operations (TrgOp), value ranges, and modes of operation (M/O/C). It also includes a section on services, detailing the inheritance and specialization of services from IEC 61850-7-2.

**Key Elements:**

- **Data Attribute Name:** Identifies the specific attribute within the measurand information template.
- **Type:** Specifies the type of data attribute, such as VISIBLE STRING, UNICODE STRING, etc.
- **FC (Functional Constraint):** Indicates the functional constraints applicable to the data attribute.
- **TrgOp (Trigger Operation):** Describes the trigger operation associated with the data attribute.
- **Value/Value Range:** Specifies the value or range of values for the data attribute.
- **M/O/C (Mode of Operation):** Indicates the mode of operation for the data attribute.
- **Services:** Lists the services defined in IEC 61850-7-2 that are inherited and specialized for the measurand information template.

**Extracted Text:**

```
-32- 61850-7-3 © IEC:2010

VSS class
Data attribute name
stVal
q
t
d
dU
cdcNs
cdcNs
dataNs
Data attribute name
DataName
DataAttribute
Services (see IEC 61850-7-2)
Service model of IEC 61850-7-2
Service
Service applies to Attr with FC
Remark
GenCommonDataClass
SetDataValues
DC, CF, SV, BL
All
GetDataValues
All
GetDataDefinition
All
GetDataDirectory
All
Data set model
GetDataSetValues
All
SetDataSetValues
DC, CF, SV, BL
Reporting model
Report
All
GSE model
SendGOOSEMessage
MX
Sampled values model
SendMSVMessage
MX
SendUSVMessage
MX
```

This table is part of a larger technical document that defines the structure and specifications for measurand information in the context of IEC 61850-7-3, a standard for communication networks and systems in power utility automation.

---

## Page 35

![Image 1 from page 35](images/iec61850-7-3{ed2.0}b_page35_img41_968dafe6.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which is related to the IEC 61850 standard for communication networks and systems in power utility automation. This particular table, Table 30, defines the common data class "measured value" (MV). The table outlines various attributes of the MV class, including their types, data classes, and possible values or ranges.

**Key Elements:**
- **MV Class:** The main class being defined.
- **Data Attribute Name:** The specific attributes within the MV class.
- **Type:** The data type of the attribute (e.g., AnalogueValue, ENUMERATED, BOOLEAN).
- **FC (Function Code):** Indicates the function code associated with the attribute.
- **TrgOp (Trigger Operation):** Describes the operation that triggers the attribute.
- **Value/Value Range:** The possible values or ranges for the attribute.
- **M/O/C (Mandatory/Optional/Conditional):** Indicates whether the attribute is mandatory, optional, or conditional.

**Extracted Text:**

```
61850-7-3 © IEC:2010 -33- 7.4.2 Measured value (MV)
Table 30 defines the common data class "measured value".

Table 30 – Measured value

MV class
Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

DataAttribute

measured attributes

instMag
AnalogueValue
MX
MX
dchg
O
mag
AnalogueValue
MX
dchg, dupd
normal|high|low|high-high|low-low
M
range
ENUMERATED
MX
dchg
normal|high|low|high-high|low-low
O
q
Quality
MX
MX
dchg
normal|high|low|high-high|low-low
M
q
Quality
MX
MX
dchg
normal|high|low|high-high|low-low
O
t
TimeStamp
MX
MX
dchg
normal|high|low|high-high|low-low
M

substitution and blocked

subEna
BOOLEAN
SV
SV
PICS_SUBST
subMag
AnalogueValue
SV
SV
PICS_SUBST
subQ
Quality
SV
SV
PICS_SUBST
subID
VISIBLE STRING64
SV
SV
PICS_SUBST
blkEna
BOOLEAN
SV
SV
PICS_SUBST

configuration, description and extension

units
Unit
CF
dchg
see Annex A
O
db
INT32U
CF
dchg
0 ... 100 000
O
zeroDb
INT32U
CF
dchg
0 ... 100 000
O
sVC
ScaledValueConfig
CF
dchg
AC_SCAV
range
RangeConfig
CF
dchg
GC_CON_range
smpRate
INT32U
CF
dchg
O
d
VISIABLE STRING255
DC
Text
dU
UNICODE STRING255
DC
O
cdcNs
VISIBLE STRING255
EX
AC_DLNDA_M
cdcName
VISIBLE STRING255
EX
AC_DLNDA_M
dataNs
VISIBLE STRING255
EX
AC_DLN_M

Services
As defined in Table 29.
```

This table provides a comprehensive overview of the attributes and their specifications within the MV class, which is crucial for understanding the data model in IEC 61850 for power systems.

---

## Page 37

![Image 1 from page 37](images/iec61850-7-3{ed2.0}b_page37_img42_92ce8894.png)

**Image Type:** Table
**Description:** The image is a technical table from a standard document, specifically IEC 61850-7-3, which defines the "Sampled Value" (SAV) data class. This table outlines the attributes and their properties for the SAV class, which is used to represent samples of instantaneous analogue values. The table includes columns for the attribute name, type, functional class (FC), trigger operation (TrgOp), value range, and the M/O/C (Measurement/Operation/Control) status.

**Key Elements:**
- **SAV Class:** Defines the sampled value data class.
- **Attribute Name:** Lists the attributes of the SAV class.
- **Type:** Specifies the type of the attribute (e.g., AnalogueValue, Unit, STRING255).
- **FC (Functional Class):** Indicates the functional class of the attribute.
- **TrgOp (Trigger Operation):** Specifies the trigger operation for the attribute.
- **Value/Value Range:** Describes the value or range of the attribute.
- **M/O/C (Measurement/Operation/Control):** Indicates whether the attribute is for measurement, operation, or control.

**Extracted Text:**
```
61850-7-3 © IEC:2010 - 35- 7.4.4 Sampled value (SAV)

Table 32 defines the common data class "sampled value". This common data class is used to represent samples of instantaneous analogue values. The values are usually transmitted using the "transmission of sampled value model" as defined in IEC 61850-7-2.

Table 32 — Sampled value

| Data attribute name | Type | FC | TrgOp | Value/Value range | M/O/C |
|---------------------|------|----|-------|--------------------|-------|
| instMag             | AnalogueValue | MX |       |                    |       |
| q                   | Quality | MX | qchg  |                    | M     |
| t                   | TimeStamp | MX |       |                    | M     |
| units               | Unit   | CF | dchg  | see Annex A        | O     |
| sVC                 | ScaledValueConfig | CF | dchg  | see Annex A        | AC_SCAV |
| min                 | AnalogueValue | CF | dchg  |                    |       |
| max                 | AnalogueValue | CF | dchg  |                    |       |
| d                   | VISIBLE STRING255 | DC | Text  |                    | AC_DLNDA_M |
| dU                  | STRING255 | DC | dchg  |                    |       |
| cdcNs               | VISIBLE STRING255 | EX |       |                    | AC_DLNDA_M |
| cdcName             | VISIBLE STRING255 | EX |       |                    | AC_DLNDA_M |
| dataNs              | VISIBLE STRING255 | EX |       |                    | AC_DLNDA_M |

Services
As defined in Table 29.
```

This table is part of a larger standard document and is used to define the structure and attributes of the sampled value data class in the context of IEC 61850, which is a standard for communication networks and systems in power utility automation.

---

## Page 38

![Image 1 from page 38](images/iec61850-7-3{ed2.0}b_page38_img43_d8df9757.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the "WYE" class, which is a common data class for phase-to-ground/neutral related measured values in a three-phase system. The table outlines the attributes, their types, and their configurations, as well as their default values and extensions. It also includes additional specifications regarding the use of certain attributes and the time at which the values are acquired.

**Key Elements:**

- **WYE Class Definition:** The table defines the "WYE" class as a collection of simultaneous measurements of phase-to-ground values in a three-phase system.
- **Attributes and Their Specifications:**
  - **DataName:** Inherited from GenDataObject Class or GenSubDataObject Class.
  - **SubDataObject:** Includes attributes like phsA, phsB, phsC, neut, net, and res.
  - **DataAttributes:** Includes attributes such as angRef, phsToNeut, d, dU, cdcNs, cdcName, and dataNs.
  - **Configuration, Description, and Extension:** Specifies the configuration, description, and extension for each attribute.
  - **Additional Specifications:** Includes rules for the use of certain attributes and the time at which the values are acquired.

**Extracted Text:**

```
7.4.5 Phase to ground/neutral related measured values of a three-phase system (WYE)
Table 33 defines the common data class "WYE". This class is a collection of simultaneous measurements of values in a three-phase system that represent phase to ground values.

Table 33 – WYE

Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
GC_1

SubDataObject
phsA
CMV
GC_1
phsB
CMV
GC_1
phsC
CMV
GC_1
neut
CMV
GC_1
net
CMV
GC_1
res
CMV
GC_1

DataAttribute
configuration, description and extension

angRef
ENUMERATED
CF
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc | Vca | Vother | Aother | Synchrophasor
O

phsToNeut
BOOLEAN
CF
dchg
DEFAULT=FALSE
O

d
VISIBLE STRING255
DC
Text
DEFAULT=FALSE
O

dU
UNICODE STRING255
DC
Text
O

cdcNs
STRING255
DC
AC_DLNDA_M

cdcName
VISIBLE STRING255
EX
AC_DLNDA_M

dataNs
VISIBLE STRING255
EX
AC_DLN_M

Services
As defined in Table 29.

With regard to data attributes of the CDC CMV, the following additional specifications apply.
- The data attribute angRef of phsA, phsB, phsC, neut, net and res shall not be used. Instead, the attribute angRef defined with the CDC WYE shall be used.
- The values of phsA.t, phsB.t, phsC.t, neut.t, net.t and res.t are identical. They specify the time at which the values for phsA, phsB, phsC and neut have been simultaneously acquired or determined.
```

This table and its associated text provide a comprehensive technical specification for the "WYE" data class, which is crucial for understanding the structure and usage of this class in a three-phase system.

---

## Page 39

![Image 1 from page 39](images/iec61850-7-3{ed2.0}b_page39_img44_9e91f74f.png)

**Image Type:** Table
**Description:** This image is a technical table from a standard document, specifically from IEC 61850-7-3, which is related to the IEC 61850 standard for communication networks and systems in power utility automation. The table, labeled as Table 34, defines the common data class "delta" for a three-phase system. It outlines the attributes and their specifications for the DEL class, which is used to represent phase-to-phase related measured values in a three-phase system.

**Key Elements:**
- **DEL class:** Defines the common data class "delta".
- **Data attributes:** Lists various attributes such as DataName, phsABObject, phsBCObject, phsCAObject, angRef, d, dU, ddcNs, ddcName, dataNs, and dataNs.
- **Type:** Specifies the type of each attribute, such as ENUMERATED, VISIBLE STRING255, etc.
- **FC, TrgOp, Value/Value range, M/O/C:** Columns that define the functionality, trigger operation, value range, and measurement/output/control status of each attribute.
- **Configuration, description and extension:** Additional specifications for the attributes, including their configuration, description, and extension.

**Extracted Text:**
```
61850-7-3 © IEC:2010 -37- 7.4.6 Phase to phase related measured values of a three-phase system (DEL)

Table 34 defines the common data class "delta". This class is a collection of measurements of values in a three-phase system that represent phase to phase values.

Table 34 – Delta

DEL class class

Data attribute name
DataName
phsABObject
phsBCObject
phsCAObject
angRef
d
dU
ddcNs
ddcName
dataNs
dataNs

Type
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
CMV
CMV
CMV
ENUMERATED
VISIBLE STRING255
VISIBLE STRING255
VISIBLE STRING255
VISIBLE STRING255
VISIBLE STRING255
VISIBLE STRING255

FC
dchg
DC
DC
CF
DC
DC
EX
EX
EX
EX

TrgOp
dchg
Text
Text
dchg
Text
Text
EX
EX
EX
EX

Value/Value range
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc | Vca | Vother | Aother | Synchrophasor
Text
Text
Text
Text
Text
Text
Text
Text
Text
Text
Text

M/O/C
O
O
O
O
O
O
O
AC_DLNDA_M
AC_DLNDA_M
AC_DLNDA_M
AC_DLNDA_M

With regard to data attributes of the CDC CMV, the following additional specifications apply.
- The data attribute angRef of phsAB, phsBC and phsCA shall not be used. Instead, the attribute angRef defined with the CDC DEL shall be used.
- The values of phsAB.t, phsBC.t and phsCA.t are identical. They specify the time at which the values for phsAB, phsBC and phsCA have been simultaneously acquired or determined.
```

This table provides a structured format for defining and specifying the attributes of the DEL class, which is crucial for implementing and standardizing communication protocols in power systems.

---

## Page 40

![Image 1 from page 40](images/iec61850-7-3{ed2.0}b_page40_img45_ef159049.png)

**Image Type:** Table
**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which defines the "Sequence" data class. The table outlines the attributes and their specifications for the "Sequence" class, which is a collection of sequence components of a value. The table includes columns for data attribute names, types, functional classes (FC), trigger operations (TrgOp), value ranges, and measurement attributes (M/O/C).

**Key Elements:**
- **SEQ class**: Defines the "Sequence" data class.
- **Data attribute name**: Lists the attributes such as DataName, seqT, phsRef, d, dU, cdcNs, cdcName, and dataNs.
- **Type**: Specifies the type of each attribute, such as ENUMERATED, CMV, STRING255, etc.
- **FC**: Functional classes associated with each attribute.
- **TrgOp**: Trigger operations for each attribute.
- **Value/Value range**: Specifies the range of values for each attribute.
- **M/O/C**: Measurement, operation, and configuration attributes.

**Extracted Text:**
```
— 38- 61850-7-3 © IEC:2010
7.4.7 Sequence (SEQ)
Table 35 defines the common data class "sequence". This class is a collection of sequence components of a value.
Table 35 - Sequence
[SEQ class]
Data attribute name
DataName
SubDataObject
c1
c2
c3
seqT
phsRef
d
dU
cdcNs
cdcName
dataNs
Services
As defined in Table 29.
With regard to data attributes of the CDC CMV, the following additional specifications apply.
The values of c1.t, c2.t and c3.t are identical. They specify the time at which the values for c1, c2 and c3 have been calculated.
```

This table is part of a larger document that provides detailed technical specifications for the "Sequence" data class in the context of IEC 61850-7-3, which is used in the automation and control of electrical power systems.

---

## Page 41

![Image 1 from page 41](images/iec61850-7-3{ed2.0}b_page41_img46_debf87f8.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document (IEC 61850-7-3 © IEC:2010) that defines the Harmonic Value (HMV) class. This class is used to represent the harmonic or interharmonic content of a process value, particularly for non-phase-related harmonic values. The table outlines the attributes and their corresponding data types, ranges, and configurations for the HMV class.

**Key Elements:**
- **HMV class**: Defines the common data class for non-phase-related harmonic values.
- **Data attributes**: Includes attributes such as `numHar`, `numCyc`, `evalTm`, `smpRate`, `frequency`, `hvRef`, `rmsCyc`, `d`, `dU`, `cdcNs`, `cdcName`, and `dataNs`.
- **Data types**: Specifies the type of data for each attribute, such as `INT16U`, `FLOAT32`, `ENUMERATED`, and `STRING255`.
- **Value/Value range**: Indicates the range or value type for each attribute.
- **M/O/C**: Represents the Modifiable, Optional, or Configurable status of each attribute.
- **Services**: Lists services associated with the HMV class.

**Extracted Text:**
```
61850-7-3 © IEC:2010 - 39 - 7.4.8 Harmonic value (HMV)

Table 36 defines the common data class for non-phase-related harmonic values. This class is a collection of values that represent the harmonic or interharmonic content of a process value.

Table 36 – Harmonic value

HMV class

Data attribute name
DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

SubDataObject
har
ARRAY 0..numHar OF CMV

DataAttribute

configuration, description and extension

numHar
INT16U
CF
dchg
>0
M

numCyc
INT16U
CF
dchg
>0
M

evalTm
INT16U
CF
dchg
0
M

smpRate
INT32U
CF
dchg
0
M

frequency
FLOAT32
CF
dchg
nominal frequency
M

hvRef
ENUMERATED
CF
dchg
fundamental | rms | absolute
O

rmsCyc
INT16U
CF
dchg
AC_RMS_M

d
VISIBLE STRING255
DC
Text
O

dU
UNICODE STRING255
DC
O

cdcNs
VISIBLE STRING255
EX
AC_DLNDA_M

cdcName
VISIBLE STRING255
EX
AC_DLNDA_M

dataNs
VISIBLE STRING255
EX
AC_DLN_M

Services
As defined in Table 29.

NOTE Harmonics for a single circuit have phase angles (optional) but need no reference for the angle (angRef), since by convention, the reference is always the fundamental frequency (index 1).
```

This table provides a comprehensive overview of the HMV class, its attributes, and their configurations, which is essential for understanding and implementing the standard in electrical and electronic systems.

---

## Page 42

![Image 1 from page 42](images/iec61850-7-3{ed2.0}b_page42_img47_9a20acaf.png)

**Image Type:** Table

**Description:** The image is a technical table defining the common data class "harmonic value for WYE" (HWYE). This table outlines the attributes and their corresponding data types, value ranges, and other technical specifications for the HWYE class. The table is structured to provide a comprehensive overview of the data attributes and their configurations, descriptions, and extensions, which are essential for understanding the harmonic content of a three-phase system with phase-to-ground values.

**Key Elements:**
- **HWYE class**: Defines the common data class for harmonic values in a WYE system.
- **Data attributes**: Lists various attributes such as phsAHar, phsBHar, phsCHar, neutHar, netHar, resHar, numHar, numCyc, evalTm, angRef, smpRate, frequency, hvRef, rmsCyc, d, dU, cdcNs, cdcName, dataNs, and Services.
- **Data types**: Specifies the type of data for each attribute, such as INT16U, FLOAT32, ENUMERATED, STRING255, etc.
- **FC (Function Code)**: Indicates the function code associated with each attribute.
- **TrgOp (Trigger Operation)**: Specifies the trigger operation for each attribute.
- **Value/Value range**: Defines the range of values for each attribute.
- **M/O/C**: Indicates whether the attribute is mandatory (M), optional (O), or configurable (C).

**Extracted Text:**

```
7.4.9 Harmonic value for WYE (HWYE)

Table 37 defines the common data class "harmonic value for WYE". This class is a collection of simultaneous measurements (or evaluations) of values that represent the harmonic or interharmonic content of a process value in a three-phase system with phase to ground values.

Table 37 – Harmonic values for WYE

HWYE class

Data attribute name
Data type
FC
TrgOp
Value/Value range
M/O/C

phsAHar
ARRAY 0..numHar OF CMV
M
phsBHar
ARRAY 0..numHar OF CMV
O
phsCHar
ARRAY 0..numHar OF CMV
O
neutHar
ARRAY 0..numHar OF CMV
O
netHar
ARRAY 0..numHar OF CMV
O
resHar
ARRAY 0..numHar OF CMV
O

DataAttribute
configuration, description and extension

numHar
INT16U
CF
dchg
>0
M
numCyc
INT16U
CF
dchg
>0
M
evalTm
INT16U
CF
dchg
M
angRef
ENUMERATED
CF
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc | Vca | Vother | Aother | Synchrophasor
O
smpRate
INT32U
CF
dchg
M
frequency
FLOAT32
CF
dchg
fundamental frequency
O
hvRef
ENUMERATED
CF
dchg
fundamental | rms | absolute
O
rmsCyc
INT16U
CF
dchg
M
d
VISIBLE STRING255
DC
Text
O
dU
UNICODE STRING255
DC
O
cdcNs
VISIBLE STRING255
EX
AC_RMS_M
cdcName
VISIBLE STRING255
EX
AC_DLNDA_M
dataNs
VISIBLE STRING255
EX
AC_DLNDA_M
dataNs
VISIBLE STRING255
EX
AC_DLN_M
Services
As defined in Table 29.
```

This table is crucial for technical documentation and search, as it provides detailed information about the harmonic values in a WYE system, which is essential for electrical engineering and power system analysis.

---

## Page 43

![Image 1 from page 43](images/iec61850-7-3{ed2.0}b_page43_img48_e7e3086e.png)

**Image Type:** Table

**Description:** The image is a technical table from IEC 61850-7-3, published in 2010. It defines the common data class "harmonic value for delta" (HDEL), which is used to represent harmonic or interharmonic content of a process value in a three-phase system with phase-to-phase values. The table outlines the attributes and their corresponding data types, ranges, and configurations for this data class.

**Key Elements:**
- **Data Class:** Harmonic value for delta (HDEL)
- **Attributes:** DataName, numHar, numCyc, evalTm, angRef, smpRate, frequency, hvRef, rmsCyc, d, dU, ddcNs, ddcName, dataNs, and services.
- **Data Types:** INT16U, INT32U, FLOAT32, ENUMERATED, STRING255, etc.
- **Value Ranges:** Various ranges are specified for each attribute, such as >0, fundamental, rms, absolute, etc.
- **Configuration and Description:** Each attribute has a configuration (CF), description (dchg), and description text (Text) provided.

**Extracted Text:**

```
61850-7-3 © IEC:2010 -41- 7.4.10 Harmonic value for DEL (HDEL)

Table 38 defines the common data class "harmonic value for delta". This class is a collection of simultaneous measurements (or evaluations) of values that represent the harmonic or interharmonic content of a process value in a three-phase system with phase to phase values.

Table 38 — Harmonic values for delta

Data attribute name
DataName
phsABHar
phsBCHar
phsCAHar
numHar
numCyc
evalTm
angRef
smpRate
frequency
hvRef
rmsCyc
d
dU
ddcNs
ddcName
dataNs
Services

Data class
Type
FC
TrgOp
Value/Value range
M/O/C

Data attribute
configuration, description and extension

numHar
INT16U
CF
dchg
>0
M

numCyc
INT16U
CF
dchg
>0
M

evalTm
INT16U
CF
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc | Vca | Vother | Aother | Synchrophasor
M

smpRate
INT32U
CF
dchg
nominal frequency
M

frequency
FLOAT32
CF
dchg
nominal frequency
M

hvRef
ENUMERATED
CF
dchg
fundamental | rms | absolute
O

rmsCyc
INT16U
CF
dchg
fundamental | rms | absolute
O

d
VISIBLE STRING255
DC
dchg
Text
O

dU
UNICODE STRING255
DC
DC
Text
O

ddcNs
VISIBLE STRING255
EX
dchg
AC_DLNDA_M
O

ddcName
VISIBLE STRING255
EX
dchg
AC_DLNDA_M
O

dataNs
VISIBLE STRING255
EX
dchg
AC_DLNDA_M
O

Services
As defined in Table 29.
```

This table provides a comprehensive definition of the HDEL data class, detailing each attribute and its configuration, description, and value range, which is essential for implementing and understanding the IEC 61850 standard in electrical and automation systems.

---

## Page 44

![Image 1 from page 44](images/iec61850-7-3{ed2.0}b_page44_img49_bfb3ef91.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document related to IEC 61850-7-3, which specifies common data class specifications for controls. The table outlines the basic controllable status information template and details various services and their attributes, types, and operational parameters.

**Key Elements:**
- **Table Title:** Basic controllable status information template
- **Columns:** Data attribute name, Type, FC, TrgOp, Value/Value range, M/O/C
- **Services:** SetDataValues, GetDataValues, GetDataDefinition, GetDataDirectory, Report, SendGOOSEMessage, SendGSSEMessage, SendMSVMessage, SendUSVMessage, Select, SelectWithValue, Cancel, Operate, CommandTermination, TimeActivatedOperate
- **Attributes:** Status, measured attributes, control mirror, substitution, blocked, configuration, description, extension, and parameters for control services
- **Service Models:** GenCommonDataClass model, Data set model, Reporting model, GSE model, Sampled values model, Control model

**Extracted Text:**

```
- 42 - 61850-7-3 © IEC:2010
7.5 Common data class specifications for controls
7.5.1 Application of services
Table 39 defines the basic controllable status information template. In particular, it defines the inheritance and specialization of services defined in IEC 61850-7-2.
Table 39 – Basic controllable status information template
Basic controllable status information template
Data Type TrgOp Value/Value range M/O/C
attribute name DataName Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
DataAttribute Status / measured attributes and control mirror substitution and blocked configuration, description and extension parameters for control services
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes with a functional constraint as specified below.
Service model of Service Service Remark
IEC 61850-7-2 applies to Attr with FC
GenCommonDataClass Service Service
model Service
GetDataValues DC, CF, SV, BL
GetDataDefinition ALL
GetDataDirectory ALL
Data set model GetDataSetValues ALL
SetDataSetValues DC, CF, SV, BL
Reporting model Report ALL As specified within the data set that is used
GSE model SendGOOSEMessage ST, MX to define the content of the message
SendGSSEMessage ST
Sampled values model I SendMSVMessage ST, MX SendUSVMessage ST, MX
Control model Select SelectWithValue Cancel Operate CommandTermination TimeActivatedOperate
All common data classes for controllable status information include both the control and the related status information.
NOTE The service parameter of the control, which belongs to the control model defined in IEC 61850-7-2, is included here, since the type is defined by the CDC.
```

This table and its associated text provide a comprehensive overview of the basic controllable status information template and the services associated with it, adhering to the IEC 61850-7-2 standard.

---

## Page 45

![Image 1 from page 45](images/iec61850-7-3{ed2.0}b_page45_img50_6e0bb8f6.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically IEC 61850-7-3, which defines the common data class "controllable single point" (SPC). The table, labeled as Table 40, outlines the attributes and their corresponding data types, value ranges, and access categories (M/O/C) for the SPC class. It includes detailed information about the attributes such as origin, status, quality, and configuration parameters, along with their respective data types, value ranges, and access categories.

**Key Elements:**
- **SPC Class:** Defines the common data class "controllable single point."
- **Attributes:** Lists various attributes such as origin, status, quality, configuration, and services.
- **Data Types:** Specifies the type of data for each attribute (e.g., BOOLEAN, STRING, TIMESTAMP).
- **Value Ranges:** Indicates the range of values for certain attributes (e.g., stVal has a range of 0.255).
- **Access Categories (M/O/C):** Denotes the access categories for each attribute (M - Modify, O - Read, C - Create).
- **Substitution and Blocked Attributes:** Lists attributes that can be substituted or blocked.
- **Services:** Defines parameters for control services, including service parameter names and types.

**Extracted Text:**

```
61850-7-3 © IEC:2010 - 43 - 7.5.2 Controllable single point (SPC)
Table 40 defines the common data class "controllable single point".
Table 40 - Controllable single point

SPC class
Data
attribute
name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
ST
dchg
0.255
AC_CO_O

DataAttribute
origin
Originator
ST
dchg
0.255
AC_CO_O

stNum
INT8U
ST
dchg
0.255
AC_ST_O

stVal
BOOLEAN
ST
dchg
FALSE | TRUE
AC_AC_ST

q
Quality
ST
qchg
FALSE | TRUE
AC_AC_ST

t
Time Stamp
ST
dchg
0
AC_ST

stSelId
BOOLEAN
ST
dchg
0
AC_ST

opRcvd
BOOLEAN
OR
dchg
0
O

opOk
BOOLEAN
OR
dchg
0
O

tOpOk
TimeStamp
OR
dchg
0
O

subEna
BOOLEAN
SV
dchg
PICS_SUBST

subVal
BOOLEAN
SV
FALSE | TRUE
PICS_SUBST

subQ
Quality
SV
PT
PICS_SUBST

subID
VISIBLE STRING64
SV
dchg
PICS_SUBST

blkEna
BOOLEAN
BL
dchg
0
O

pulseConfig
PulseConfig
CF
dchg
AC_CO_O

ctlModel
CtlModelsConfig
CF
dchg
AC_CO_O

sboTimeout
INT32U
CF
dchg
AC_CO_O

sboClass
SboClasses
CF
dchg
AC_CO_O

operTimeout
INT32U
CF
dchg
AC_CO_O

d
VISIBLE STRING255
CF
dchg
AC_CO_O

dU
UNICODE STRING255
DC
dchg
Text
O

cdcNs
STRING255
EX
AC_DLNDA_M

cdcName
VISIBLE STRING255
EX
AC_DLNDA_M

dataNs
VISIBLE STRING255
EX
AC_DLN_M

Services
As defined in Table 39.

Service parameter name
Service parameter type
Value/Value range

ctlVal
BOOLEAN
off (FALSE) | on (TRUE)
```

This table provides a comprehensive overview of the controllable single point class, detailing its attributes, data types, value ranges, and access categories, which are essential for understanding and implementing the standard in technical systems.

---

## Page 48

![Image 1 from page 48](images/iec61850-7-3{ed2.0}b_page48_img51_ff0d016c.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the attributes and their definitions for the "Controllable Enumerated Status" (ENC) class as defined in IEC 61850-7-3. The table is structured to provide a comprehensive overview of the data attributes, their types, field control (FC), trigger operation (TrgOp), value range, and access modes (M/O/C).

**Key Elements:**
- **Data Class:** ENC class
- **Attributes:** Origin, stNum, stVal, q, t, stSeld, subEna, subVal, subQ, subID, blkEna, ctlModel, sboTimeout, sboClass, operTimeout, d, dU, cdcNs, cdcName, dataNs, eva, and ctlVal.
- **Types:** ENUMERATED, BOOLEAN, TIMESTAMP, STRING64, STRING255, etc.
- **Field Control (FC):** ST, SV, CF, OR, BL, etc.
- **Trigger Operation (TrgOp):** dchg, qchg, etc.
- **Value Range:** 0..255, INT32U, etc.
- **Access Modes (M/O/C):** AC, CO, O, etc.

**Extracted Text:**
```plaintext
– 46 – 61850-7-3 © IEC:2010
7.5.5 Controllable enumerated status (ENC)
Table 43 defines the common data class "controllable enumerated status".
Table 43 – Controllable enumerated status

ENC class
Data class
Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
Origin
Originator
ST
AC_CO_O
stNum
ENUMERATED
ST
dchg
0..255
AC_CO_O
stVal
ENUMERATED
ST
dchg
M
q
Quality
ST
qchg
M
t
TimeStamp
ST
M
t
TimeStamp
ST
M
stSeld
BOOLEAN
ST
dchg
M
subEna
BOOLEAN
SV
PICS_SUBST
subVal
ENUMERATED
SV
PICS_SUBST
subQ
Quality
SV
PICS_SUBST
subID
VISIBLE STRING64
SV
PICS_SUBST
blkEna
BOOLEAN
BL
PICS_SUBST
ctlModel
CtIModels
CF
dchg
M
sboTimeout
INT32U
CF
dchg
AC_CO_O
sboClass
SboClasses
CF
dchg
AC_CO_O
operTimeout
INT32U
CF
dchg
AC_CO_O
d
VISIBLE STRING255
DC
Text
dU
UNICODE STRING255
DC
Text
cdcNs
STRING255
DC
Text
cdcName
VISIBLE STRING255
EX
AC_DLNDA_M
dataNs
VISIBLE STRING255
EX
AC_DLNDA_M
eva
ENUMERATED
EX
AC_DLNDA_M
ctlVal
```

This table is part of a larger technical document, likely related to IEC 61850 standards for smart grid communication, specifically detailing the attributes and their definitions for the "Controllable Enumerated Status" class.

---

## Page 54

![Image 1 from page 54](images/iec61850-7-3{ed2.0}b_page54_img52_f3360725.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which is related to the configuration and description of data classes for intelligent electronic devices (IEDs). The table details the structure and attributes of two data classes: "Integer status setting" (ING) and "Enumerated status setting" (ENG). These classes are used for defining the status of various parameters in IEDs, such as protection settings, control commands, and monitoring data.

**Key Elements:**

- **Table Title:** Table 50 - Integer status setting setting and Table 51 - Enumerated status setting setting.
- **Data Classes:** ING and ENG.
- **Attributes:** DataName, DataAttribute, Type, FC, TrgOp, Value/Value range, M/O/C.
- **Specific Attributes for ING:**
  - setVal: INT32, SP, dchg, AC_NSG_M, AC_SG_M, AC_DLNDA_M, AC_DLN_M.
  - minVal: INT32, CF, dchg, O.
  - maxVal: INT32, CF, dchg, O.
  - stepSize: INT32U, CF, dchg, O.
  - units: Unit, CF, dchg, O.
- **Specific Attributes for ENG:**
  - setVal: ENUMERATED, SP, dchg, AC_NSG_M, AC_SG_M, AC_DLNDA_M, AC_DLN_M.
  - d: VISIBLE STRING255, DC, dchg, Text.
  - dU: VISIBLE STRING255, DC, dchg, Text.
  - ddcNs: VISIBLE STRING255, DC, dchg, Text.
  - ddcName: VISIBLE STRING255, EX, dchg, Text.
  - dataNs: VISIBLE STRING255, EX, dchg, Text.

**Extracted Text:**

```
-52- 61850-7-3 © IEC:2010
7.6.3 Integer status setting (ING)
Table 50 defines the common data class “integer status setting”.
Table 50 – Integer status setting setting

ING class
Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

DataAttribute
setting

setVal
INT32
SP
dchg
AC_NSG_M
AC_SG_M
AC_DLNDA_M
AC_DLN_M

setVal
INT32
SG, SE
dchg
O

minVal
INT32
CF
dchg
O

maxVal
INT32
CF
dchg
O

stepSize
INT32U
CF
dchg
O

units
Unit
CF
dchg
O

d
VISIBLE STRING255
DC
dchg
Text
O

dU
VISIBLE STRING255
DC
dchg
Text
O

ddcNs
VISIBLE STRING255
DC
dchg
Text
O

ddcName
VISIBLE STRING255
EX
dchg
Text
O

dataNs
VISIBLE STRING255
EX
dchg
Text
O

Services
As defined in Table 48.

7.6.4 Enumerated status setting (ENG)
Table 51 defines the common data class “enumerated status setting”.
Table 51 – Enumerated status setting setting

ENG class
Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

DataAttribute
setting

setVal
ENUMERATED
SP
dchg
AC_NSG_M
AC_SG_M
AC_DLNDA_M
AC_DLN_M

setVal
ENUMERATED
SG, SE
dchg
O

d
VISIBLE STRING255
DC
dchg
Text
O

dU
VISIBLE STRING255
DC
dchg
Text
O

ddcNs
VISIBLE STRING255
DC
dchg
Text
O

ddcName
VISIBLE STRING255
EX
dchg
Text
O

dataNs
VISIBLE STRING255
EX
dchg
Text
O

---

## Page 57

![Image 1 from page 57](images/iec61850-7-3{ed2.0}b_page57_img53_6d20d2e8.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, published in 2010. It details the "Basic controllable analogue information template" and the "Basic analogue setting template," which are part of the IEC 61850 standard for communication networks and systems in power utility automation. The table outlines the data attributes, their types, functional constraints (FC), trigger operations (TrgOp), value ranges, and the mode of operation (M/O/C) for various services related to analogue settings.

**Key Elements:**

- **Data attribute name**: Identifies the specific attribute being described.
- **Type**: Specifies the type of data, such as VISIBLE STRING255.
- **FC**: Functional constraint, indicating the type of service or operation.
- **TrgOp**: Trigger operation, defining the conditions under which the service is triggered.
- **Value/Value range**: The possible values or ranges for the data attribute.
- **M/O/C**: Mode of operation, indicating whether the service is for measurement (M), operation (O), or control (C).

**Extracted Text:**

```
61850-7-3 © IEC:2010 - 55-
Data attribute name Type FC TrgOp Value/Value range M/O/C
cdcName VISIBLE STRING255 EX
dataNs VISIBLE STRING255 EX
Services
As defined in Table 48.

7.7 Common data class specifications for analogue settings
7.7.1 Application of services
Table 56 defines the basic controllable analogue information template. In particular, it defines the inheritance and specialization of services defined in IEC 61850-7-2.
Table 56 – Basic analogue setting template
Basic controllable analogue information template
Data attribute name Type FC TrgOp Value/Value range M/O/C
DataName Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
DataAttribute configuration, description and extension
Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes with a functional constraint as specified below.
Service model of Service Service Remark
IEC 61850-7-2 SetDataValues Service applies to Attr with FC
GenCommonDataClass GetDataValues DC, CF, SP
model GetDataDefinition ALL except SE
GetDataDirectory ALL
Data set model GetDataSetValues ALL except SE
SetDataSetValues DC, CF
Reporting model Report ALL except SE
GSE model SendGOOSEMessage SP As specified within the data set that is used to define the content of the message
Setting group control SetEditSGValues SE model
GetEditSGValues SE, SG
```

This table and its associated text provide a structured overview of the services and data attributes involved in the configuration and management of analogue settings within the IEC 61850 framework.

---

## Page 58

![Image 1 from page 58](images/iec61850-7-3{ed2.0}b_page58_img54_dcb0c956.png)

**Image Type:** Table
**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which defines the common data class "analogue setting" (ASG). The table, labeled as Table 57, provides a detailed structure for the ASG class, including its attributes, types, and configuration options.

**Key Elements:**
- **ASG Class:** The table outlines the attributes and their configurations for the Analogue Setting (ASG) class.
- **Attributes:** The table lists attributes such as DataName, setMag, units, sVC, minVal, maxVal, stepSize, dU, cdcNs, cdcName, dataNs, and Services.
- **Types:** Each attribute is associated with a specific type, such as AnalogueValue, Unit, ScaledValueConfig, etc.
- **Configuration Options:** The table includes configuration options like FC (Field Configuration), TrgOp (Trigger Operation), Value/Value range, and M/O/C (Modify/Observe/Control).
- **Value/Value Range:** The range of values for certain attributes is specified, such as 0 to (maxVal - minVal).
- **Services:** The table also includes services associated with the ASG class.

**Extracted Text:**
```
-56- 61850-7-3 © IEC:2010
7.7.2 Analogue setting (ASG)
Table 57 defines the common data class "analogue setting".
Table 57 – Analogue setting

ASG class
Data attribute name
Type
FC
TrgOp
Value/Value range
M/O/C

DataName
Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
setMag
AnalogueValue
SP
dchg
AC_NSG_M
setMag
AnalogueValue
SG, SE
dchg
AC_SG_M
units
Unit
CF
dchg
see Annex A
sVC
ScaledValueConfig
CF
dchg
AC_SCAV
minVal
AnalogueValue
CF
dchg
AC_SCAV
maxVal
AnalogueValue
CF
dchg
AC_SCAV
stepSize
AnalogueValue
CF
dchg
0 ... (maxVal - minVal)
dU
VISIBLE STRING255
DC
dchg
Text
dU
UNICODE
STRING255
DC
dchg
cdcNs
STRING255
DC
cdcName
VISIBLE STRING255
EX
dataNs
VISIBLE STRING255
EX
Services
As defined in Table 56.
```

This table is crucial for understanding the configuration and implementation of the Analogue Setting class in IEC 61850 standards, which is widely used in smart grid and automation systems.

---

## Page 61

![Image 1 from page 61](images/iec61850-7-3{ed2.0}b_page61_img55_50ec821a.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page from IEC 61850-7-3, dated 2010. It includes a diagram illustrating a two-dimensional shape created by multiple Common Shape Groups (CSG) and a table defining a basic description information template. The document discusses the creation of shape settings using data objects and the use of specific attributes and services to define these settings.

**Key Elements:**
- **Diagram:** A two-dimensional shape created by connecting multiple CSGs.
- **Table:** Defines a basic description information template with columns for Data attribute name, Type, FC (Functional Constraint), TrgOp (Trigger Operation), Value/Value range, and M/O/C (Modify/Observe/Control).
- **Text:** Describes the creation of shape settings using data objects, the use of attributes and services, and the inheritance and specialization of services.

**Extracted Text:**

```
61850-7-3 © IEC:2010 - 59-
A family of shape settings can be created by multiple instances of a data object with the CDC CSG. In that case, the common data attribute type point used for crvPts shall not support the optional element z and the attribute pointZ is used to represent the value of the curve on the z axis. The three-dimensional shape is created by connecting the curves with each other. See Figure 7.

Figure 7 – Two-dimensional shape shape created by multiple CSG

7.8 Common data class specifications for description information

7.8.1 Application of services

Table 60 defines the basic description information template. In particular, it defines the inheritance and specialization of services defined in IEC 61850-7-2.

Table 60 – Basic description description information information template

Basic description information template
Data Type TrgOp Value/Value range M/O/C
attribute name DataName Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

Services (see IEC 61850-7-2)
The following services are inherited from IEC 61850-7-2. They are specialized by restricting the service to attributes with a functional constraint as specified below.

Service model of Service Service Remark
IEC 61850-7-2 applies to Attr with FC
GenCommonDataClass SetDataValues DC, CF
model GetDataValues ALL GetDataDefinition ALL GetDataDirectory ALL
Data set model GetDataSetValues ALL SetDataSetValues DC, CF
Reporting model Report ALL As specified within the data set that is used to define the content of the message
GOOSE, SV model GOOSE, SV Sa I
```

This technical document provides a detailed explanation of how to create and manage shape settings in a data object using the IEC 61850 standard, focusing on the use of Common Shape Groups (CSG) and specific attributes and services.

---

## Page 62

![Image 1 from page 62](images/iec61850-7-3{ed2.0}b_page62_img56_ff827d9e.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which defines the "device name plate" (DPL) common data class. This table outlines the attributes and their specifications for the DPL class, which is used to identify entities such as primary equipment or physical devices in a smart grid or automation system.

**Key Elements:**
- **DPL Class:** The table defines the Device Name Plate (DPL) class, which is a common data class used to identify entities.
- **Data Attributes:** The table lists various attributes that can be associated with the DPL class, such as vendor, hardware revision, software revision, serial number, model, location, name, owner, ePS name, prime operator, second operator, latitude, longitude, altitude, MRID, data name, and data class name.
- **Type:** Each attribute is defined with a type, such as VISIBLE STRING255, FLOAT32, and UNICODE STRING255.
- **FC (Function Code):** Indicates the function code for each attribute, which can be DC (Data Communication), EX (External), or M (Measurement).
- **TrgOp (Trigger Operation):** Specifies the trigger operation for each attribute, which can be DC, EX, or M.
- **Value/Value Range:** Provides the value or value range for each attribute.
- **M/O/C:** Indicates whether the attribute is mandatory (M), optional (O), or can be configured (C).

**Extracted Text:**

```
61850-7-3 © IEC:2010
7.8.2 Device name plate (DPL)
Table 61 defines the common data class "device name plate". Data of this common data class are used to identify entities like primary equipment or physical devices.
Table 61 – Device name plate common data class specification
Data attribute name
DataName Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)
DataAttribute
vendor VISIBLE STRING255 DC
hwRev VISIBLE STRING255 DC
swRev VISIBLE STRING255 DC
serNum VISIBLE STRING255 DC
model VISIBLE STRING255 DC DC
location VISIBLE STRING255 DC
name VISIBLE STRING64 DC
owner VISIBLE STRING255 DC
ePSName VISIBLE STRING255 DC
primeOper VISIBLE STRING255 DC DC
secondOper VISIBLE STRING255 DC DC
latitude FLOAT32 DC
longitude FLOAT32 DC
altitude FLOAT32 DC
mRID VISIBLE STRING255 DC
dataNs VISIBLE STRING255 EX
dataName VISIBLE STRING255 EX
cdcNs VISIBLE STRING255 EX
cdcName VISIBLE STRING255 EX
Services
As defined in Table 60.
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

This table is crucial for engineers and system integrators working with IEC 61850 standards, as it provides a comprehensive list of attributes and their specifications for the device name plate, which is essential for the identification and configuration of devices in smart grid and automation systems.

---

## Page 63

![Image 1 from page 63](images/iec61850-7-3{ed2.0}b_page63_img57_432aaf4c.png)

**Image Type:** Table
**Description:** The image is a technical table from a standard document, specifically from IEC 61850-7-3, which defines the common data class "logical node name plate" (LPL). This table outlines the attributes and their specifications for the LPL class, which is used to describe logical nodes in a communication network.

**Key Elements:**
- **LPL Class:** The table defines the logical node name plate class.
- **Data Attribute Name:** Lists the attributes of the LPL class.
- **Type:** Specifies the type of data for each attribute (e.g., VISIBLE STRING255, UNICODE STRING255, INT32).
- **FC (Function Code):** Indicates the function code associated with each attribute.
- **TrgOp (Trigger Operation):** Specifies the trigger operation for each attribute.
- **Value/Value Range:** Provides the value or range of values for each attribute.
- **M/O/C (Mandatory/Optional/Conditional):** Indicates whether the attribute is mandatory, optional, or conditional.

**Extracted Text:**

```
61850-7-3 © IEC:2010 -61- 7.8.3 Logical node name plate (LPL)

Table 62 defines the common data class "logical node name plate". Data of this common data class are used to describe logical nodes.

Table 62 – Logical node name plate common data class specification

LPL class

Data attribute name    Type    FC    TrgOp    Value/Value range    M/O/C

DataName               Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2)

DataAttribute          configuration, description and extension

vendor                 VISIBLE STRING255    DC    TPM    M

swRev                  VISIBLE STRING255    DC    OC    M

d                     VISIBLE STRING255    DC    O    O

dU                    UNICODE STRING255    DC    O    O

configRev              VISIBLE STRING255    DC    O    O

paramRev               INT32                ST    dchg    AC_LNO_M

valRev                 INT32                ST    dchg    O

idNsRev                VISIBLE STRING255    EX    dchg    AC_LNO_EX

idNs                   VISIBLE STRING255    EX    AC_DLD_M

InNsRev                VISIBLE STRING255    EX    dchg    AC_DLNDA_M

InNs                   VISIBLE STRING255    EX    AC_DLN_M

cdcNsRev               VISIBLE STRING255    EX    dchg    AC_DLNDA_M

cdcNs                  VISIBLE STRING255    EX    AC_DLN_M

cdcNameRev             VISIBLE STRING255    EX    dchg    AC_DLN_M

cdcName                 VISIBLE STRING255    EX    AC_DLN_M

dataNsRev              VISIBLE STRING255    EX    dchg    AC_DLN_M

dataNs                 VISIBLE STRING255    EX    AC_DLN_M

Services

As defined in Table 60.

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

This table provides a detailed specification for the LPL class, which is crucial for defining and managing logical nodes in IEC 61850-based communication systems.

---

## Page 64

![Image 1 from page 64](images/iec61850-7-3{ed2.0}b_page64_img58_b1697f9c.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the attributes and specifications of the "Curve Shape Description" (CSD) class, which is a common data class used to describe the shape of a curve. This table is part of a larger technical document, likely related to IEC 61850-7-3, as indicated by the copyright notice at the top.

**Key Elements:**
- **CSD Class:** Defines the structure and attributes of the curve shape description.
- **Attributes:** Lists various attributes such as xUnits, yUnits, zUnits, crvPts, numPts, etc., along with their types, value ranges, and access modes (M for mandatory, O for optional, C for configurable).
- **Value/Value Range:** Specifies the permissible values or ranges for each attribute.
- **M/O/C:** Indicates whether the attribute is mandatory (M), optional (O), or configurable (C).
- **Services:** Lists services associated with the CSD class, such as dataNs and edeNs, which are likely related to data access and extension.

**Extracted Text:**

```
61850-7-3 © IEC:2010
7.8.4 Curve shape description (CSD)

Table 63 defines the common data class "curve shape description". Data of this common data class are used to read the shape of a curve as for example used with protection settings.

Table 63 – Curve shape description common data class specification

| Attribute name | Attribute type | FC | TrgOp | Value/Value range | M/O/C |
|----------------|----------------|----|--------|-------------------|-------|
| DataName       | Inherited from GenDataObject Class or from GenSubDataObject Class (see IEC 61850-7-2) | DC | DC | | M |
| DataAttribute  | | | | | |
| xUnits         | Unit | DC | DC | | M |
| xD             | VISIBLE STRING255 | DC | DC | | M |
| xDU            | UNICODE STRING255 | DC | DC | | M |
| yUnits         | Unit | DC | DC | | O |
| yD             | VISIBLE STRING255 | DC | DC | | O |
| yDU            | UNICODE STRING255 | DC | DC | | O |
| zUnits         | Unit | DC | DC | | O |
| zD             | VISIBLE STRING255 | DC | DC | | O |
| zDU            | UNICODE STRING255 | DC | DC | | O |
| numPts         | INT16U | DC | DC | >1 | M |
| crvPts         | ARRAY 0..numPts-1 | DC | DC | | M |
| d              | VISIBLE STRING255 | DC | DC | | O |
| dD             | VISIBLE STRING255 | DC | DC | | O |
| dDU            | UNICODE STRING255 | DC | DC | | O |
| edeNs          | VISIBLE STRING255 | EX | | | AC_DLNDAM |
| edeName        | VISIBLE STRING255 | EX | | | AC_DLNDAM |
| dataNs         | VISIBLE STRING255 | EX | | | AC_DLNM |

Services
As defined in Table 60.

The curve is created by the connection of crvPts(n) with crvPts(n+1) with 0<n<numPts.
```

This table provides a comprehensive overview of the attributes and their specifications within the CSD class, which is crucial for understanding how to implement and use this class in a technical context, particularly in the context of IEC 61850-7-3.

---

## Page 67

![Image 1 from page 67](images/iec61850-7-3{ed2.0}b_page67_img59_226770e8.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically from page 65 of a standard or specification document. The table is titled "Data attribute name" and provides semantical definitions for the array elements within a data structure. The table is structured with two columns: "Data attribute name" and "Semantics."

**Key Elements:**
- **Data attribute name:** har
- **Semantics:** Describes the content and structure of the array elements for the "har" data attribute.

**Extracted Text:**
```
61850-7-3 © IEC:2010 - 65 - Data attribute name Semantics
har This array shall contain the harmonic and subharmonic or the interharmonic values. Harmonic and subharmonic values (evalTm equal to the period of the power frequency) The first array element shall contain the dc components, the further array elements shall contain the values for the harmonics 1.. numHar. If numCycl is larger than one, then the array shall contain both harmonics and subharmonics and their multiples. In that case, sequence entries with the number n×2numCycl−1 are harmonics; all other ones are subharmonics or multiple of subharmonics. Interharmonic values (evalTm not equal to the period of the power frequency) The first array element shall contain the dc components, the further array elements shall contain the values for the harmonics 1.. numHar.
```

**Technical Analysis:**
The table describes the structure of an array used to store harmonic, subharmonic, and interharmonic values. The array is defined by the "har" data attribute. The first element of the array contains the direct current (dc) components, while subsequent elements contain the values for harmonics and subharmonics. If the number of cycles (numCycl) is greater than one, the array will also contain subharmonics and their multiples. The distinction between harmonics and subharmonics is made based on the sequence number within the array. Harmonics are identified by entries with the number \( n \times 2^{\text{numCycl}-1} \), while all other entries are subharmonics or multiples of subharmonics. Interharmonic values are those that do not correspond to the power frequency period (evalTm). The document is part of a standard from IEC (International Electrotechnical Commission) and is copyrighted in 2010.

---

## Page 68

![Image 1 from page 68](images/iec61850-7-3{ed2.0}b_page68_img60_dfa3b9b3.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical diagram that explains the structure and usage of a histogram in the context of data analysis. It includes a table that defines the data attributes and their semantics, as well as a graphical representation of a one-dimensional histogram and a two-dimensional histogram.

**Key Elements:**

- **Data Attribute Table:**
  - **hstVal:** This array contains the values for the histogram entries. Each entry represents the count of the appearance of the evaluated values in a specific range.
  - **hstRangeC:** This array contains the values for the configuration of the ranges for the histogram. Each entry represents a range of values.
  - **hvRef:** Specifies the reference type, such as the ratio of harmonic to fundamental, RMS, or absolute.
  - **intAddr:** Represents a manufacturer-specific internal address.

- **Graphical Representation:**
  - **One-dimensional Histogram:** The diagram shows a bar chart where each bar represents a range of values, and the height of the bar indicates the count of values within that range.
  - **Two-dimensional Histogram:** The diagram shows a grid where each cell represents a range of values, and the color intensity or number within the cell indicates the count of values within that range.

- **Textual Explanation:**
  - The text explains how a histogram evaluates a series of values and how the appearance of a value in a certain range is evaluated. It also describes the configuration of the ranges for the histogram and the reference type.

**Extracted Text:**

```
— 66 — 61850-7-3 © IEC:2010

Data attribute name
This array shall contain the values for the histogram entries. A histogram can be calculated based on a one-dimensional or a two-dimensional range. Details of a one-dimensional histogram representation are shown in the drawing below.
Value (counts or other)
hstVal(0) hstVal(1) hstVal(3) hstVal(2)
xUnits
A histogram evaluates a series of values and evaluates the appearance of a value in a certain range. The evaluation can typically be a count, a measurement of a duration or the calculation of an average. The value range is configured with the configuration attribute hstRangeC. The attribute hstVal[1] shall be the count of the appearance of the evaluated values in the range hstRangeC[1]. For a two-dimensional histogram, the range can be as shown in the following drawing. Each of the rectangles represents one range; there is no rule, how to order the ranges.
X-axis
For that example, the values would be as follows:
index 0 1 2 3 4 5
hstVal 0 10 9 1 5 3
hstRangeC 0:0/4:10 4:0/10,4 10:0/12:4 4:4/12:8 4:8/8:10 8:8/12:10
This array shall contain the values for the configuration of the ranges for the histogram. For details, see hstVal.
hvRef Specifies the reference type (i.e. ratio of harmonic to fundamental, to RMS or to absolute), which the data attribute mag of the data attribute type Vector contain.
intAddr This value represents a manufacturer specific internal address.
```

This technical analysis provides a clear understanding of the image's content and its relevance to data analysis and histogram representation.

---

## Page 73

![Image 1 from page 73](images/iec61850-7-3{ed2.0}b_page73_img61_62934d0f.png)

**Image Type:** Table

**Description:** The image is a technical table from IEC 61850-7-3, which outlines various data attributes and their semantics, including ranges, validity, and detail quality. The table is structured to provide a comprehensive overview of the data attributes used in the context of electrical and electronic systems, particularly in the context of smart grid communication standards.

**Key Elements:**
- **Data Attribute Name:** Lists the names of the data attributes such as `range`, `rangeAng`, `rangeAngC`, `rangeC`, `res`, `resHar`, `rmsCyc`, `sboClass`, `sboTimeout`, `secondOper`, `seqT`, `serNum`, and `setCal`.
- **Semantics:** Provides detailed descriptions of the semantics associated with each data attribute, including their ranges, validity, and detail quality.
- **Range:** Defines the range in which the current value of the data attribute falls.
- **Validity:** Indicates the quality of the data, such as `good`, `questionable`, or `outOfRange`.
- **Detail-Qual:** Specifies the detail quality of the data, which can be `high`, `normal`, or `low`.

**Extracted Text:**

```
61850-7-3 © IEC:2010 - 71 - Data attribute name Range in which the current value of instMag or instCVal.mag is. It may be used to issue an event if the current value changes and transitions to another range. Range shall be used in the context with configuration attributes like hhLim, hLim, ILim, IlLim, min and max as shown below.

range validity detail-qual
high-high questionable outOfRange
max ooo high-high good
pati I A high good
pLim I ——___________ high good
hLim I ——___________ high good
normal good
ILim — low good
IlLim ——___________ normal good
low good
Mim I ——___________ low-low good
min ooo low-low questionable outOfRange

NOTE 7 The use of algorithms to filter events based on transition from one range to another is a local issue.
NOTE 8 This value with the trigger option "data-change" as described in 61850-7-2 may be used to report an event to the client.

rangeAng Range in which the current value of instCVal.ang is. For further details, see range.
rangeAngC Configuration parameters as used in the context with the rangeAng attribute.
rangeC Configuration parameters as used in the context with the range attribute.
res Residual current. Residual current is the algebraic sum of the instantaneous values of currents flowing through all live conductors (i.e. sum over phase currents) of a circuit at a point of the electrical installation. For further details, see phsA (WYE).
resHar This array shall contain the harmonic and subharmonics or interharmonic values related to residual current. For further details, see Har.
rmsCyc Number of cycles of power frequency, which are used for the calculation of rms values.
sboClass Specifies the SBO-class according to the control model of IEC 61850-7-2 that corresponds to the behaviour of the data. The following values are defined:
operate-once: Following an operate request, the control object shall return in the unselected state.
operate-many: Following an operate request, the control object shall remain in the ready state, as long as sboTimeout did not expire.
sboTimeout Specifies the timeout between a select and an operate command according to the control model of IEC 61850-7-2. The value shall be in ms.
secondOper Secondary operator of device.
seqT This attribute shall specify the type of the sequence. The following values are used:
value c1 c2 c3
pos-neg-zero pos neg zero
dir-quad-zero dir quad zero
setCal The value of a time setting, if the time is set with a calendar time.
```

This table is crucial for understanding the data attributes and their semantics in the context of smart grid communication standards, particularly for devices that need to report events based on changes in the data values.

---

## Page 75

![Image 1 from page 75](images/iec61850-7-3{ed2.0}b_page75_img62_4c1a2709.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically IEC 61850-7-3, published in 2010. The table outlines the semantics and definitions of various data attributes used in the context of IEC 61850, a standard for communication networks and systems in power utility automation. The table provides detailed descriptions of the meanings and values associated with each data attribute.

**Key Elements:**
- **Data attribute name:** Lists the names of the data attributes.
- **Semantics:** Describes the meaning and possible values of each data attribute.
- **Specific attributes and their definitions:**
  - `setVal`: Severity of the last violation detected.
  - `smpRate`: Sampling rate according to the sampling theorem.
  - `stepSize`: Defines the step between individual values.
  - `strTm`: Starting time of the freeze process.
  - `stSeld`: Status of the data being in the "selected" status.
  - `subCVal`: Value used to substitute the data attribute `instCVal`.

**Extracted Text:**

```
61850-7-3 © IEC:2010 -73-
Data attribute name The value of a status setting.
Semantics
setVal The value of a status setting.
sev Severity of the last violation detected. The values are:
value unknown Severity cannot be determined.
critical Severity is critical in terms of safe operation or data considered critical and privileged access was attempted.
major Severity is major in terms of safe operation or data considered of major importance and privileged access was attempted.
minor Severity is minor in the sense that access control was denied to data considered privileged.
warning Is less severe than minor.
smpRate (HMV, HWYE, HDEL) Determines according to the sampling theorem the highest possible harmonic or interharmonic detectable. The minimum is 2 x frequency. The value shall represent the number of samples per nominal period. In the case of a d.c. system, the value shall represent the number of samples per s.
smpRate (MV, CMV, WYE, DEL) Sampling rate that has been used to determine the analogue values. The value shall represent the number of samples per nominal period. In the case of a d.c. system, the value shall represent the number of samples per s.
stepSize Defines the step between individual values that ctlVal (CDC INC, APC, BAC), setVal (CDC ING) or setMag (CDC ASG) will accept.
strTm Starting time of the freeze process. If the current time is later than the start time, the first freeze shall occur at the next freeze interval (frPd) expiration, computed from the start time setting.
stSeld The controllable data is in the status "selected".
stVal Status value of the data.
subCVal Value used to substitute the data attribute instCVal.
```

This table is crucial for understanding the semantics and usage of data attributes in IEC 61850, which is widely used in the automation of power systems.

---

## Page 77

![Image 1 from page 77](images/iec61850-7-3{ed2.0}b_page77_img63_57e70b0d.png)

**Image Type:** Table

**Description:** The image is a structured table that outlines the semantics of various data attributes and their corresponding values used in a specific context, likely related to data representation and configuration in a control system or similar domain. The table is divided into sections that detail how different attributes are used to substitute values, configure scaled values, and represent timestamps of changes.

**Key Elements:**

- **Data Attribute Name:** Lists the names of the data attributes.
- **Semantics:** Describes the purpose and usage of each data attribute.
- **Substitution Values (subVal):** Specifies the values used to substitute the attribute representing the value of the data instance.
- **Scaled Value Configuration (sVC):** Details how to configure scaled values for different data attributes.
- **Timestamp of Last Change (t):** Indicates the timestamp of the last change in the data or q attribute.
- **Activation of Control Command (tOpOk):** Describes the time stamp when a control command is activated.

**Extracted Text:**

```
61850-7-3 © IEC:2010 -75-

Data attribute Value used to substitute the attribute representing the value of the data instance. For the different CDCs, subVal is used to substitute the following data attributes: supval

Scaled value configuration. Shall be used to configure the scaled value representation. For the different CDCs, sVC applies to the following data attributes and service parameters: svc

APC mxVal, subVal, minVal, maxVal, stepSize; service

Timestamp of the last change in one of the attribute(s) representing the value of the data or in the q attribute. For the different CDCs, t applies to the following data attributes dirPhsC, neut, dirNeut

The time stamp with the time, when an output of a controllable object is activated following a control command. For details, see opRevd.
```

This table is part of a larger technical document, likely related to standards or specifications for data representation and control systems, as indicated by the IEC (International Electrotechnical Commission) copyright notice.

---

## Page 78

![Image 1 from page 78](images/iec61850-7-3{ed2.0}b_page78_img64_f2b197b3.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical diagram that illustrates the concept of switching between original and test data sources for a logical node (LN) in a control and automation system. It includes a table that lists data attributes and their semantics, as well as a flowchart that explains the process of switching between the original and test data sources.

**Key Elements:**

- **tstEna (Test Enable):** A logical node attribute that enables the use of a test data source.
- **LN yyyy (Original Data Source):** The original data source that the LN xxxx receives input from.
- **LN xxxx (Test Data Source):** The test data source that the LN xxxx can receive input from when tstEna is set to TRUE.
- **SPCSO1 (Test Signal):** A signal used for functional testing of the LN xxxx.
- **InRef1 (Input Reference):** A data attribute that points to the original or test data source.
- **setSrcRef (Set Source Reference):** A function that sets the original data source reference.
- **setTstRef (Set Test Reference):** A function that sets the test data source reference.
- **setSrcCB (Set Source Control Block):** A control block that manages the original data source.
- **setTstCB (Set Test Control Block):** A control block that manages the test data source.
- **valRev (Revision of Preconfiguration):** A revision identifier for the preconfiguration of configuration values in a logical device or logical node instance.
- **Units of Data Attributes:** The units of measurement for various data attributes, such as actVal, instMag, instCVal.mag, hstVal, mxVal, subVal, minVal, maxVal, stepSize, and setMag.

**Extracted Text:**

```plaintext
-76- 61850-7-3 © IEC:2010
Data attribute name Switch between original data source (as defined with setSrcRef and setSrcCB) for a reference and test data source (as defined with setTstRef and setTstCB). The concept is explained in the following drawing.
tstEna
tstEna=TRUE
IEC 2560/10
In a normal operation, the LN xxxx receives as an input the signal Out from LN yyyy. The data attribute xxxx.InRef1.setSrcRef points to yyyy.Out. For functional testing of the LN xxxx, a logical node GTST may be used to generate test patterns. In that case, the LN xxxx shall receive the input from LN GTST; e.g. the signal SPCSO1. This is indicated by the data attribute xxx.InRef1.setTstRef. By setting xxx.InRef1.tstEna to TRUE, the LN xxxx will start receiving the signal InRef1 from GTST instead of yyyy.
Units of the attribute(s) representing the value of the data. For the different CDCs, units applies to the following data attributes and service parameters:
CDC —— data attribute units applies to
BCR actVal, frVal, pulsQt
MV instMag, mag, subMag, rangeC
CMV instCVal.mag, cVal.mag, subCVal.mag, rangeC
CMV instCVal.mag, cVal.mag, subCVal.mag, rangeC
SAV instVal, min, max
HST hstVal
APC mxVal, subVal, minVal, maxVal, stepSize; service parameter ctlVal
BAC mxVal, subVal, minVal, maxVal, stepSize
BAC mxVal, subVal, minVal, maxVal, stepSize
ASG setMag, minVal, maxVal, stepSize
Uniquely identifies the revision of the preconfiguration of configuration values (FC= CF) in a logical device or logical node instance through a SCL file. ValRev has to be changed at least on any change of preconfigured values within an SCL file for this logical device or logical node. How this is detected and performed is left to the user. For further details, see as well Annex C.
valRev The change of ValRev shall be done with the following semantic:
— if the value change is done in the IED only through communication services or through the local HMI, the value shall be increased by one,
— if the value change is done in the configuration file, the value shall be increased by 10'000.
Value with transient indication.
Name of the vendor.
[xO __I Description of the value of the x-axis of a curve.
Description of the value of the x-axis of a curve in UNICODE
Unit of the x-axis of a curve.
Description of the value of the y

---

## Page 79

![Image 1 from page 79](images/iec61850-7-3{ed2.0}b_page79_img65_93311a70.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically from page 77 of a standard or specification document. The table is titled "Data attribute name" and "Semantics," and it describes a configuration parameter named "zeroDb." This parameter is used to calculate the range around zero, where the analogue value will be forced to zero. The value represents the percentage of difference between max and min in units of 0.001%. The table lists the data attributes to which the zeroDb parameter applies.

**Key Elements:**
- **Data attribute name:** zeroDb
- **Semantics:** Configuration parameter used to calculate the range around zero, where the analogue value will be forced to zero. The value represents the percentage of difference between max and min in units of 0.001%.
- **Data attributes to which zeroDb applies:**
  - MV (Magnitude)
  - CMV (Complex Magnitude)
  - cVal.mag (Complex Value Magnitude)
  - zUnits (Unit of the z-axis of a curve)

**Extracted Text:**
```
61850-7-3 © IEC:2010 -77-
Data attribute name
Semantics
zeroDb
Configuration parameter used to calculate the range around zero, where the analogue value will be forced to zero. The value shall represent the percentage of difference between max and min in units of 0,001 %. For the different CDCs, zeroDb applies to the following data attributes:
zeroDb data attribute zeroDb applies to
CDC
data attribute zeroDb applies to
MV
mag
CMV
cVal.mag
zUnits
Unit of the z-axis of a curve.
```

This table is part of a larger document, likely related to electrical or electronic engineering, as it discusses parameters used in signal processing or measurement systems. The document is copyrighted by IEC (International Electrotechnical Commission) and is dated 2010.

---

## Page 81

![Image 1 from page 81](images/iec61850-7-3{ed2.0}b_page81_img66_d4e8eda3.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC (International Electrotechnical Commission) standard 61850-7-3, published in 2010. The table is titled "Table A.3 – SI units: extended units" and provides a comprehensive list of SI units with their corresponding values, quantities, unit names, and symbols. The table is structured in four columns: Value, Quantity, Unit name, and Symbol. The table continues into a second part, "Table A.4 – SI units: industry specific units," which includes additional units related to electrical and industrial measurements.

**Key Elements:**
- **Table A.3 – SI units: extended units**
  - Value: Lists numerical values associated with various physical quantities.
  - Quantity: Describes the physical quantity being measured.
  - Unit name: Specifies the name of the unit.
  - Symbol: Provides the symbol used for the unit.
- **Table A.4 – SI units: industry specific units**
  - Value: Lists numerical values associated with various electrical and industrial quantities.
  - Quantity: Describes the electrical or industrial quantity being measured.
  - Unit name: Specifies the name of the unit.
  - Symbol: Provides the symbol used for the unit.

**Extracted Text:**
```plaintext
61850-7-3 © IEC:2010 -79- Table A.3 – SI units: extended units
Value Quantity Unit name Symbol
41 Area square meter (m²) m²
42 Volume cubic meter (m³) m³
43 Velocity meters per second (m/s) m/s
44 Acceleration meters per second² (m/s²) m/s²
45 Volumetric flow rate cubic meters per second (m³/s) m³/s
46 Fuel efficiency meters/cubic meter (m/m³) m/m³
47 Moment of mass kilogram meter (kg m) M
48 Density kilogram/cubic meter (kg/m³) kg/m³
49 Viscosity meter square/second (m²/s) m²/s
50 Thermal conductivity watt/meter Kelvin (W/m K) W/m K
51 Heat capacity joule/Kelvin (J/K) J/K
52 Concentration parts per million ppm
53 Rotational speed rotations per second (1/s) 1/s
54 Angular velocity rotations per second (rad/s) rad/s
55 Insolation watt per square meter W/m²
56 Insolation energy watt seconds per square meter J/m²
57 Electric conductivity siemens per meter S/m
58 Temperature change rate kelvin per second K/s
59 Pressure change rate pascal per second Pa/s
60 Specific heat joule per kilogram per kelvin J/kg K
61 Apparent power volt ampere (VA) VA
62 Real power watts (W) W
63 Reactive power volt ampere reactive (VAR) VAR
64 Phase angle degrees θ
65 Power factor (dimensionless) Cosθ
66 Volt seconds volt seconds (Ws/A) Vs
67 Volts squared volt square (W²/A²) V²
68 Amp seconds amp second (As) As
69 Amps squared amp square (A²) A²
70 Amps squared time amp square second (A²s) A²t
71 Apparent energy volt ampere hours (VAh) VAh
72 Real energy watt hours Wh
73 Reactive energy volt ampere reactive hours (VARh) VARh
74 Magnetic flux volts per second (A²s) A²t
75 Rate of change of frequency hertz per second Hz/s
76 Baud characters per second char/s
```

This table provides a detailed list of SI units and their corresponding symbols, which are essential for standardizing measurements in various fields, including electrical engineering and industrial applications.

---

## Page 82

![Image 1 from page 82](images/iec61850-7-3{ed2.0}b_page82_img67_5fef2108.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC:2010, page 80. It lists various physical quantities, their corresponding units, and symbols, along with a separate table of multipliers for unit conversion. The table is structured to provide a comprehensive reference for units of measurement used in electrical and mechanical engineering.

**Key Elements:**
- **Table A.5 - Multiplier:** This table lists multipliers for various units of measurement, ranging from Yocto (10^-24) to Yotta (10^24). Each multiplier is associated with a specific prefix and its corresponding symbol.
- **Physical Quantities and Units:** The main table lists physical quantities such as Turbine Inertia, Sound Pressure Level, Heat Rate, Ramp Rate, Flow Rate, and Power Level. Each quantity is paired with its unit name and symbol.
- **Unit Names and Symbols:** Examples include kg square meter (kgm²), joule per watt-hour (J/W·h), watt per second (W/s), and power measurement relative to 1 mW (dBm).

**Extracted Text:**

```
– 80 –
61850-7-3 © IEC:2010

Table A.5 – Multiplier

Value | Multiplier value | Name | Symbol
-24   | 10^-24           | Yocto | y
-21   | 10^-21           | Zepto | z
-18   | 10^-18           | Atto  | a
-15   | 10^-15           | Femto | f
-12   | 10^-12           | Pico  | p
-9    | 10^-9            | Nano  | n
-6    | 10^-6            | Micro | μ
-3    | 10^-3            | Milli | m
-2    | 10^-2            | Centi | c
-1    | 10^-1            | Deci  | d
0     | 1                |       |
1     | 10^1             |       |
2     | 10^2             | Hecto | h
3     | 10^3             | Kilo  | k
6     | 10^6             | Mega  | M
9     | 10^9             | Giga  | G
12    | 10^12            | Tera  | T
15    | 10^15            | Peta  | P
18    | 10^18            | Exa   | E
21    | 10^21            | Zetta | Z
24    | 10^24            | Yotta | Y

NOTE A value that is representing a percentage can use the unit 1 (dimensionless) and a multiplier –2.
```

This table provides a detailed reference for the units of measurement used in various technical fields, ensuring consistency and accuracy in documentation and calculations.

---

## Page 83

![Image 1 from page 83](images/iec61850-7-3{ed2.0}b_page83_img68_79a62947.png)

**Image Type:** Page from a technical standard document

**Description:** This image is a page from a technical standard document, specifically from IEC 61850-7-3, dated 2010. It is part of a series of documents related to the IEC 61850 standard, which is widely used in the field of smart grid communication and automation. The page is labeled as Annex B and is marked as informative. It discusses functional constraints, which are defined in a related document, IEC 61850-7-2. The text indicates that the functional constraints relevant to this part of the standard are repeated here for better readability.

**Key Elements:**
- **Standard Reference:** IEC 61850-7-3 © IEC:2010
- **Page Number:** 81
- **Annex B:** Informative
- **Functional Constraints:** Defined in IEC 61850-7-2
- **Relevance:** Relevant for this part of IEC 61850
- **Table Reference:** Table B.1

**Extracted Text:**
```
61850-7-3 © IEC:2010
– 81 –
Annex B
(informative)
Functional constraints

The functional constraints are defined in IEC 61850-7-2. Those that are relevant for this part of IEC 61850 are repeated here for better reading of the standard. See Table B.1.
```

This page serves as a reference for understanding the functional constraints within the context of the IEC 61850 standard, particularly for those parts of the standard that are relevant to this annex.

---

## Page 85

![Image 1 from page 85](images/iec61850-7-3{ed2.0}b_page85_img69_321b7e7b.png)

**Image Type:** Table

**Description:** The image is a structured table titled "Tracking of configuration revisions" from Annex C of a technical document. The table is divided into several columns and rows, detailing the impact and location of changes made to configuration elements within an IED (Intelligent Electronic Device). The table is organized to show how different types of changes affect the configuration of an IED, including the impact on data sets, control blocks, and communication services.

**Key Elements:**
- **Columns:** The table has columns for "Issue," "Impact / Comment," and "Where is the change made?" The "Where is the change made?" column is further divided into sub-columns for "In config file," "In IED only," "Through system or IED configuration tool," and "Communication services (where applicable) or local HMI."
- **Rows:** Each row represents a specific type of change, such as "Semantic change within a logical device / logical node," "Change of domain data model," "Content of data sets, presence of control blocks," and "Change of the value of a CF."
- **Data:** The table provides detailed information on how each change impacts the configuration and where the changes are made. For example, a semantic change within a logical device or logical node impacts the identification of the information and is made in the configuration file.

**Extracted Text:**
The OCR text is not clear and contains a mix of numbers, letters, and symbols that do not form coherent words or sentences. It appears to be a mix of random characters and possibly some technical symbols or codes. The text is not legible and does not provide any useful information for the analysis of the table.

**Technical Analysis:**
The table is a critical component of a technical document, likely related to the configuration management of IEDs in a control system. It outlines the impact of various changes on the configuration of an IED and specifies where these changes should be made. This is essential for maintaining the integrity and functionality of the system, ensuring that changes are documented and managed correctly to avoid errors and ensure system reliability.

The table is structured to help engineers and system administrators understand the implications of changes to the configuration of an IED, which can include modifications to data sets, control blocks, and communication services. This information is crucial for maintaining the system's operational integrity and ensuring that any changes are properly documented and managed.

---

## Page 86

![Image 1 from page 86](images/iec61850-7-3{ed2.0}b_page86_img70_dca62001.png)

**Image Type:** Schematic Diagram

**Description:** The image is a schematic diagram of SCL (Substation Configuration Language) enumerations, specifically from Annex D of a standard document. It outlines various types of enumerations used in the SCL language, which is commonly used in the context of IEC 61850 for configuration of substation automation systems. The enumerations are categorized into different types such as ControlOutputKind, CtlModelKind, SboClassKind, OriginatorCategoryKind, OccurrenceKind, and MonthKind.

**Key Elements:**
- EnumType: Defines the type of enumeration.
- EnumVal: Defines the values within each enumeration type.
- ord: Ordinal value for each enumeration value.
- id: Identifier for each enumeration type.

**Extracted Text:**
```plaintext
— 84- 61850-7-3 © IEC:2010
Annex D (normative)
SCL (normative)
SCL enumerations

<EnumType id="ControlOutputKind">
    <EnumVal ord="0">pulse</EnumVal>
    <EnumVal ord="1">persistent</EnumVal>
</EnumType>
<EnumType id="CtlModelKind">
    <EnumVal ord="0">status-only</EnumVal>
    <EnumVal ord="1">direct-with-normal-security</EnumVal>
    <EnumVal ord="2">sbo-with-normal-security</EnumVal>
    <EnumVal ord="3">direct-with-enhanced-security</EnumVal>
    <EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
</EnumType>
<EnumType id="SboClassKind">
    <EnumVal ord="0">operate-once</EnumVal>
    <EnumVal ord="1">operate-many</EnumVal>
</EnumType>
<EnumType id="OriginatorCategoryKind">
    <EnumVal ord="0">not-supported</EnumVal>
    <EnumVal ord="1">bay-control</EnumVal>
    <EnumVal ord="2">station-control</EnumVal>
    <EnumVal ord="3">remote-control</EnumVal>
    <EnumVal ord="4">automatic-bay</EnumVal>
    <EnumVal ord="5">automatic-station</EnumVal>
    <EnumVal ord="6">automatic-remote</EnumVal>
    <EnumVal ord="7">maintenance</EnumVal>
    <EnumVal ord="8">process</EnumVal>
</EnumType>
<EnumType id="OccurrenceKind">
    <EnumVal ord="0">Time</EnumVal>
    <EnumVal ord="1">WeekDay</EnumVal>
    <EnumVal ord="2">WeekOfYear</EnumVal>
    <EnumVal ord="3">DayOfMonth</EnumVal>
    <EnumVal ord="4">DayOfYear</EnumVal>
</EnumType>
<EnumType id="MonthKind">
    <EnumVal ord="0">reserved</EnumVal>
    <EnumVal ord="1">January</EnumVal>
    <EnumVal ord="2">February</EnumVal>
    <EnumVal ord="3">March</EnumVal>
    <EnumVal ord="4">April</EnumVal>
    <EnumVal ord="5">May</EnumVal>
    <EnumVal ord="6">June</EnumVal>
    <EnumVal ord="7">July</EnumVal>
</EnumType>
```

This analysis provides a clear understanding of the structure and content of the SCL enumerations as presented in the image, which is crucial for anyone working with IEC 61850 standards for substation automation systems.

---

## Page 87

![Image 1 from page 87](images/iec61850-7-3{ed2.0}b_page87_img71_17560d0a.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The XML document appears to be part of a larger schema or standard, as indicated by the copyright notice at the top, which references IEC:2010.

**Key Elements:**
- The document is structured using XML tags, with each enumeration type defined using `<EnumType>` tags.
- Each enumeration type is identified by an `id` attribute, such as `PeriodKind`, `DaWeekdayKind`, `FaultDirectionKind`, `PhaseFaultDirectionKind`, `SeverityKind`, and `RangeKind`.
- Each enumeration type contains a series of `<EnumVal>` tags, which define the possible values for the enumeration, each with an `ord` attribute indicating its order or position within the enumeration.
- The values within the `<EnumVal>` tags are descriptive and represent specific concepts or states, such as months, weekdays, directions, severity levels, and ranges.

**Extracted Text:**
```xml
61850-7-3 © IEC:2010 -85-
<EnumVal ord="8">August</EnumVal>
<EnumVal ord="9">September</EnumVal>
<EnumVal ord="10">October</EnumVal>
<EnumVal ord="11">November</EnumVal>
<EnumVal ord="12">December</EnumVal>
</EnumType>
<EnumType id="PeriodKind">
<EnumVal ord="0">Hour</EnumVal>
<EnumVal ord="1">Day</EnumVal>
<EnumVal ord="2">Week</EnumVal>
<EnumVal ord="3">Month</EnumVal>
<EnumVal ord="4">Year</EnumVal>
</EnumType>
<EnumType id="DaWeekdayKind">
<EnumVal ord="0">reserved</EnumVal>
<EnumVal ord="1">Monday</EnumVal>
<EnumVal ord="2">Tuesday</EnumVal>
<EnumVal ord="3">Wednesday</EnumVal>
<EnumVal ord="4">Thursday</EnumVal>
<EnumVal ord="5">Friday</EnumVal>
<EnumVal ord="6">Saturday</EnumVal>
<EnumVal ord="7">Sunday</EnumVal>
</EnumType>
<EnumType id="FaultDirectionKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">forward</EnumVal>
<EnumVal ord="2">backward</EnumVal>
<EnumVal ord="3">both</EnumVal>
</EnumType>
<EnumType id="PhaseFaultDirectionKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">forward</EnumVal>
<EnumVal ord="2">backward</EnumVal>
</EnumType>
<EnumType id="SeverityKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">critical</EnumVal>
<EnumVal ord="2">major</EnumVal>
<EnumVal ord="3">minor</EnumVal>
<EnumVal ord="4">warning</EnumVal>
</EnumType>
<EnumType id="RangeKind">
<EnumVal ord="0">normal</EnumVal>
<EnumVal ord="1">high</EnumVal>
<EnumVal ord="2">low</EnumVal>
<EnumVal ord="3">high-high</EnumVal>
<EnumVal ord="4">low-low</EnumVal>
</EnumType>
```

This XML document is likely part of a larger standard or schema used for defining and managing enumerations in a structured format, which is common in technical documentation, configuration files, or data models.

---

## Page 88

![Image 1 from page 88](images/iec61850-7-3{ed2.0}b_page88_img72_aceebf91.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The XML document appears to be part of a larger technical specification, likely related to electrical engineering or standards, as indicated by the copyright notice from IEC (International Electrotechnical Commission).

**Key Elements:**
- The document is structured using XML tags, with each enumeration type defined by `<EnumType>` tags.
- Each enumeration type is identified by an `id` attribute, such as `AngleReferenceKind`, `PhaseAngleReferenceKind`, `PhaseReferenceKind`, `SequenceKind`, `HvReferenceKind`, and `CurveCharKind`.
- Each enumeration type contains a series of `<EnumVal>` tags, each with an `ord` attribute that defines the order of the enumeration value and a corresponding value, such as "V", "A", "other", "Synchrophasor", etc.
- The XML document is formatted with indentation to improve readability, which is a common practice in XML to denote the hierarchical structure of the document.

**Extracted Text:**
```xml
<EnumType id="AngleReferenceKind">
    <EnumVal ord="0">V</EnumVal>
    <EnumVal ord="1">A</EnumVal>
    <EnumVal ord="2">other</EnumVal>
    <EnumVal ord="3">Synchrophasor</EnumVal>
</EnumType>
<EnumType id="PhaseAngleReferenceKind">
    <EnumVal ord="0">Va</EnumVal>
    <EnumVal ord="1">Vb</EnumVal>
    <EnumVal ord="2">Vc</EnumVal>
    <EnumVal ord="3">Aa</EnumVal>
    <EnumVal ord="4">Ab</EnumVal>
    <EnumVal ord="5">Ac</EnumVal>
    <EnumVal ord="6">Vab</EnumVal>
    <EnumVal ord="7">Vbc</EnumVal>
    <EnumVal ord="8">Vca</EnumVal>
    <EnumVal ord="9">Vother</EnumVal>
    <EnumVal ord="10">Aother</EnumVal>
    <EnumVal ord="11">Synchrophasor</EnumVal>
</EnumType>
<EnumType id="PhaseReferenceKind">
    <EnumVal ord="0">A</EnumVal>
    <EnumVal ord="1">B</EnumVal>
    <EnumVal ord="2">C</EnumVal>
</EnumType>
<EnumType id="SequenceKind">
    <EnumVal ord="0">pos-neg-zero</EnumVal>
    <EnumVal ord="1">dir-quad-zero</EnumVal>
</EnumType>
<EnumType id="HvReferenceKind">
    <EnumVal ord="0">fundamental</EnumVal>
    <EnumVal ord="1">rms</EnumVal>
    <EnumVal ord="2">absolute</EnumVal>
</EnumType>
<EnumType id="CurveCharKind">
    <EnumVal ord="0">none</EnumVal>
    <EnumVal ord="1">ANSI Extremely Inverse</EnumVal>
    <EnumVal ord="2">ANSI Very Inverse</EnumVal>
    <EnumVal ord="3">ANSI Normal Inverse</EnumVal>
    <EnumVal ord="4">ANSI Moderate Inverse</EnumVal>
    <EnumVal ord="5">ANSI Definite Time</EnumVal>
    <EnumVal ord="6">Long-Time Extremely Inverse</EnumVal>
    <EnumVal ord="7">Long-Time Very Inverse</EnumVal>
    <EnumVal ord="8">Long-Time Inverse</EnumVal>
    <EnumVal ord="9">IEC Normal Inverse</EnumVal>
    <EnumVal ord="10">IEC Very Inverse</EnumVal>
</EnumType>
```

This XML document is likely part of a larger technical specification, possibly related to electrical engineering standards, as indicated by the copyright notice from IEC. The document defines various enumeration types, each with a specific set of values and their corresponding orders, which are used to represent different types of electrical quantities or characteristics.

---

## Page 89

![Image 1 from page 89](images/iec61850-7-3{ed2.0}b_page89_img73_eee3f02c.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, likely part of a larger technical specification or standard. The document appears to be structured with enumerated values (EnumVal) and types (EnumType) related to various mathematical and computational concepts, such as inverse operations and polynomial types.

**Key Elements:**
- **EnumVal:** These are enumerated values with specific identifiers (ord) and descriptions. They represent different types of inverse operations (e.g., IEC Inverse, IEC Extremely Inverse) and polynomial types (e.g., Polynom 1, Polynom 2).
- **EnumType:** This section defines the types of multipliers (MultiplierKind) with corresponding values (ord) and descriptions (e.g., y, z, a, f, p, n, y, μ).
- **XML Structure:** The document is formatted in XML, with tags such as `<EnumVal>`, `<EnumType>`, and `<EnumType id="MultiplierKind">` indicating the structure and organization of the data.

**Extracted Text:**
```xml
61850-7-3 © IEC:2010 -87-
<EnumVal ord="11">IEC Inverse</EnumVal>
<EnumVal ord="12">IEC Extremely Inverse</EnumVal>
<EnumVal ord="13">IEC Short-Time Inverse</EnumVal>
<EnumVal ord="14">IEC Long-Time Inverse</EnumVal>
<EnumVal ord="15">IEC Definite Time</EnumVal>
<EnumVal ord="16">Reserved</EnumVal>
<EnumVal ord="17">Polynom 1</EnumVal>
<EnumVal ord="18">Polynom 2</EnumVal>
<EnumVal ord="19">Polynom 3</EnumVal>
<EnumVal ord="20">Polynom 4</EnumVal>
<EnumVal ord="21">Polynom 5</EnumVal>
<EnumVal ord="22">Polynom 6</EnumVal>
<EnumVal ord="23">Polynom 7</EnumVal>
<EnumVal ord="24">Polynom 8</EnumVal>
<EnumVal ord="25">Polynom 9</EnumVal>
<EnumVal ord="26">Polynom 10</EnumVal>
<EnumVal ord="27">Polynom 11</EnumVal>
<EnumVal ord="28">Polynom 12</EnumVal>
<EnumVal ord="29">Polynom 13</EnumVal>
<EnumVal ord="30">Polynom 14</EnumVal>
<EnumVal ord="31">Polynom 15</EnumVal>
<EnumVal ord="32">Polynom 16</EnumVal>
<EnumVal ord="33">Multiline 1</EnumVal>
<EnumVal ord="34">Multiline 2</EnumVal>
<EnumVal ord="35">Multiline 3</EnumVal>
<EnumVal ord="36">Multiline 4</EnumVal>
<EnumVal ord="37">Multiline 5</EnumVal>
<EnumVal ord="38">Multiline 6</EnumVal>
<EnumVal ord="39">Multiline 7</EnumVal>
<EnumVal ord="40">Multiline 8</EnumVal>
<EnumVal ord="41">Multiline 9</EnumVal>
<EnumVal ord="42">Multiline 10</EnumVal>
<EnumVal ord="43">Multiline 11</EnumVal>
<EnumVal ord="44">Multiline 12</EnumVal>
<EnumVal ord="45">Multiline 13</EnumVal>
<EnumVal ord="46">Multiline 14</EnumVal>
<EnumVal ord="47">Multiline 15</EnumVal>
<EnumVal ord="48">Multiline 16</EnumVal>
</EnumType>
<EnumType id="MultiplierKind">
<EnumVal ord="-24">y</EnumVal>
<EnumVal ord="-21">z</EnumVal>
<EnumVal ord="-18">a</EnumVal>
<EnumVal ord="-15">f</EnumVal>
<EnumVal ord="-12">p</EnumVal>
<EnumVal ord="-9">n</EnumVal>
<EnumVal ord="-6">y</EnumVal>
</EnumType>
```

This XML document appears to be part of a larger technical standard or specification, possibly related to electrical

---

## Page 90

![Image 1 from page 90](images/iec61850-7-3{ed2.0}b_page90_img74_5edaa7ed.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The XML structure includes nested `<EnumType>` elements, each defining a set of enumeration values (`<EnumVal>`). The document appears to be related to the International Electrotechnical Commission (IEC) standard, as indicated by the copyright notice at the bottom.

**Key Elements:**
- `<EnumType>` elements: These define different types of enumerations.
- `<EnumVal>` elements: These define individual enumeration values, each with an associated ordinal value (`ord` attribute).
- `id` attribute: Used to uniquely identify the `<EnumType>` element.
- Ordinal values: These are integer values assigned to each enumeration value, starting from 0 and incrementing sequentially.

**Extracted Text:**
```xml
<EnumVal ord="-3">m</EnumVal>
<EnumVal ord="-2">c</EnumVal>
<EnumVal ord="-1">d</EnumVal>
<EnumVal ord="0"></EnumVal>
<EnumVal ord="1">da</EnumVal>
<EnumVal ord="2">h</EnumVal>
<EnumVal ord="3">k</EnumVal>
<EnumVal ord="6">M</EnumVal>
<EnumVal ord="9">G</EnumVal>
<EnumVal ord="12">T</EnumVal>
<EnumVal ord="15">P</EnumVal>
<EnumVal ord="18">E</EnumVal>
<EnumVal ord="21">Z</EnumVal>
<EnumVal ord="24">Y</EnumVal>
</EnumType>
<EnumType id="SIUnitKind">
<EnumVal ord="1"></EnumVal>
<EnumVal ord="2">m</EnumVal>
<EnumVal ord="3">kg</EnumVal>
<EnumVal ord="4">s</EnumVal>
<EnumVal ord="5">A</EnumVal>
<EnumVal ord="6">K</EnumVal>
<EnumVal ord="7">mol</EnumVal>
<EnumVal ord="8">cd</EnumVal>
<EnumVal ord="9">deg</EnumVal>
<EnumVal ord="10">rad</EnumVal>
<EnumVal ord="11">sr</EnumVal>
<EnumVal ord="21">Gy</EnumVal>
<EnumVal ord="22">Bq</EnumVal>
<EnumVal ord="23">°C</EnumVal>
<EnumVal ord="24">Sv</EnumVal>
<EnumVal ord="25">F</EnumVal>
<EnumVal ord="26">C</EnumVal>
<EnumVal ord="27">S</EnumVal>
<EnumVal ord="28">H</EnumVal>
<EnumVal ord="29">V</EnumVal>
<EnumVal ord="30">ohm</EnumVal>
<EnumVal ord="31">J</EnumVal>
<EnumVal ord="32">N</EnumVal>
<EnumVal ord="33">Hz</EnumVal>
<EnumVal ord="34">Ix</EnumVal>
<EnumVal ord="35">Lm</EnumVal>
<EnumVal ord="36">Wb</EnumVal>
<EnumVal ord="37">T</EnumVal>
<EnumVal ord="38">W</EnumVal>
<EnumVal ord="39">Pa</EnumVal>
<EnumVal ord="41">m²</EnumVal>
</EnumType>
```

**Technical Analysis:**
- The XML document is structured to define different types of enumerations, with the first `<EnumType>` defining a set of values with negative ordinal values, and the second `<EnumType>` defining a set of SI unit kinds with positive ordinal values.
- The `<EnumVal>` elements are used to define the specific values within each enumeration type, with each value having an associated ordinal value.
- The `id` attribute in the second `<EnumType>` is used to uniquely identify the type of enumeration, which in this case is "SIUnitKind".
- The ordinal values range from -3 to 41, indicating a wide range of enumeration values, which could be used to represent various physical quantities or units in the context of the IEC standard.

This XML structure is likely part of a larger document that defines various enumerations for use in technical specifications or standards, such as those published by the IEC.

---

## Page 91

![Image 1 from page 91](images/iec61850-7-3{ed2.0}b_page91_img75_0729930b.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically page 89 of a document with the reference number 61850-7-3 © IEC:2010. The table lists various EnumVal (enumerated values) with their corresponding numerical identifiers and physical units or derived units. These values are part of a larger enumeration system, likely used for standardizing units or measurements in a specific technical context.

**Key Elements:**
- The table is structured with EnumVal entries, each with a unique identifier (ord) and a corresponding value.
- The values represent various physical quantities, such as length (m), speed (m/s), power (W), and other derived units.
- The table includes a range of values from 42 to 83, indicating a comprehensive list of units or measurements.

**Extracted Text:**
```plaintext
61850-7-3 © IEC:2010 -89-
<EnumVal ord="42">m?</EnumVal>
<EnumVal ord="43">m/s</EnumVal>
<EnumVal ord="44">m/s*</EnumVal>
<EnumVal ord="45">m*/s</EnumVal>
<EnumVal ord="46">m/m?</EnumVal>
<EnumVal ord="47">M</EnumVal>
<EnumVal ord="48">kg/m*</EnumVal>
<EnumVal ord="49">m?/s</EnumVal>
<EnumVal ord="50">W/m K</EnumVal>
<EnumVal ord="51">J/K</EnumVal>
<EnumVal ord="52">ppm</EnumVal>
<EnumVal ord="53">1/s</EnumVal>
<EnumVal ord="54">rad/s</EnumVal>
<EnumVal ord="55">W/m?</EnumVal>
<EnumVal ord="56">J/m?</EnumVal>
<EnumVal ord="57">S/m</EnumVal>
<EnumVal ord="58">K/s</EnumVal>
<EnumVal ord="59">Pa/s</EnumVal>
<EnumVal ord="60">J/kg K</EnumVal>
<EnumVal ord="61">VA</EnumVal>
<EnumVal ord="62">Watts</EnumVal>
<EnumVal ord="63">VAr</EnumVal>
<EnumVal ord="64">phi</EnumVal>
<EnumVal ord="65">cos(phi)</EnumVal>
<EnumVal ord="66">Vs</EnumVal>
<EnumVal ord="67">V?</EnumVal>
<EnumVal ord="68">As</EnumVal>
<EnumVal ord="69">A?</EnumVal>
<EnumVal ord="70">A*t</EnumVal>
<EnumVal ord="71">VAh</EnumVal>
<EnumVal ord="72">Wh</EnumVal>
<EnumVal ord="73">VArh</EnumVal>
<EnumVal ord="74">V/Hz</EnumVal>
<EnumVal ord="75">Hz/s</EnumVal>
<EnumVal ord="76">char</EnumVal>
<EnumVal ord="77">char/s</EnumVal>
<EnumVal ord="78">kgm?</EnumVal>
<EnumVal ord="79">dB</EnumVal>
<EnumVal ord="80">J/Wh</EnumVal>
<EnumVal ord="81">W/s</EnumVal>
<EnumVal ord="82">I/s</EnumVal>
<EnumVal ord="83">dBm</EnumVal>
</EnumType>
```

This table is part of a larger document, likely related to standards or specifications for measurements and units, as indicated by the copyright notice and the reference to IEC (International Electrotechnical Commission).

---

## Page 92

![Image 1 from page 92](images/iec61850-7-3{ed2.0}b_page92_img76_e2e11e8c.png)

**Image Type:** Bibliography Page

**Description:** The image is a page from a technical document, specifically a bibliography section. It lists various standards and specifications related to communication networks and systems for power utility automation, as well as industrial automation systems. The page is formatted with a title "Bibliography" at the top, followed by references to IEC and ISO standards.

**Key Elements:**
- Title: Bibliography
- Standards listed:
  - IEC 61850-8-x (all parts): Communication networks and systems for power utility automation – Part 8: Specific Communication Service Mapping (SCSM)
  - IEC 61850-9-x (all parts): Communication networks and systems for power utility automation – Part 9: Specific Communication Service Mapping (SCSM)
  - ISO 9506 (all parts): Industrial automation systems – Manufacturing Message Specification
- Page number: 90
- Copyright notice: © IEC:2010
- Customer information: Phil Young, TriangleMicroworks, Order No.: WS-2011-009821

**Extracted Text:**
```
– 90 –
61850-7-3 © IEC:2010

Bibliography

IEC 61850-8-x (all parts), Communication networks and systems for power utility automation – Part 8: Specific Communication Service Mapping (SCSM)

IEC 61850-9-x (all parts), Communication networks and systems for power utility automation – Part 9: Specific Communication Service Mapping (SCSM)

ISO 9506 (all parts), Industrial automation systems – Manufacturing Message Specification

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 93

![Image 1 from page 93](images/iec61850-7-3{ed2.0}b_page93_img77_bfb32b20.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank or placeholder diagram, possibly used in a technical document to indicate a section where a diagram should be inserted. It is likely part of a larger document, such as a technical manual or a presentation, where the actual diagram is meant to be placed but has not been included.

**Key Elements:** 
- The image is completely white, indicating no content or a placeholder.
- There are no technical elements, components, or data present in the image.
- The OCR text detected in the image is not available, as indicated by the instruction.

**Extracted Text:** No text detected

This image serves as a placeholder for a diagram that should be included in the technical document. It is important to ensure that the actual diagram is inserted in the correct location to maintain the integrity and completeness of the document.

---

## Page 98

![Image 1 from page 98](images/iec61850-7-3{ed2.0}b_page98_img78_be70015f.png)

**Image Type:** Table of Contents

**Description:** This image is a table of contents page from a technical document. It lists various sections and their corresponding page numbers. The document appears to be related to standards or specifications, as indicated by the reference to "CEI:2010" and the use of SI units (International System of Units).

**Key Elements:**
- Section titles and their page numbers.
- References to specific tables (e.g., Tableau A.3, Tableau A.4, Tableau A.5, Tableau B.1).
- Page numbers for each section.

**Extracted Text:**
```
96 - 61850-7-3 © CEI:2010
Tableau A.3 – Unités SI: unités étendues................................................... 169
Tableau A.4 – Unités SI: unités spécifiques à l'industrie................................ 169
Tableau A.5 – Multiplicateur........................................................................... 170
Tableau B.1 – Contraintes fonctionnelles....................................................... 172
```

**Additional Information:**
- The document is copyrighted by CEI (Comité Electrotechnique International) in 2010.
- The document includes tables related to SI units, extended units, specific industrial units, a multiplier, and functional constraints.
- The customer is identified as Phil Young, and the company is TriangleMicroworks.
- The order number is WS-2011-009821.
- The document is subject to a license agreement, and contact information for inquiries is provided.

---

## Page 100

![Image 1 from page 100](images/iec61850-7-3{ed2.0}b_page100_img79_a0037642.png)

**Image Type:** Document Page
**Description:** The image is a page from a technical document, specifically a standard or normative document related to electrical systems automation. The document appears to be part of a series under the CEI (Comité Electrotechnique International) and is structured according to ISO/CEI Directives, Part 2. It discusses the content and structure of a series of documents related to communication networks and systems for electrical systems automation.

**Key Elements:**
- The document is part of the CEI 61850 series.
- It is structured according to ISO/CEI Directives, Part 2.
- The content of the publication will not be modified before a specified date of stability.
- The document discusses the transition from a general title to a more specific title related to electrical systems automation.
- The document includes a note about the "colour inside" logo, indicating that the document contains color, which is useful for understanding its content.

**Extracted Text:**
```
98 - 61850-7-3 © CEI:2010
Le texte anglais de cette norme est issu des documents 57/1087/FDIS et 57/1095/RVD. Le rapport de vote 57/1095/RVD donne toute information sur le vote ayant abouti à l'approbation de cette norme. La version française de cette norme n'a pas été soumise au vote. Cette publication a été rédigée selon les Directives ISO/CEI, Partie 2. Une liste de toutes les parties de la série CEI 61850, sous le titre général: Réseaux et systèmes de communication pour l'automatisation des systèmes électriques, peut être consultée sur le site web de la CEI. Le titre général de cette série était Réseaux et systèmes de communication dans des postes. Pour correspondre à l'extension du domaine d'application de la CEI 61850, le titre général a été remplacé par Réseaux et systèmes de communication pour l'automatisation des systèmes électriques. Le comité a décidé que le contenu de cette publication ne sera pas modifié avant la date de stabilité indiquée sur le site web de la CEI sous «http://webstore.iec.ch» dans les données relatives à la publication recherchée. A cette date, la publication sera
- reconduite,
- supprimée,
- remplacée par une édition révisée, ou
- amendée.
IMPORTANT – Le logo "colour inside" qui se trouve sur la page de couverture de cette publication indique qu'elle contient des couleurs qui sont considérées comme utiles à une bonne compréhension de son contenu. Les utilisateurs devraient, par conséquent, imprimer cette publication en utilisant une imprimante couleur.
```

This document provides a clear structure and content description for a series of technical standards related to electrical systems automation, emphasizing the importance of color in the document for better understanding.

---

## Page 101

![Image 1 from page 101](images/iec61850-7-3{ed2.0}b_page101_img80_de4d5a9b.png)

**Image Type:** Document Page

**Description:** The image is a page from a technical document, specifically from the CEI 61850 series, which is a standard for communication networks in power systems. The page is titled "INTRODUCTION" and discusses the architecture of communication for a post (a power system component). It explains the use of abstract class definitions and service definitions to make the specifications independent of specific protocol stacks and objects. The document references other parts of the CEI 61850 series for more detailed information on data objects and their attributes.

**Key Elements:**
- CEI 61850-7-3 standard
- CEI 61850-8-x (bus de poste)
- CEI 61850-9-x (bus de processus)
- CEI 61850-7-1 (overview of the communication architecture)
- CEI 61850-7-2 (definition of abstract class and attribute definitions)
- CEI 61850-7-4 (definition of compatible dataObject classes)
- MMS (Manufacturing Message Specification, ISO 9506 series)
- SubDataObjects, DataAttributes, SubAttributes
- DataObject instances
- Abstract class definitions
- Concrete object definitions for specific protocols

**Extracted Text:**
61850-7-3 © CEI:2010 -99- INTRODUCTION
Le présent document fait partie d'un ensemble de spécifications qui donne en détail une architecture de communication stratifiée d'un poste. Cette architecture a été choisie pour fournir des définitions abstraites de classes et de services de manière à rendre les spécifications indépendantes de toute pile protocolaire spécifique et de tout objet spécifique. Le mappage de ces classes abstraites et de services à des piles de communication ne relève pas du domaine d'application de la CEI 61850-7-x et peut être consulté dans la CEI 61850-8-x (bus de poste) et dans la CEI 61850-9-x (bus de processus).
La CEI 61850-7-1 donne une vue d'ensemble de cette architecture de communication. La présente partie de la CEI 61850 définit des classes d'attributs construits et des classes de données communes liées à des applications dans les systèmes électriques utilisant les concepts de modélisation selon la CEI 61850 tels que les postes, l'hydroélectricité ou les ressources énergétiques distribuées. Ces classes de données communes sont utilisées dans la CEI 61850-7-4 pour définir des classes dataObject compatibles. Les SubDataObjects, DataAttributes ou SubAttributes des instances de dataObject doivent être accessibles en utilisant des services définis dans la CEI 61850-7-2.
La présente partie est utilisée pour spécifier les définitions de classe de données communes abstraite et de classe d'attributs construits. Ces définitions abstraites sont mappées à des définitions d'objets concrets qui sont à utiliser pour un protocole particulier (par exemple MMS, série ISO 9506).
Remarquer qu'il existe des classes de données communes utilisées pour le suivi de service, qui sont définies dans la CEI 61850-7-2.

This document is part of a series of specifications that detail a stratified communication architecture for a post. It explains the use of abstract class definitions and service definitions to make the specifications independent of specific protocol stacks and objects. The document references other parts of the CEI 61850 series for more detailed information on data objects and their attributes.

---

## Page 108

![Image 1 from page 108](images/iec61850-7-3{ed2.0}b_page108_img81_fc7cb606.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that illustrates the concept of quality identifiers in a simple client-server relationship. It includes a flowchart and textual explanations to describe how quality identifiers are used to reflect the quality of information in a server and how this information is provided to a client. The diagram also explains how quality identifiers are used to block updates to a value if another update has been blocked by an operator, and how the quality of the information is determined based on potential sources of influence.

**Key Elements:**

- **Client**: Represents the client in the client-server relationship.
- **Server**: Represents the server in the client-server relationship.
- **Unité d'entrée**: Represents the input unit.
- **Réseau de communication**: Represents the communication network.
- **Information Source**: Represents the source of information.
- **oldData**: Represents the old data.
- **Substituted**: Represents the substituted data.
- **Invalid / questionable**: Represents the quality of the information being invalid or questionable.
- **overFlow**: Represents the overflow condition.
- **outOfRange**: Represents the out-of-range condition.
- **badReference**: Represents the bad reference condition.
- **oscillatory**: Represents the oscillatory condition.
- **Failure**: Represents the failure condition.

**Extracted Text:**

```
— 106 - 61850-7-3 © CEI:2010
6.2.6 Gelé par l'opérateur
operatorBlocked: cet identificateur doit être mis si une autre mise à jour de la valeur a été bloquée par un opérateur. La valeur doit être l'information qui a été acquise avant le blocage. Si cet identificateur est mis, l'identificateur oldData de detailQual doit aussi être mis. L'opérateur doit utiliser l'attribut de donnée blkEna pour bloquer la mise à jour de la valeur.
NOTE Un opérateur et aussi une fonction automatique peuvent geler la mise à jour des communications et aussi la mise à jour des données d'entrée. Dans les deux cas, detailQual.oldData sera mis. Si le blocage est effectué par un opérateur, l'identificateur operatorBlocked est mis en plus. Dans ce cas, une activité d'opérateur est requise pour éliminer l'état.
EXEMPLE Un opérateur peut geler la mise à jour d'une donnée d'entrée, afin de sauver l'ancienne valeur avant que l'alimentation auxiliaire soit mise hors tension.
6.2.7 Qualité dans le contexte client-serveur
Source d'information
Unité d'entrée
Réseau de communication
Invalid / questionable
overFlow
Substituted
outOfRange
badReference
oscillatory
Failure
Questionable
oldData
CEI 808/03
Figure 1- Identificateurs de qualité dans une relation client-serveur simple
L'identificateur de qualité doit refléter la qualité de l'information dans le serveur, telle qu'elle est fournie au client. La Figure 1 montre des sources potentielles qui peuvent influencer la qualité dans une relation client - serveur simple. "Information Source" (c'est-à-dire la source d'information) est le raccordement (cablé) de l'information sur le processus au système. L'information peut être "non valide" (invalid) ou "douteuse" (questionable) comme montré dans la Figure 1. Un comportement anormal supplémentaire de la source d'information peut être détecté par l'unité d'entrée. Dans ce cas, l'unité d'entrée peut conserver l'ancienne donnée et la signaler en conséquence.
Dans une relation client-serveur multiple, telle que montrée à la Figure 2, les informations peuvent être acquises sur une liaison de communication (avec le Client B). Si cette liaison de communication est rompue, le client B détectera cette situation d'erreur et qualifiera les informations comme étant douteuses/anciennes.
```

This technical analysis provides a clear understanding of the diagram and the associated text, which is essential for technical documentation and search.

---

## Page 110

![Image 1 from page 110](images/iec61850-7-3{ed2.0}b_page110_img82_6e3894da.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the interaction of substitution and validity in a system, as described in the accompanying text. It outlines four scenarios (A, B, C, and D) and their effects on the quality of information and communication within a hierarchical system. The diagram uses symbols and labels to represent different levels and states of information quality.

**Key Elements:**
- **Cas A, B, C, D:** These represent different scenarios or cases being analyzed.
- **Niveau poste (Post Level):** Represents the highest level of the system.
- **Niveau baie (Bay Level):** Represents the intermediate level.
- **Niveau processus (Process Level):** Represents the lowest level of the system.
- **CL (Client):** Indicates the client level.
- **SE (Server):** Indicates the server level.
- **Substituted:** Indicates that the information has been replaced or updated.
- **Validity = quest:** Indicates that the validity of the information is questionable.
- **oldData:** Indicates that the data is old.
- **Défaillance de communication (Communication Failure):** Indicates a failure in communication between levels.

**Extracted Text:**

```
— 108 – 61850-7-3 © CEI:2010
Dans le cas A, l'entrée est bloquée, la qualité de l'information est marquée comme étant "questionable "(c'est-a-dire douteuse) et oldData.
Dans le cas B, une substitution est effectuée au niveau processus. En l'occurrence, la qualité de l'information au niveau supérieur suivant (le niveau baie) est marquée comme étant "substituted" (mais "good").
Dans le cas C, la communication entre les niveaux processus et baie échoue. Entre le niveau baie et le niveau poste, l'information est encore marquée comme étant "substituted" (c'est-a-dire substituée). En outre, questionable et oldData sont mis pour indiquer que l'information (substituted) peut être old (c'est-a-dire ancienne).
Dans le cas D, une nouvelle substitution est effectuée au niveau baie. En l'occurrence, la qualité de l'information au niveau supérieur suivant est marquée comme étant "substituted" (mais "good") et elle est indépendante de la première substitution.
Cas A
Cas B
Niveau poste
Niveau baie
Niveau processus
CL
SE
CL
SE
Validity = quest
Substituted
(oldData)
Validity = quest
Substituted
(oldData)
Substituted
Niveau poste
Niveau baie
Niveau processus
CL
SE
CL
SE
Substituted, Substituted
validity = quest
(oldData)
Substitution
Défaillance de
communication
Défaillance de
communication
Substitution
Substitution
L'entrée est bloquée
L'entrée est bloquée
L'entrée est bloquée
L'entrée est bloquée
Figure 3 – Interaction de substitution et validity
IEC 2550/10
Légende
CL client
SE serveur
```

This technical analysis provides a clear understanding of the diagram's purpose and the scenarios it illustrates, making it useful for technical documentation and search.

---

## Page 113

![Image 1 from page 113](images/iec61850-7-3{ed2.0}b_page113_img83_1f962ac5.png)

**Image Type:** Technical Document Page

**Description:** This image is a page from a technical manual or specification document, specifically from CEI:2010, page 111. It discusses the configuration of pulse outputs in a control system, detailing the attributes and values associated with pulse configuration and originator types. The document includes definitions, tables, and diagrams to explain the configuration parameters and their usage.

**Key Elements:**
- **Pulse Configuration Table (Tableau 8):** Defines the attributes and their values for configuring pulse outputs, including `cmdQual`, `onDur`, `offDur`, and `numPls`.
- **Originator Table (Tableau 9):** Defines the attributes and their values for the originator type, including `orCat` and `orId`.
- **Figure 4:** A diagram illustrating the configuration of pulse output timing, showing `onDur`, `offDur`, and `numPls`.
- **Definitions and Descriptions:** Detailed explanations of the attributes and their usage in configuring pulse outputs and originators.

**Extracted Text:**

```
61850-7-3 © CEI:2010 - 111 - 
Le posVal doit contenir la position de phase, le transInd doit indiquer que l'équipement est dans un état transitoire.
6.7 Pulse configuration
Le type "pulse configuration" (c'est-à-dire configuration d'impulsion) est utilisé pour configurer l'impulsion de sortie générée avec une commande et doit être tel que défini au Tableau 8.
Tableau 8 - Pulse configuration

Définition du type PulseConfigConfig

| Nom d'attribut | Type d'attribut | Valeur/Plage de valeurs | M/O/C |
|----------------|------------------|------------------------|--------|
| cmdQual         | ENUMERATED       | pulse | persistent       | M      |
| onDur           | INT32U           |                      | M      |
| offDur          | INT32U           |                      | M      |
| numPls          | INT32U           |                      | M      |

cmdQual: cet identificateur doit définir si la sortie de contrôle est une sortie pulsée ou s'il s'agit d'une sortie persistante. S'il est mis à "pulse", la durée de l'impulsion doit être définie avec les identificateurs onDur, offDur et numPls. S'il est mis à "persistent", la sortie reste dans l'état indiqué dans le service "operate".
onDur, offDur, numPls: à la réception d'un service Operate, une sortie pulsée peut être générée à l'entrée on ou off d'un dispositif de commutation. La forme de cette sortie est définie par onDur, offDur et numPls conformément à la Figure 4. NumPls doit spécifier le nombre d'impulsions qui sont générées. onDur doit spécifier la durée de marche de l'impulsion, offDur spécifie la durée entre deux impulsions. onDur et offDur doivent être spécifiés en ms; une valeur de 0 ms doit spécifier que la durée est définie localement.

Figure 4 – Configuration d'impulsion de sortie de commande

6.8 Originator
Le type "originator" (c'est-à-dire émetteur) doit être tel que défini au Tableau 9.
Tableau 9 – Originator

Définition 9 – Type Originator

| Nom d'attribut | Type d'attribut | Valeur/Plage de valeurs | M/O/C |
|----------------|------------------|------------------------|--------|
| orCat          | ENUMERATED       | not-supported | bay-control | station-control | remote-control | automatic-bay | automatic-station | automatic-remote | maintenance | process |
| orId           | OCTET STRING64   |                      | M      |

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved. This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

This technical document provides a clear and structured guide for configuring pulse outputs and originators in a control system, with detailed explanations and tables for each configuration type.

---

## Page 114

![Image 1 from page 114](images/iec61850-7-3{ed2.0}b_page114_img84_da77f9eb.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically from a standard or specification document. It includes definitions and explanations for various terms and concepts related to control systems, particularly focusing on categories of emitters (orCat), units, and vectors. The document is structured with tables and definitions, and it references other tables and annexes for further details.

**Key Elements:**
- **orCat Definitions:** Describes the categories of emitters and their corresponding operations.
- **Unit Definitions:** Defines the unit attribute and its enumeration.
- **Vector Definitions:** Defines the vector attribute and its enumeration.
- **Table References:** References Tableau 10, Tableau 11, and Tableau 12 for detailed information.
- **Annex References:** References Annex A for further details on SIUnit and multiplier values.

**Extracted Text:**

```plaintext
— 112 — 61850-7-3 © CEI:2010
orCat: La catégorie d'émetteur doit spécifier la catégorie de l'émetteur. Une explication des valeurs pour orCat est donnée au Tableau 10.
Tableau 10 – Valeurs pour orCat
Valeur Explication
not-supported Cette valeur ne doit pas être utilisée
bay-control Opération de contrôle émise par un opérateur utilisant un client situé au niveau baie
station-control Opération de contrôle émise par un opérateur utilisant un client situé au niveau poste
remote-control Opération de contrôle émise par un opérateur distant situé à l'extérieur du poste (un centre de conduite de réseau, par exemple)
automatic-bay Opération de contrôle émise par une fonction automatique au niveau baie
automatic-station Opération de contrôle émise par une fonction automatique au niveau poste
Opération de contrôle émise par une fonction automatique à l'extérieur du poste
process Changement de statut survenu sans action de commande (par exemple déclenchement externe d'un disjoncteur ou défaillance à l'intérieur du disjoncteur)
orldent: l'identification d'émetteur doit montrer l'identification de l'émetteur. La valeur NULL doit être réservée pour indiquer que l'émetteur d'une action particulière est inconnu.
6.9 Définition de unit
Le type "unit" (c'est-à-dire unité) doit être tel que défini au Tableau 11.
Tableau 11 – Unit
Définition de Unit
Nom d'attribut Type d'attribut Valeur/Plage de valeurs M/O/C
SIUnit ENUMERATED Conformément aux Tableaux A.1 à A.4 dans l'Annexe A
multiplier ENUMERATED Conformément au Tableau A.5 dans l'Annexe A
SIUnit: doit définir l'unité SI conformément à l'Annexe A.
multiplier: doit définir la valeur du multiplicateur conformément à l'Annexe A. La valeur par défaut est 0 (c'est-à-dire multiplier = 1).
6.10 Définition de vector
Le type "Vector" (c'est-à-dire vecteur) doit être tel que défini au Tableau 12.
Tableau 12 – Vector
Définition du type Vector
Nom d'attribut Type d'attribut Valeur/Plage de valeurs M/O/C
mag AnalogueValueValue
ang AnalogueValueValue -180 ≤ n ≤ +180 AC_CLC_O
mag: l'amplitude de la valeur complexe.
```

This document appears to be part of a standard or specification related to control systems, likely for industrial automation or similar applications, and it provides definitions and explanations for various technical terms and concepts used in such systems.

---

## Page 115

![Image 1 from page 115](images/iec61850-7-3{ed2.0}b_page115_img85_6de2e5e1.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically from section 6.11 to 6.14, which discusses the definitions of various types and their attributes in a system. The document includes definitions for Point, CtlModels, SboClasses, and Cell, along with their respective attributes and their definitions in tables.

**Key Elements:**
- Definitions of Point, CtlModels, SboClasses, and Cell.
- Attributes and their types for each defined type.
- Tables (Tableau 13 and Tableau 14) listing the attributes and their definitions.
- Descriptions of the types and their usage in a system.

**Extracted Text:**

```
61850-7-3 © CEI:2010 — 113 —
ang: l'angle de la valeur complexe. Le SIUnit doit être degrés et le multiplicateur d'unité est 1. La référence angulaire est définie dans le contexte où le type Vector est utilisé.

6.11 Définition de Point
Le type Point doit être tel que défini au Tableau 13 et il est utilisé pour représenter des points dans un système de coordonnées à deux dimensions ou à trois dimensions.

Tableau 13 – Point

| Nom d'attribut | Type d'attribut | Valeur/Plage de valeurs | M/O/C |
|----------------|-----------------|------------------------|--------|
| xVal           | FLOAT32         |                        | M      |
| yVal           | FLOAT32         |                        | M      |
| zVal           | FLOAT32         |                        | O      |

xVal: la valeur x d'un point
yVal: la valeur y d'un point
zVal: la valeur z d'un point

6.12 Définition de CtlModels
Le type CtlModels est défini comme suit:
ENUMERATED (status-only | direct-with-normal-security | sbo-with-normal-security | direct-with-enhanced-security | sbo-with-enhanced-security)
Des détails sont fournis dans l'article 8.

6.13 Définition de SboClasses
Le type SboClasses est défini comme suit:
ENUMERATED (operate-once | operate-many)
Des détails sont fournis dans l'article 8.

6.14 Cell
Le type Cell (c'est-à-dire cellule) est utilisé pour définir une surface rectangulaire dans un environnement à deux dimensions et il doit être défini comme au Tableau 14. Le type Cell peut tout aussi bien servir à décrire une plage dans un environnement monodimensionnel. Pour les détails, voir Figure 5.

Tableau 14 – Cell

| Nom d'attribut | Type d'attribut | Valeur/Plage de valeurs | M/O/C |
|----------------|-----------------|------------------------|--------|
| xStart         | FLOAT32         |                        | M      |
| xEnd           | FLOAT32         |                        | O      |
| yStart         | FLOAT32         |                        | O      |
| yEnd           | FLOAT32         |                        | O      |

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 116

![Image 1 from page 116](images/iec61850-7-3{ed2.0}b_page116_img86_4cd141da.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page that includes a diagram and a table. The diagram illustrates the concept of a cell in a grid, with labels for the start and end coordinates of the cell in both the x and y dimensions. The table provides a detailed definition of the CalendarTime type, which is used to define a time setting in reference to the calendar.

**Key Elements:**

- **Diagram:**
  - A grid is shown with labeled coordinates: xStart, xEnd, yStart, and yEnd.
  - The diagram visually represents the cell's boundaries in a two-dimensional space.

- **Table:**
  - The table defines the CalendarTime type, including its attributes and their corresponding values.
  - Attributes include: occ (Occurrence), occType (Type of occurrence), occPer (Period), weekDay (Weekday), month, day, hr (Hour), and mn (Minute).
  - Each attribute is described with its type (ENUMERATED or INT8U) and the range of values it can take.

- **Extracted Text:**
  - The text provides definitions for xStart, xEnd, yStart, and yEnd, explaining their roles in defining a cell's boundaries.
  - It also describes the CalendarTime type, its attributes, and their values, including examples of how to use it to specify times such as the last day of the month or the second Saturday of March at 03:00.

**Extracted Text:**

```plaintext
— 114 — 61850-7-3 © CEI:2010
xStart: la valeur x du coin inférieur gauche du carré.
xEnd: la valeur x du coin supérieur droit du carré. Cette composante ne doit pas être présente pour indiquer l'infini dans la direction de l'axe des x.
yStart: la valeur y du coin inférieur gauche du carré. Cette composante ne doit pas être présente s'il est nécessaire de décrire seulement une plage à une dimension.
yEnd: la valeur y du coin supérieur droit du carré. Cette composante ne doit pas être présente s'il est nécessaire de décrire seulement une plage à une dimension ou pour indiquer l'infini dans la direction de l'axe des y.
IEC 2551/10
Figure 5 — Définition de Cell
6.15 Définition de CalendarTime
Le type CalendarTime est utilisé pour définir un réglage de temps en référence au calendrier et doit être tel que défini au Tableau 15. Cette classe d'attributs construits permet la spécification des temps tels que le dernier jour du mois ou le deuxième samedi de mars à 03.00h.
Tableau 15 — CalendarTime
Définition du type CalendarTime
Nom d'attribut Type d'attribut Valeur/Plage de valeurs M/O/C
occ INT16U
occType ENUMERATED Time, WeekDay, WeekOfYear, DayOfMonth, DayOfYear
occPer ENUMERATED Hour, Day, Week, Month, Year
weekDay ENUMERATED reserved, Monday, Tuesday, ... Sunday
month ENUMERATED reserved, January, February, ... December
day INT8U 1..31
hr INT8U 0..23
mn INT8U 0..59
occ: Occurrence d'un élément du calendrier. La valeur 0 est utilisée pour indiquer la dernière. Pour l'identification des numéros de semaine, le numéro de semaine 01 doit toujours être la première semaine de janvier (conformément à la définition de l'ONU / CEFACT).
```

This technical document is useful for understanding the definition and usage of a cell in a grid and the CalendarTime type, which is crucial for specifying time settings in reference to the calendar.

---

## Page 117

![Image 1 from page 117](images/iec61850-7-3{ed2.0}b_page117_img87_75e4e06e.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically from the CEI 61850 standard series, which deals with the IEC 61850-7-3 standard. The page focuses on the semantic interpretation of calendar time settings and the general specifications of common data classes. It includes definitions, explanations, and tables that detail the attributes and their interpretations, as well as the structure of the common data classes and their relationships.

**Key Elements:**
- **Semantic Interpretation of Calendar Time Settings:** The page explains the meaning of attributes such as `occType`, `occPer`, `weekDay`, `month`, `day`, `hr`, and `mn` in the context of calendar time settings.
- **Tableau 16:** A table that provides the semantic interpretation of the time settings for calendar events.
- **Common Data Classes Specifications:** The document outlines the definitions and relationships between attributes within common data classes.
- **Generalities Section (7.1):** This section provides an overview of the common data classes and their attributes, including their functional constraints and possible triggering options.
- **Name Spaces (7.2):** This section explains how name spaces are defined to extend the present definitions of the CEI 61850-7-3 and 7-4 standards, based on a hierarchical structure from the root node LLN0.

**Extracted Text:**

```plaintext
61850-7-3 © CEI:2010 — 115 —
occType: le type d'élément de calendrier qui est utilisé pour cette occurrence.
occPer: La durée de répétition d'un réglage de temps basé sur le calendrier.
weekDay: le jour de la semaine.
month: le mois.
day: le jour.
hr: l'heure.
mn: la minute.
L'interprétation sémantique des attributs est donnée au Tableau 16.
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Tableau 16 – Interprétation sémantique des réglages du temps de calendrier
Table

---

## Page 125

![Image 1 from page 125](images/iec61850-7-3{ed2.0}b_page125_img88_96f468df.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically from the CEI 61850-7-3 standard. It details the attributes and services related to the Common Data Classes (CDCs) and their inheritance and specialization as defined in the CEI 61850-7-2 standard. The table provides a structured format for defining the attributes and their corresponding types, triggers, and value ranges, as well as the services associated with these attributes.

**Key Elements:**
- **Class VSS (Virtual Storage Structure):** This section defines the attributes and their properties.
  - **Nom d'attribut de donnée (Name of Data Attribute):** The name of the attribute.
  - **Type:** The type of the attribute, such as UNICODE, STRING255, etc.
  - **FC (Function Code):** The function code associated with the attribute.
  - **TrgOp (Trigger Operation):** The trigger operation for the attribute.
  - **Valeur/Plage de valeurs (Value/Value Range):** The value or range of values for the attribute.
  - **M/O/C (Mandatory/Optional/Conditional):** Indicates whether the attribute is mandatory, optional, or conditional.
- **Services:** This section lists the services associated with the attributes, including their descriptions and constraints.
  - **Service:** The specific service provided.
  - **Le service s'applique à (The service applies to):** The attributes to which the service applies.
  - **Remarque (Remark):** Additional remarks or constraints.

**Extracted Text:**

```
61850-7-3 © CEI:2010 — 123 —
Classe VSS
Nom d'attribut de donnée Type FC TrgOp Valeur/Plage de valeurs M/O/C
dU UNICODE DC
cdcNs VISIBLE STRING255 EX
cdcName VISIBLE STRING255 EX
dataNs VISIBLE STRING255 EX
Services
Comme défini dans le Tableau 18.

7.4 Spécifications de classe de données communes pour les informations relatives au mesurande
7.4.1 Application de services
Le Tableau 29 définit le modèle d'informations de mesurande de base. En particulier, il définit les relations d'héritage et la spécialisation de services définis dans la CEI 61850-7-2.
NOTE Des valeurs mesurées telles qu'utilisées dans les articles suivants peuvent aussi être appliquées à des valeurs calculées.

Tableau 29 — Modèle d'informations de mesurande de base
Modèle d'informations de mesurande de base
Nom Type TrgOp Valeur/Plage de valeurs M/O/C
d'attribut de donnée Hérité de GenDataObject Class ou de GenSubDataObject Class (voir la CEI 61850-7-2)
DataAttribute attributs mesurés substitution configuration, description et extension
Services (voir la CEI 61850-7-2)
Les services suivants sont hérités de la CEI 61850-7-2. Ils sont spécialisés en limitant le service aux attributs ayant une contrainte fonctionnelle comme spécifié ci-dessous.
Modèle de services Service Le service Remarque selon la s'applique à CEI 61850-7-2 Attr avec FC
GenCommonDataClass SetDataValues DC, CF, SV, BL
model (modèle de GetDataValues ALL GenCommonDataClass) GetDataDefinition ALL GetDataDirectory ALL
Data set model (Modèle GetDataSetValues ALL de jeux de données) SetDataSetValues DC, CF, SV, BL
Reporting model Report ALL comme spécifié dans le jeu de données qui
GSE model SendGOOSEMessage MX est utilisé pour définir le contenu du message.
Sampled values model SendMSVMessage MX
SendUSVMessage MX
```

This table and its associated text provide a comprehensive overview of the data classes and services defined in the CEI 61850-7-3 standard, which is crucial for understanding the structure and functionality of data management in smart grid communication protocols.

---

## Page 126

![Image 1 from page 126](images/iec61850-7-3{ed2.0}b_page126_img89_df71bb8d.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the attributes and properties of a "Measured Value" (MV) class in a data model. The table is structured to define the common data class "measured value" as specified in Tableau 30. It includes columns for the name of the MV attribute, type, FC (Function Code), TrgOp (Trigger Operation), Value/Value Range, and M/O/C (Modification/Output/Calculation) status. The table also provides additional attributes such as units, database, zero point, scaling configuration, range configuration, sampling rate, and data names, along with their respective types and statuses.

**Key Elements:**
- **Class MV:** Defines the measured value class.
- **Attributes:** Includes instMag, mag, range, q, t, subEna, subMag, subQ, subID, blkEna, units, db, zeroDb, sVC, rangeC, smpRate, d, dRate, dU, ddcNs, ddcName, dataNs.
- **Types:** AnalogueValue, ENUMERATED, BOOLEAN, Unit, INT32U, ScaledValueConfig, RangeConfig, INT32U, VISIBLE STRING255.
- **FC (Function Code):** MX, SV, CF, DC, EX.
- **TrgOp (Trigger Operation):** dchg, dupd, dupg, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, dchq, dchd, dchp, d

---

## Page 132

![Image 1 from page 132](images/iec61850-7-3{ed2.0}b_page132_img90_f31969ea.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the harmonic value for DEL (HDEL) as defined in Table 38. It outlines the attributes and their corresponding types, ranges, and configurations for a class of data objects used in a triphasic system, specifically for harmonic and interharmonic content of phase-phase process values.

**Key Elements:**
- **Class HDEL:** Defines the harmonic value for DEL.
- **DataName:** Identifies the name of the data object, which inherits from either GenDataObject Class or GenSubDataObject Class.
- **SubDataObject:** Specifies the sub-data objects related to the harmonic values.
- **DataAttribute:** Lists the attributes of the data object, including their types, configurations, and ranges.
- **Services:** Indicates the services associated with the data object.

**Extracted Text:**

```
— 130 – 61850-7-3 © CEI:2010

7.4.10 Harmonic value for DEL (HDEL)

Le Tableau 38 définit la classe de données communes “harmonic value for delta” (c'est-à-dire valeur harmonique pour montage triangle). Cette classe est un ensemble de mesures (ou évaluations) simultanées de valeurs qui représentent le contenu harmonique ou interharmonique d'une valeur de processus dans un système triphasé avec des valeurs phase-phase.

Tableau 38 – Harmonic values for delta

Classe HDEL
Nom d'attribut de donnée
Type
FC
TrgOp
Valeur/Plage de valeurs
M/O/C

DataName
Hérité de GenDataObject Class ou de GenSubDataObject Class (voir la CEI 61850-7-2)

SubDataObject
phsABHar
ARRAY 0..numHar OF CMV
M
phsBCHar
ARRAY 0..numHar OF CMV
O
phsCAHar
ARRAY 0..numHar OF CMV
O

DataAttribute
configuration, description et extension

numHar
INT16U
CF
dchg
>0
M
numCyc
INT16U
CF
dchg
>0
M
evalTm
INT16U
CF
dchg
Va | Vb | Vc | Aa | Ab | Ac | Vab | Vbc | Vca | Vother | Aother | Synchrophasor
M
smpRef
ENUMERATED
CF
dchg
fundamental | rms | absolute
O
smpRate
INT32U
CF
dchg
nominal frequency
M
Fréquence
FLOAT32
CF
dchg
nominal frequency
M
hvRef
ENUMERATED
CF
dchg
fundamental | rms | absolute
O
rmsCyc
INT16U
CF
dchg
fundamental | rms | absolute
AC_RMS_M
d
VISIBLE STRING255
DC
Text
O
dU
UNICODE STRING255
DC
Text
O
cdcNs
VISIBLE STRING255
EX
AC_DLNDA_M
cdcName
VISIBLE STRING255
EX
AC_DLNDA_M
dataNs
VISIBLE STRING255
EX
AC_DLN_M

Services
Comme défini dans le Tableau 29.
```

This table provides a comprehensive overview of the harmonic value for DEL, including the attributes and their configurations, which are essential for understanding the data structure and its application in a triphasic system.

---

## Page 133

![Image 1 from page 133](images/iec61850-7-3{ed2.0}b_page133_img91_2ec0569b.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically from the CEI 61850-7-3 standard. It outlines the specifications for common data classes for commands, focusing on the inheritance and specialization of services defined in the CEI 61850-7-2 standard. The table provides a structured format for defining the attributes and services related to controlled status information.

**Key Elements:**
- **Table Title:** "Tableau 39 – Modèle d'informations de statut contrôlable de base" (Table 39 – Controlled Status Information Model)
- **Columns:** The table includes columns for "Nom d'attribut de donnée" (Attribute Name), "Type" (Type), "FC" (Function Code), "TrgOp" (Trigger Operation), "Valeur/Plage de valeurs" (Value/Value Range), and "M/O/C" (Measure/Operation/Control).
- **Services Section:** Lists services such as "SetDataValues," "GetDataValues," "GetDataDefinition," "GetDataDirectory," "Report," "SendGOOSEMessage," "SendGSSEMessage," "SendMSVMessage," "SendUSVMessage," "Select," "SelectWithValue," "Cancel," "Operate," and "CommandTermination."
- **Remarque (Remark):** Provides additional notes and explanations for each service.

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 — 131 —
7.5 Spécifications de classe de données communes pour les commandes
7.5.1 Application de services
Le Tableau 39 définit le modèle d'informations de statut contrôlables de base. En particulier, il définit les relations d'héritage et la spécialisation de services définis dans la CEI 61850-7-2.
Tableau 39 — Modèle d'informations de statut contrôlable de base
Modèle d'informations de statut contrôlable de base
Nom Type TrgOp Valeur/Plage de valeurs M/O/C
d'attribut de donnée Hérité de GenDataObject Class ou de GenSubDataObject Class (voir la CEI 61850-7-2)
DataAttribute état/ attributs mesurés et miroir de contrôle
substitution et bloqué
configuration, description et extension
paramètres pour services de contrôle
Services (voir la CEI 61850-7-2)
Les services suivants sont hérités de la CEI 61850-7-2. Ils sont spécialisés en limitant le service aux attributs ayant une contrainte fonctionnelle comme spécifié ci-dessous.
Modèle de services Service Le service Remarque selon la s'applique à CEI 61850-7-2 Attr avec FC
GenCommonDataClass SetDataValues DC, CF, SV, BL model GetDataValues ALL GetDataDefinition ALL GetDataDirectory ALL Data set model (Modèle I GetDataSetValues ALL de jeux de données) SetDataSetValues DC, CF, SV, BL Reporting model Report ALL comme spécifié dans le jeu de données qui GSE model SendGOOSEMessage ST, MX est utilisé pour définir le contenu du SendGSSEMessage ST message Sampled values model I SendMSVMessage ST, MX SendUSVMessage ST, MX Modèle de contrôle Select SelectWithValue Cancel Operate CommandTermination TimeActivatedOperate Toutes les classes de données communes pour les informations de statut contrôlables comprennent aussi bien la commande que les informations de statut connexes.
NOTE Le paramètre de service du contrôle, qui appartient au modèle de contrôle défini dans la CEI 61850-7-2, est inclus ici, comme ce type est défini par le CDC.
```

This table is part of a larger document detailing the structured approach to defining controlled status information in the context of the IEC 61850 standard, which is widely used in the automation and control of electrical power systems.

---

## Page 143

![Image 1 from page 143](images/iec61850-7-3{ed2.0}b_page143_img92_653c6efa.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically from CEI 61850-7-3, dated 2010. It details the definitions and settings for two types of status settings: "Integer status setting (ING)" and "Enumerated status setting (ENG)." The table is structured to provide a comprehensive overview of the attributes and their configurations for these status settings, including data types, field control (FC), trigger operations (TrgOp), value ranges, and access modes (M/O/C).

**Key Elements:**

- **Tableau 50 – Integer status setting**
  - **DataName**: Name of the data attribute.
  - **Type**: Data type (e.g., INT32, STRING255).
  - **FC**: Field Control (e.g., SP, SG, SE).
  - **TrgOp**: Trigger Operation (e.g., dchg, dchg, dchg).
  - **Valeur/Plage de valeurs**: Value/Value range.
  - **M/O/C**: Access modes (e.g., AC_NSG_M, AC_SG_M, AC_DLNDA_M).

- **Tableau 51 – Enumerated status setting**
  - **DataName**: Name of the data attribute.
  - **Type**: Data type (e.g., ENUMERATED, STRING255).
  - **FC**: Field Control (e.g., SP, SG, SE).
  - **TrgOp**: Trigger Operation (e.g., dchg, dchg, dchg).
  - **Valeur/Plage de valeurs**: Value/Value range.
  - **M/O/C**: Access modes (e.g., AC_NSG_M, AC_SG_M, AC_DLNDA_M).

**Extracted Text:**

```plaintext
61850-7-3 © CEI:2010 - 141 - 7.6.3 Integer status setting (ING)
Le Tableau 50 définit la classe de données communes "integer status setting".
Tableau 50 – Integer status setting

Tableau 50 – Integer status setting
d'attribut de donnée — es I CEI 61850-7-2)
[setVal INT32 TSP dchg POT A NSG_M]
[setVal INT32 SG, SE]
[minVal INT32 CF dchg]
[maxVal INT32 CF dchg]
[stepSize INT32U CF dchg 1 ... (maxVal – minVal)]
[units Unit CF dchg]
[d VISIBLE STRING255 DC dchg Text]
[dU VISIBLE STRING255 DC dchg Text]
[dU VISIBLE STRING255 DC dchg Text]
[cdcNs VISIBLE STRING255 DC dchg Text]
[cdcName VISIBLE STRING255 EX]
[dataNs VISIBLE STRING255 EX]
[Services]
Comme défini dans le Tableau 48.

7.6.4 Enumerated status setting (ENG)
Le Tableau 51 définit la classe de données communes "enumerated status setting".
Tableau 51 – Enumerated status setting

Tableau 51 – Enumerated status setting
d'attribut de donnée
S— ae CEI 61850-7-2)
[setVal ENUMERATED SP dchg]
[setVal ENUMERATED SG, SE]
[d VISIBLE STRING255 DC dchg Text]
[dU VISIBLE STRING255 DC dchg Text]
[dU VISIBLE STRING255 DC dchg Text]
[cdcNs VISIBLE STRING255 DC dchg Text]
[cdcName VISIBLE STRING255 EX]
[dataNs VISIBLE STRING255 EX]
[Services]
Comme défini dans le Tableau 48.
```

This table provides a structured format for defining and configuring integer and enumerated status settings, which are crucial for the configuration of data objects in the IEC 61850 standard, commonly used in smart grid and automation systems.

---

## Page 147

![Image 1 from page 147](images/iec61850-7-3{ed2.0}b_page147_img93_ee9f9651.png)

**Image Type:** Table
**Description:** This image is a technical table from a standard or specification document, specifically related to the "Analogue setting (ASG)" as defined in Tableau 57. The table outlines the attributes and their configurations for an "analogue setting" class of data objects, which is part of a larger standard (CEI 61850-7-3) published by the International Electrotechnical Commission (IEC) in 2010.

**Key Elements:**
- **Table Title:** "Tableau 57 – Analogue setting"
- **Columns:** The table includes columns for "Nom d'attribut de donnée" (Name of Data Attribute), "Type" (Type), "FC" (Field Control), "TrgOp" (Trigger Operation), "Valeur/Plage de valeurs" (Value/Value Range), and "M/O/C" (Modify/Output/Control).
- **Data Attributes:** The table lists various attributes such as "DataName," "setMag," "units," "sVC," "minVal," "maxVal," "stepSize," "dU," "cdcNs," "cdcName," and "dataNs."
- **Configuration and Description:** The table provides configuration details for each attribute, including field control (FC), trigger operation (TrgOp), and value range (Valeur/Plage de valeurs). It also includes descriptions and extensions for each attribute.

**Extracted Text:**
61850-7-3 © CEI:2010 145 - 7.7.2 Analogue setting (ASG) Le Tableau 57 définit la classe de données communes “analogue setting”. Tableau 57 – Analogue setting d'attribut de donnée — es [I CEI 61850-7-2) [settiag[AnalogueVaue [SP [échg [I SANS I [setttag [AnalogueValue [86.5] I ——SSCSCSCSS YA SI [units unit Tr adchg [voirAnnexeaA TO [sve_____IScaledValueConfig I cr [dchg I AC SCA I [minVal[AnalogueVatue I cr Iécng I SCS SY [maxval__IAnalogueValue [I cr [dchg I [stepSize I AnalogueValue___I CF __[dchg O... (maxVal-minval) I [os _IvisieestRiNGzs [oc I [tx = SSSSSCSCid SCC a a a STRING255 [eden [VISIBLE sTRING255 [EX [IS ABEND [edeName [VISIBLE STRING255 I ex [I ———SSSY AC “DLN I [dates [VISIBLE STRING255 I_Ex I__I SSCS CAC NM

This table is part of a larger standard and is referenced in other parts of the document, as indicated by the references to "Tableau 56" and "CEI 61850-7-2." The document is copyrighted by the IEC and is subject to a license agreement.

---

## Page 150

![Image 1 from page 150](images/iec61850-7-3{ed2.0}b_page150_img94_d16fbae7.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page that includes a diagram and a table. The diagram illustrates a 2D form created by multiple CSG (Constructive Solid Geometry) curves, with specific annotations for Crv0 and Crv1. The table provides specifications for a common data class model, detailing attributes, types, and services related to data description and services.

**Key Elements:**
- **Diagram:** Depicts a 2D form created by multiple CSG curves, with annotations for Crv0 and Crv1.
- **Table:** Lists attributes, types, and services related to data description and services.
- **Text:** Describes the creation of a 2D form using multiple CSG curves, the use of Crv0 and Crv1, and the definition of a common data class model.

**Extracted Text:**

---

**— 148 — 61850-7-3 © CEI:2010**

Une famille de réglages de formes peut être créée par plusieurs instances d'un objet de donnée avec la CDC CSG. Dans ce cas, le type d'attribut de donnée commun Point utilisé pour crvPts ne doit pas prendre en charge l'élément facultatif z. De plus, l'attribut pointZ est utilisé pour représenter la valeur de la courbe sur l'axe z. La forme tridimensionnelle est créée en reliant les courbes les unes aux autres. Voir Figure 7.

Figure 7 – Forme à deux dimensions créée par plusieurs CSG

7.8 Spécifications de classe de données communes pour les informations de description

7.8.1 Application de services

Le Tableau 60 définit le modèle d'informations de description de base. En particulier, il définit les relations d'héritage et la spécialisation de services définis dans la CEI 61850-7-2.

Tableau 60 – Modèle d'informations de description de base

| Modèle d'informations de description de base |
| --- |
| Nom d'attribut de donnée |
| Type |
| FC |
| TrgOp |
| Valeur/Plage de valeurs |
| M/O/C |
| DataName |
| Hérité de GenDataObject Class ou de GenSubDataObject Class (voir la CEI 61850-7-2) |
| DataAttribute |
| configuration, description et extension |
| Services (voir la CEI 61850-7-2) |
| Les services suivants sont hérités de la CEI 61850-7-2. Ils sont spécialisés en limitant le service aux attributs ayant une contrainte fonctionnelle comme spécifié ci-dessous. |
| Modèle de services selon la CEI 61850-7-2 |
| Service |
| Le service s'applique à |
| Remarque |
| GenCommonDataClass model |
| SetDataValues |
| DC, CF |
| GetDataValues |
| ALL |
| GetDataDefinition |
| ALL |
| GetDataDirectory |
| ALL |
| Data set model (Modèle de jeux de données) |
| SetDataValues |
| ALL |
| SetDataSetValues |
| DC, CF |
| Modèle de production de rapports |
| Report |
| ALL |
| GOOSE, SV model |
| GOOSE, SV |
| ST |

---

This technical document page provides a detailed explanation of the creation of a 2D form using multiple CSG curves, the definition of a common data class model, and the specifications for services related to data description.

---

## Page 151

![Image 1 from page 151](images/iec61850-7-3{ed2.0}b_page151_img95_44e6924a.png)

**Image Type:** Table

**Description:** This image is a technical table from a standard or specification document, specifically from CEI 61850-7-3, which is related to IEC (International Electrotechnical Commission) standards. The table is titled "Tableau 61 – Spécification de la classe de données communes 'Device name plate'" and details the specifications for a "Device name plate" data class. This class is used to identify entities such as primary equipment or physical devices.

The table outlines the attributes of the "Device name plate" data class, including their types, field control (FC), trigger operation (TrgOp), value range, and whether they are mandatory (M), optional (O), or configurable (C). The table also includes a column for services, which is defined in Table 60.

**Key Elements:**
- **Class DPL:** Specifies the "Device name plate" data class.
- **Attributes:** Lists various attributes such as vendor, hwRev, swRev, serNum, model, location, name, owner, ePSName, primeOper, secondOper, latitude, longitude, altitude, mRID, d, dU, cdcNs, cdcName, dataNs, and services.
- **Data Types:** Includes types such as VISIBLE STRING255, FLOAT32, and UNICODE STRING255.
- **Field Control (FC):** Indicates whether the attribute is mandatory (M), optional (O), or configurable (C).
- **Trigger Operation (TrgOp):** Specifies the operation type for the attribute.
- **Value Range:** Defines the range of values for each attribute.
- **Services:** References Table 60 for service definitions.

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 — 149 — 7.8.2 Device name plate (DPL)

Le Tableau 61 définit la classe de données communes "device name plate" (c'est-à-dire plaque signalétique de dispositif). Les données de cette classe de données communes sont utilisées pour identifier des entités comme les équipements primaires ou les dispositifs physiques.

Tableau 61 – Spécification de la classe de données communes "Device name plate"

| Nom d'attribut de donnée | Type | FC | TrgOp | Valeur/Plage de valeurs | M/O/C |
|--------------------------|------|----|--------|-------------------------|-------|
| vendor                   | VISIBLE STRING255 | DC |        |                         | M     |
| hwRev                    | VISIBLE STRING255 | DC |        |                         | O     |
| swRev                    | VISIBLE STRING255 | DC |        |                         | O     |
| serNum                   | VISIBLE STRING255 | DC |        |                         | O     |
| model                    | VISIBLE STRING255 | DC |        |                         | O     |
| location                 | VISIBLE STRING255 | DC |        |                         | O     |
| name                     | VISIBLE STRING64  | DC |        |                         | O     |
| owner                    | VISIBLE STRING255 | DC |        |                         | O     |
| ePSName                  | VISIBLE STRING255 | DC |        |                         | O     |
| primeOper                | VISIBLE STRING255 | DC |        |                         | O     |
| secondOper               | VISIBLE STRING255 | DC |        |                         | O     |
| latitude                 | FLOAT32           | DC |        |                         | O     |
| longitude                | FLOAT32           | DC |        |                         | O     |
| altitude                 | FLOAT32           | DC |        |                         | O     |
| mRID                     | VISIBLE STRING255 | DC |        |                         | O     |
| d                        | VISIBLE STRING255 | DC |        |                         | O     |
| dU                       | UNICODE STRING255  | DC |        |                         | O     |
| cdcNs                    | VISIBLE STRING255  | EX |        |                         | AC_DLNDA_M |
| cdcName                  | VISIBLE STRING255  | EX |        |                         | AC_DLNDA_M |
| dataNs                   | VISIBLE STRING255  | EX |        |                         | AC_DLN_M |

Services
Comme défini dans le Tableau 60.
```

This table provides a structured overview of the "Device name plate" data class, detailing its attributes, their types, and their roles within the system

---

## Page 152

![Image 1 from page 152](images/iec61850-7-3{ed2.0}b_page152_img96_7c06f5c0.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the specifications of the "Logical node name plate" (LPL) class of data, as defined in Table 62. This table outlines the attributes and their corresponding types, field control (FC), trigger operation (TrgOp), value range, and whether the attribute is mandatory (M), optional (O), or configurable (C). The table is structured to provide a comprehensive overview of the data attributes used to describe logical nodes in a specific standard (CEI 61850-7-2).

**Key Elements:**
- **Class LPL:** Defines the "Logical node name plate" class.
- **Data attributes:** Includes vendor, swRev, d, dU, configRev, paramRev, vaiRev, idNs, InNs, cdcNs, cdcName, dataNs, and dataName.
- **Type:** Specifies the type of each attribute (e.g., STRING255, INT32, UNICODE STRING255).
- **FC (Field Control):** Indicates whether the attribute is mandatory (DC), configurable (EX), or optional (ST).
- **TrgOp (Trigger Operation):** Specifies the trigger operation for the attribute.
- **Value Range:** Provides the range of values for each attribute.
- **M/O/C (Mandatory/Optional/Configurable):** Indicates whether the attribute is mandatory, optional, or configurable.

**Extracted Text:**

```
— 15O - 6185O-7-3 © CEI:2O1O
7.8.3 Logical node name plate (LPL)

Le Tableau 62 définit la classe de données communes “logical node name plate” (c'est-a-dire plaque signalétique de noeud logique). Les données de cette classe de données communes sont utilisées pour décrire des noeuds logiques.

Tableau 62 – Spécification de la classe de données communes
"Logical node name plate"

Classe LPL
Nom
d'attribut de
donnée
Type
FC
TrgOp
Valeur/Plage de valeurs
M/O/C

DataName
Hérité de GenDataObject Class ou de GenSubDataObject Class (voir la
CEI 61850-7-2)
DataAttribute

configuration, description et extension

vendor
VISIBLE STRING255
DC
M
swRev
VISIBLE STRING255
DC
M
d
VISIBLE STRING255
DC
M
dU
UNICODE STRING255
DC
O
configRev
VISIBLE STRING255
DC
AC_LN0_M
paramRev
INT32
ST
dchg
O
vaiRev
INT32
ST
dchg
O
idNs
VISIBLE STRING255
EX
AC_LN0_EX
InNs
VISIBLE STRING255
EX
AC_DLD_M
cdcNs
VISIBLE STRING255
EX
AC_DLNDAM
cdcName
VISIBLE STRING255
EX
AC_DLNDAM
dataNs
VISIBLE STRING255
EX
AC_DLN_M
dataName
VISIBLE STRING255
EX
AC_DLN_M

Services
Comme défini dans le Tableau 60.
```

This table is part of a larger technical document, likely related to the IEC 61850 standard, which is used in the automation and control of electric power systems. The table provides a structured format for defining and describing the attributes of logical nodes, which are essential for interoperability and data exchange in these systems.

---

## Page 154

![Image 1 from page 154](images/iec61850-7-3{ed2.0}b_page154_img97_a7f001ed.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically from CEI:2010, page 152. It lists the semantic definitions for various attributes of data and data attributes, as defined in Tableau 64. The table provides detailed explanations for each attribute, including their meanings and usage contexts.

**Key Elements:**
- **Nom d'attribut de donnée (Name of Data Attribute):** Lists the names of the attributes.
- **Sémantique (Semantics):** Provides the semantic definition and usage of each attribute.

**Extracted Text:**

```
— 152 — 61850-7-3 © CEI:2010
8 Sémantique des attributs de données
Les attributs de données, les paramètres contrôlables et, dans certains cas, les données utilisées à l'Article 7 doivent avoir la sémantique telle que définie au Tableau 64.
Tableau 64 – Sémantique des attributs de données et des données
Nom d'attribut de donnée
Sémantique
actVal
État de compteur binaire représenté comme une valeur entière.
addInfo
Informations complémentaires qui peuvent apporter davantage de clarification concernant la dernière violation détectée.
addr
Adresse de la source distante qui est à l'origine de la dernière incrémentation du compteur.
NOTE 1 La sorte d'adresse stockée (adresse d'application, adresse IP, adresse de liaison,...) est tout ce que le serveur peut détecter. Cela peut dépendre du mappage spécifique.
altitude
Position géographique en coordonnées WGS84 (World Geodetic System 1984: Système géodésique mondial, révision de 1984) — altitude.
angRef
Référence angulaire. Indique la grandeur qui est utilisée comme référence pour l'angle de phase. Pour la grandeur indiquée, la fréquence fondamentale (indice = 1) est utilisée comme référence par convention.
angRef = "Synchrophasor" signifie que la référence angulaire est telle que définie dans le 4.2 de l'IEEE C37.118.
angSVC
Configuration de valeur à l'échelle pour les angles. Doit être utilisé pour configurer la représentation à l'échelle de l'angle des attributs instCVal et cVal et les limites dans rangeAngC de la CDC CMV.
blkEna
Si TRUE, le fanion de qualité operator-blocked est mis et la valeur de processus n'est plus mise à jour.
cdcName
Nom de la classe de données communes. Utilisé avec cdcNs; pour les détails, voir la CEI 61850-7-1.
cdcNs
Espace de nom de la classe de données communes. Pour les détails, voir la CEI 61850-7-1.
configRev
Définit de façon unique la configuration d'une instance de dispositif logique. ConfigRev dans LLNO (au niveau LD) doit changer au moins suite à n'importe quel changement sémantique du modèle de données de ce LD relatif à la fonctionnalité du client. La manière dont cela est détecté et effectué est laissée à l'utilisateur. De même, la sémantique de configRev concernant d'autres NL est laissée à l'utilisateur. Pour plus d'informations, voir également l'Annexe C.
crvPts
La matrice avec les points spécifiant une forme de courbe.
```

This table is crucial for understanding the semantic definitions of various attributes in a data model, which is essential for compliance with the CEI:2010 standard.

---

## Page 156

![Image 1 from page 156](images/iec61850-7-3{ed2.0}b_page156_img98_4b225e98.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines various attributes and their semantic descriptions related to electrical system data. The table is structured with two columns: "Nom d'attribut de donnée" (Attribute Name) and "Sémantique" (Semantic Description). It appears to be part of a larger technical document, possibly related to electrical standards or specifications, as indicated by the copyright notice at the bottom.

**Key Elements:**
- **Attribute Name:** Lists various attributes such as dirPhsC, dU, ePSName, evalTm, frEna, frequency, frPd, frRs, frTm, frVal, and har.
- **Semantic Description:** Provides detailed descriptions for each attribute, explaining their purpose and usage in the context of electrical system data.

**Extracted Text:**
```plaintext
— 154 — 61850-7-3 © CEI:2010
Nom d'attribut de donnée
Sémantique
dirPhsC Sens du défaut pour la phase C.
dU Description textuelle de la donnée utilisant des caractères unicode. Pour plus d'informations, voir d.
ePSName Nom du système électrique auquel le dispositif est raccordé.
evalTm Fenêtre de temps appliquée à des calculs inharmoniques. La valeur doit être représentée en ms. Pour plus d'informations, voir har.
frEna Valeur BOOLEAN, qui commande le processus de gel. S'il est TRUE, le gel doit se produire comme spécifié dans strTm, frPd et frRs. S'il est FALSE, aucun gel ne doit avoir lieu.
frequency Fréquence nominale du système électrique ou quelque autre fréquence fondamentale en Hz.
frPd Intervalle de temps en ms entre des opérations de gel. Si frPd est 0, un seul gel est exécuté à l'instant indiqué dans strTm.
frRs Indique que le compteur doit automatiquement être remis à zéro après chaque processus de gel.
frTm Heure du dernier gel du compteur.
frVal État de compteur binaire gelé représenté comme une valeur entière.
har Cette matrice doit contenir les valeurs harmoniques et sous-harmoniques ou interharmoniques. valeurs harmoniques et sous-harmoniques (evalTm est égal à la période de la fréquence d'alimentation électrique) Le premier élément de la matrice doit contenir les composantes de courant continu (dc), les éléments ultérieurs de la matrice doivent contenir les valeurs des harmoniques 1.. numHar. Si numCycl est supérieur à 1, la matrice doit contenir aussi bien les harmoniques que les sous-harmoniques et leurs multiples. Dans ce cas, les entrées de séquence avec le numéro n × 2numCycl-1 sont des harmoniques; Toutes les autres sont des sous-harmoniques ou des multiples de sous-harmoniques. valeurs interharmoniques (evalTm n'est pas égal à la période de la fréquence d'alimentation électrique) Le premier élément de la matrice doit contenir les composantes de courant continu (dc), les éléments ultérieurs de la matrice doivent contenir les valeurs des harmoniques 1.. numHar.
```

This table is likely part of a larger document related to electrical system data, possibly for a standard or specification document. The content is technical and detailed, focusing on the semantic descriptions of various attributes used in electrical system data management.

---

## Page 157

![Image 1 from page 157](images/iec61850-7-3{ed2.0}b_page157_img99_7a2ac93e.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical diagram that explains the structure and usage of an attribute matrix for histogram data. It includes a table with semantic descriptions of attributes and a graphical representation of a histogram with labeled axes and ranges.

**Key Elements:**

- **Attribute Table:** The table lists attributes such as `hstVal`, `hstRangeC`, `hvRef`, and `hwRev`, along with their semantic descriptions.
- **Semantic Descriptions:**
  - `hstVal`: Represents the values for histogram entries.
  - `hstRangeC`: Configures the range of values for the histogram.
  - `hvRef`: Specifies the type of reference (e.g., harmonic ratio, RMS value, absolute value).
  - `hwRev`: Indicates the hardware revision.
- **Graphical Representation:** A histogram is depicted with labeled axes (X-axis and Y-axis) and ranges (e.g., 0:0/4:10, 4:0/10,4, etc.). The histogram shows the frequency of values within each range.
- **Example Data:** The table provides an example of how the values for `hstVal` and `hstRangeC` would be structured.

**Extracted Text:**

```plaintext
61850-7-3 © CEI:2010 — 155 —
Nom d'attribut, Sémantique
Cette matrice doit contenir les valeurs pour les entrées d'histogramme. Un histogramme peut étre calculé sur la base d'une plage unidimensionnelle ou bidimensionnelle. Les détails d'une représentation en histogramme unidimensionnel sont montrés dans la figure ci-dessous.
Valeur (comptes ou autres)
hstVal(0) hstVal(1) hstVal(3) hstVal(2)
xUnits
hstRangeC(0) hstRangeC(1) hstRangeC(2) hstRangeC(3)
IEC 2554/10
Un histogramme évalue une série de valeurs et évalue les occurrences d'une valeur dans une certaine plage. L'évaluation peut typiquement étre un décompte, une mesure de durée ou le calcul d'une moyenne. La plage de valeurs est configurée avec l'attribut de configuration hstRangeC. L'attribut hstVal[1] doit étre le décompte des apparitions des valeurs évaluées hstVal dans la plage hstRangeC[1]. Pour un histogramme bidimensionnel, la plage peut étre telle que montrée a la figure ci-aprés. Chacun des rectangles représente une plage; II n'y a pas de régle relative a la fagon d’ordonner les plages.
X-axis
IEC 2555/10
Pour l'exemple en question, les valeurs seraient comme suit:
indice 0 1 2 3 4 5
hstVal 0 10 9 1 5 3
hstRangeC 0:0/4:10 4:0/10,4 10:0/12:4 4:4/12;8 4:8/8;10 8:8/12;10
hstRangeC Cette matrice doit contenir les valeurs pour la configuration des plages pour l'histogramme. Pour les détails, voir hstVal.
Spécifie le type de référence (à savoir, rapport harmonique sur fondamentale, sur valeur RMS ou sur valeur absolue) que contient l'attribut de donnée mag du type d'attribut de donnée Vector.
hwRev HW-revision.
```

This technical analysis provides a clear understanding of the image's content and structure, which can be useful for technical documentation and search.

---

## Page 160

![Image 1 from page 160](images/iec61850-7-3{ed2.0}b_page160_img100_491934b9.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical diagram that illustrates the flow of commands and data within an IED (Intelligent Electronic Device) system, specifically focusing on the handling of GOOSE (Generic Object Oriented Substation Event) messages and their associated attributes. The diagram includes a flowchart and a table that explains the semantic meaning of various attributes related to the GOOSE messages and their processing within the IED.

**Key Elements:**
- **Flowchart:** Depicts the sequence of events when a GOOSE message is received by the IED, including the evaluation of the command, acceptance of the command, and the activation of the output.
- **Table:** Lists the semantic meanings of various attributes such as `opRcvd`, `opOk`, `tOpOk`, `origin`, `originSrc`, `owner`, `paramRev`, and `persistent`.
- **Attributes and Their Semantics:**
  - `opRcvd`: Indicates that a command for a controllable data object has been received.
  - `opOk`: Indicates that the command has been evaluated and accepted.
  - `tOpOk`: A temporal marker indicating the moment the output would be activated.
  - `origin`: Contains information about the sender of the last modification to the process value of the controllable object.
  - `originSrc`: Contains information about the sender of a control action transmitted by a GOOSE message.
  - `owner`: The owner of the device.
  - `paramRev`: Identifies the "revision" parameter of a logical device instance or a logical node instance. It should change at least when a parameter change occurs (FC=SE or FC=SP).
  - `persistent`: Configures the output control. If set to FALSE, the service operation modifies a single upper or lower position. If set to TRUE, the service operation triggers the persistent activation of the output.

**Extracted Text:**

```plaintext
— 158 — 61850-7-3 © CEI:2010

Nom d'attribut de donnée
Sémantique

opRcvd
Indication qu'une commande operate pour un objet de donnée contrélable a été reçue. Utilisé à des fins d'essais avec opOk et tOpOk en particulier lorsque le mode NL est TEST-BLOCKED.

opOk
Indication qu'une commande operate pour un objet de donnée contrélable a été évaluée et acceptée. Pour les détails, voir opRcvd.

origin
Origin contient l'information relative à l'émetteur de la dernière modification apportée à la valeur de processus de l'objet contrôlable.

originSrc
originSrc contient l'information relative à l'émetteur d'une action de contrôle transmise par un message GOOSE.

owner
Propriétaire du dispositif

paramRev
Identifie de façon univoque le paramètre "revision" d'une instance de dispositif logique ou d'une instance de nœud logique. ParamRev doit changer au moins suite à n'importe quel changement de paramètre (FC=SE ou FC=SP) dans ce dispositif logique ou nœud logique. La manière dont cela est détecté et effectué est laissée à l'utilisateur. Pour plus d'informations, voir également l'Annexe C.

persistent
Configure la sortie de contrôle. S'il est mis à FALSE, le service operate entraine la modification d'une et d'une seule position supérieure ou inférieure. S'il est mis à TRUE, le service operate enclenche l'activation persistante de la sortie. La sortie doit être désactivée par un service operate avec la valeur stop ou par une temporisation locale. Un client peut répéter l'envoi du service operate afin de réenclencher la sortie. Si persistent est mis à TRUE, ctlModel doit être mis à direct-with-normal-security.
```

This technical analysis provides a clear understanding of the flow and semantics of GOOSE messages and their attributes within an IED system, which is crucial for anyone working with or analyzing this type of technical documentation.

---

## Page 162

![Image 1 from page 162](images/iec61850-7-3{ed2.0}b_page162_img101_7ebe1496.png)

**Image Type:** Table

**Description:** The image is a detailed technical table that outlines various attributes and their corresponding semantic descriptions, along with their application to different data attributes. It also includes a section on the range of values for certain attributes and their validity and detail quality.

**Key Elements:**

- **Nom d'attribut de donnée (Name of Data Attribute):** Lists the names of the attributes such as `pointZ`, `primeOper`, `pulseConfig`, and `pulsQty`.
- **Sémantique (Semantics):** Provides the semantic description of each attribute, such as the position on the z-axis for `pointZ` and the configuration for `pulseConfig`.
- **Purpose:** Describes the purpose of the reference object, such as calculating the value using `actVal`, `frVal`, and `pulsQty`.
- **q (Quality):** Indicates the quality of the attribute or attributes representing the data value. It applies to various data attributes like `SPS`, `HST`, `CMV`, and others.
- **range:** Defines the range in which the value of current `instMag` or `instCVal.mag` is found. It can be used to emit an event if the current value changes and moves to another range.
- **validity:** Indicates the validity of the range, such as `high`, `normal`, and `low`.
- **detail-qual:** Specifies the detail quality, such as `questionable` and `good`.

**Extracted Text:**

```plaintext
– 160 – 61850-7-3 © CEI:2010

Nom d'attribut de donnée
pointZ Position de la courbe sur l'axe z.
primeOper Opérateur principal de dispositif.
pulseConfig Utilisé pour configurer l'impulsion de sortie générée avec la commande, si applicable.
pulsQty Amplitude de la valeur comptée par décompte. actVal/frVal et pulsQty sont utilisés pour calculer la valeur: valeur = actVal x pulsQty valeur = frVal x pulsQty
purpose Description du but de la référence d'objet.
q Qualité de l'attribut ou des attributs représentant la valeur de la donnée. Pour les différentes CDC, q s'applique aux attributs de données suivants:
SPS stVal dirPhsC, neut, dirNeut
HST hstCnt
CMV instCMag, cMag, range
SAV instMag
range Plage dans laquelle se trouve la valeur de courant instMag ou instCVal.mag. Il peut être utilisé pour émettre un événement si la valeur de courant change et passe à une autre plage. Range doit être utilisé dans le contexte avec des attributs de configuration comme hhLim, hLim, ILim, IILim, min et max comme montré ci-dessous.
range validity detail-qual
max high-high questionable outOfRange
hhLim high-high good
hLim high good
ILim normal good
IILim low good
min low-low good
NOTE 7 L'utilisation d'algorithmes pour filtrer des événements en fonction de la transition d'une plage vers une autre est une question locale.
NOTE 8 Cette valeur avec l'option de déclenchement "data-change" comme décrit dans la CEI 61850-7-2 peut être utilisée pour rapporter un événement au client.
Plage dans laquelle se trouve la valeur de courant instCVal.ang. Pour plus d'informations, voir
```

This table is part of a larger technical document, likely related to data attributes and their application in a specific context, such as industrial automation or control systems.

---

## Page 163

![Image 1 from page 163](images/iec61850-7-3{ed2.0}b_page163_img102_11da885b.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically from page 161 of a document with the reference 61850-7-3 © CEI:2010. The table lists various attributes and their semantic meanings, which are related to electrical and control systems. The table is structured with two columns: "Nom d'attribut de donnée" (Name of Data Attribute) and "Sémantique" (Semantics). Each row describes a specific attribute and its meaning in the context of electrical systems and control.

**Key Elements:**
- **Attributes and their Semantics:** The table includes attributes such as "rangeAngC," "resC," "resHar," "rmsCyc," "sboClass," "sboTimeout," "secondOper," "seqT," and "setCal." Each attribute is accompanied by a detailed semantic description.
- **Semantic Descriptions:** These descriptions provide technical definitions and explanations for each attribute, such as the residual current, harmonics, and control behavior.
- **Control Model References:** Some attributes, like "sboClass" and "sboTimeout," reference the CEI 61850-7-2 model for control behavior.

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 — 161 —
Nom d'attribut de donnée Sémantique
rangeAngC Paramètres de configuration tels qu'utilisés dans le contexte avec l'attribut rangeAng.
rangeAngC Paramètres de configuration tels qu'utilisés dans le contexte avec l'attribut range.
resC Courant résiduel. Le courant résiduel est la somme algébrique des valeurs instantanées de courants circulant à travers tous les conducteurs sous tension (à savoir la somme sur les courants de phase) d'un circuit en un point de l'installation électrique. Pour plus d'informations, voir phsA (WYE).
resHar Cette matrice doit contenir les valeurs harmoniques et sous-harmoniques ou interharmoniques liées au courant résiduel. Pour plus d'informations, voir Har.
rmsCyc Nombre de cycles de fréquence d'alimentation, qui sont utilisés pour le calcul de valeurs rms.
sboClass Spécifie la classe SBO selon le modèle de contrôle de la CEI 61850-7-2 qui correspond au comportement de la donnée. Les valeurs suivantes sont définies:
  Value
  operate-once À la suite d'une demande operate, l'objet de contrôle doit retourner dans l'état désélectionné.
  operate-many À la suite d'une demande operate, l'objet de contrôle doit rester dans l'état ready (prêt) tant que sboTimeout n'a pas expiré.
sboTimeout Spécifie la temporisation entre une commande select et une commande operate selon le modèle de contrôle de la CEI 61850-7-2. La valeur doit être en ms.
secondOper Opérateur secondaire de dispositif.
seqT Cet attribut doit spécifier le type de la séquence. Les valeurs suivantes sont utilisées:
  Value
  c1 c2 c3
  pos neg zero
  dir quad zero
setCal La valeur d'un réglage du temps, si le temps est réglé avec le temps du calendrier.
```

This table is part of a larger technical document that provides detailed definitions and explanations for various attributes used in electrical and control systems, particularly in the context of the CEI 61850 standard.

---

## Page 165

![Image 1 from page 165](images/iec61850-7-3{ed2.0}b_page165_img103_75016b44.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, likely related to electrical or electronic systems, as indicated by the reference to "IEC:2010" and the terminology used. The table lists various attributes of a system or device, along with their semantic meanings or descriptions. The table is structured with two columns: "Nom d'attribut de donnée" (Attribute Name) and "Sémantique" (Semantic Meaning). Each row describes a specific attribute and its corresponding semantic meaning, which includes technical definitions and conditions under which the attribute is applicable.

**Key Elements:**
- **Attribute Names:** These include terms like "setTstRef," "setVal," "sev," "smpRate," "stepSize," "strTm," "stSeld," and "stVal."
- **Semantic Meanings:** These provide detailed descriptions of the attribute's function and conditions, such as the reference to a test setup, status settings, severity levels, sampling rates, and timing parameters.
- **Technical Definitions:** The semantic meanings include definitions of terms like "critical," "major," "minor," and "warning" in terms of severity levels, and descriptions of sampling rates and step sizes in the context of analog values.

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 — 163 —
Nom d'attribut de donnée
Sémantique
setTstRef
La valeur du réglage de la référence d'objet utilisée lorsque tstEna est true (c'est-a-dire vrai) a des fins d'essais comme une référence de remplacement à la référence établie avec setSrcRef. Pour les détails, voir tstEna.
setVal
La valeur dun réglage de statut
sev
Sévérité de la dernière violation détectée. Les valeurs sont:
unknown
La sévérité ne peut être déterminée.
critical
La sévérité est critique en termes de sécurité de fonctionnement ou la donnée considérée critique et l'accès privilégié a été tenté.
major
La sévérité est majeure en termes de sécurité de fonctionnement ou la donnée considérée d'importance majeure et l'accès privilégié a été tenté.
minor
La sévérité est mineure en ce sens que le contrôle d'accès a été refusé à la donnée considérée privilégiée.
warning
Est moins sévère que "minor".
smpRate (HMV, HWYE, HDEL)
Détermine en fonction du théorème d'échantillonnage la plus élevée des harmoniques ou interharmoniques possibles qui soit détectable. Le minimum est 2 x frequency. La valeur doit représenter le nombre d'échantillons par période nominale. Dans le cas d'un système en courant continu (d.c.), la valeur doit représenter le nombre d'échantillons par seconde.
smpRate (MV, CMV, WYE, DEL)
Fréquence d'échantillonnage qui a été utilisée pour déterminer les valeurs analogiques. La valeur doit représenter le nombre d'échantillons par période nominale. Dans le cas d'un système en courant continu (d.c.), la valeur doit représenter le nombre d'échantillons par seconde.
stepSize
Détermine le pas entre valeurs individuelles que ctlVal (CDC INC, APC, BAC), setVal (CDC ING) ou setMag (CDC ASG) acceptera.
strTm
Heure de début du processus de gel. Si l'heure actuelle est plus tardive que l'heure de départ, le premier gel doit se produire à la prochaine expiration d'intervalle de gel (frPd), calculée à partir du réglage de l'heure de départ.
stSeld
La donnée contrôlable est dans le statut "selected".
stVal
Valeur de statut des données.
subCVal
Valeur utilisée pour remplacer l'attribut de donnée instCVal.
```

This table is part of a larger document, likely a standard or specification for an electrical or electronic system, providing detailed definitions and conditions for various attributes used in the system's operation and monitoring.

---

## Page 167

![Image 1 from page 167](images/iec61850-7-3{ed2.0}b_page167_img104_004f9ee0.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically from page 165 of document 61850-7-3 © CEI:2010. The table is structured to describe the semantic attributes and their corresponding values used for substituting data attributes in a system. The table includes columns for the name of the attribute, the semantic description, and the values used for substitution.

**Key Elements:**
- **Nom d'attribut (Attribute Name):** Lists the names of the attributes such as subQ, subVal, sVC, swRev, and t.
- **Sémantique (Semantics):** Describes the semantic meaning of each attribute, such as "Valeur utilisée pour substituer l'attribut de donnée q." (Value used to substitute the data attribute q).
- **Valeur utilisée pour substituer l'attribut de donnée q. (Value used to substitute the data attribute q.):** Lists the values used for substitution, such as stVal, valWTr, mxVal, etc.
- **Configuration de la valeur à l'échelle (Configuration of the value at scale):** Describes how to configure the representation of the value at scale, applicable to attributes like sVC.
- **Horodatage de la dernière modification (Timestamp of the last modification):** Describes the timestamping of the last modification applied to an attribute or attributes representing the value of the data or attribute q, applicable to attributes like t.

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 — 165 —
Nom d'attribut
Sémantique
subQ
Valeur utilisée pour substituer l'attribut représentant la valeur de l'instance de donnée. Pour les différentes CDC, subVal est utilisée pour substituer les attributs de données suivants:
subVal
Configuration de la valeur à l'échelle. Doit être utilisée pour configurer la représentation de la valeur à l'échelle. Pour les différentes CDC, sVC s'applique aux attributs de données et paramètres de service suivants:
sVC
Horodatage de la dernière modification apportée à l'un des attributs ou aux attributs représentant la valeur de la donnée ou à l'attribut q. Pour les différentes CDC, t s'applique aux attributs de données suivants:
t
Le marqueur temporel avec l'heure à laquelle une sortie d'un objet contrôlable est activé à la suite d'une commande de contrôle. Pour les détails, voir opRcvd.
```

This table is crucial for understanding the substitution and configuration of data attributes in a system, particularly in the context of the IEC 61850 standard for smart grid communication.

---

## Page 168

![Image 1 from page 168](images/iec61850-7-3{ed2.0}b_page168_img105_216d99e7.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the concept of data source switching in a control system. It shows the interaction between different logical nodes (LN) and the control functions they perform. The diagram includes a flowchart with nodes representing LN xxxx and LN yyyy, and arrows indicating the flow of signals and data. The diagram also explains how the tstEna attribute is used to switch between the original data source and the test data source.

**Key Elements:**
- **Nodes:** LN xxxx, LN yyyy, Function, Input1, Out
- **Signals:** Out, InRef1, SPCSO1
- **Attributes:** tstEna, setSrcRef, setSrcCB, setTstRef, setTstCB
- **Concepts:** Data source switching, normal operation, test mode, logical node GTST, signal flow, and the role of the tstEna attribute.

**Extracted Text:**
```plaintext
— 166 — 61850-7-3 © CEI:2010

Nom d'attribut de donnée
Sémantique
Commute entre la source originale de données (telle que définie avec setSrcRef et setSrcCB) pour une référence et la source de données d'essai (telle que définie avec setTstRef et setTstCB). Ce concept est expliqué dans la figure suivante.

tstEna=TRUE >>> lec 2560/10
En fonctionnement normal, le LN xxxx reçoit comme entrée le signal Out provenant de LN yyyy. L'attribut de donnée xxxx.InRef1.setSrcRef pointe sur yyyy.Out. Pour les essais fonctionnels de LN xxxx, un nœud logique GTST peut être utilisé pour générer des profils d'essai. Dans ce cas, le LN xxxx doit recevoir l'entrée du NL GTST; le signal SPCSO1 par exemple. Cela est indiqué par l'attribut de donnée xxx.InRef1.setTstRef. En mettant xxx.InRef1.tstEna à TRUE, le LN xxxx commencera à recevoir le signal InRef1 issu de GTST au lieu de yyyy.

Units de l'attribut ou des attributs représentant la valeur de la donnée. Pour les différentes CDC, units s'applique aux attributs de données et paramètres de service suivants:

CDC l'attribut de donnée units s'applique à
BCR actVal, frVal, pulsQt
MV instMag, mag, subMag, rangeC
CMV instCVal.mag, cVal.mag, subCVal.mag, rangeC
SAV instVal, min, max
HST hstVal
APC mxVal, subVal, minVal, maxVal, stepSize; paramètre de service ctiVal
BAC mxVal, subVal, minVal, maxVal, stepSize
BAC mxVal, subVal, minVal, maxVal, stepSize
ASG setMag, minVal, maxVal, stepSize
ASG setMag, minVal, maxVal, stepSize

Identifie de façon unique la révision de la préconfiguration des valeurs de configuration (FC= CF) dans une instance de dispositif logique ou de nœud logique par le biais du fichier SCL. ValRev doit changer au moins suite à n'importe quel changement de valeurs configurées dans le fichier SCL pour ce dispositif logique ou nœud logique. La manière dont cela est détecté et effectué est laissée à l'utilisateur. Pour plus d'informations, voir également l'Annexe C.

valRev
La modification de ValRev doit être effectuée avec la sémantique suivante:
- si la modification de valeur est effectuée dans l'IED seulement par le biais de services de communication ou via une IHM locale, la valeur doit être augmentée de 1.
- si la modification de valeur est effectuée dans le fichier de configuration, la valeur doit être augmentée de 10 000.

Valeur avec indication d'état transitoire
x Description de la valeur de l'axe x d'une courbe.
x Description de la valeur de l'axe x d'une courbe en UNICODE.
x Unité de l'axe x d'une courbe.
y Description de la valeur de l'axe y d'une courbe.
y Description de la valeur de l'axe y d'une courbe en UNICODE.
y Unité de l'axe y d'une courbe.
z Description de la valeur de l'

---

## Page 169

![Image 1 from page 169](images/iec61850-7-3{ed2.0}b_page169_img106_df064ddb.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically page 167 of a document with the reference number 61850-7-3 © CEI:2010. The table is titled "Nom d'attribut de donnée" (Name of Data Attribute) and "Sémantique" (Semantics). It provides information about a configuration parameter used to calculate the range around zero, where the analog value is forced to zero. The parameter represents the percentage difference between max and min in units of 0.001%. The table lists the application of the "zeroDb" attribute to different data attributes and their corresponding units.

**Key Elements:**
- **Nom d'attribut de donnée** (Name of Data Attribute): Lists the name of the attribute.
- **Sémantique** (Semantics): Describes the semantic meaning and application of the attribute.
- **zeroDb**: Describes the configuration parameter used to calculate the range around zero.
- **CDC**: Lists the data attributes to which the "zeroDb" attribute applies.
- **MV**: Represents the analog value.
- **CMV**: Represents the analog value in a different context.
- **zUnits**: Specifies the unit of the z-axis of a curve.

**Extracted Text:**
```
61850-7-3 © CEI:2010 — 167 —
Nom d'attribut de donnée
Sémantique
zeroDb
Paramètre de configuration utilisé pour calculer la plage autour de zéro, où la valeur analogique sera mise de force à zéro. La valeur doit représenter le pourcentage de différence entre max et min en unités de 0,001 %. Pour les différentes CDC, zeroDb s'applique aux attributs de données suivants:
CDC | l'attribut de donnée zeroDb s'applique à
MV | mag
CMV | cVal.mag
zUnits | Unité de l'axe z d'une courbe.
```

---

## Page 172

![Image 1 from page 172](images/iec61850-7-3{ed2.0}b_page172_img107_f34f1eee.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, likely related to electrical or electronic engineering, as indicated by the units and terms used. The table is divided into two main sections: one listing various physical quantities and their corresponding units, and the other listing prefixes for units of measurement.

**Key Elements:**

1. **First Section (Physical Quantities and Units):**
   - The table lists physical quantities such as Baud, Moment d'inertie, Niveau de pression acoustique, Consommation spécifique, Vitesse rampe, Débit, and Niveau de puissance.
   - Each quantity is paired with its unit of measurement, such as Baud with "caractères par seconde" (characters per second), Moment d'inertie with "kg mètre carré" (kilogram meter squared), and Niveau de puissance with "dBm" (decibel milliwatt).

2. **Second Section (Multiplicators):**
   - This section lists prefixes for units of measurement, ranging from Yocto (10^-24) to Yotta (10^24).
   - Each prefix is associated with its symbol (e.g., y for Yocto, z for Zepto, etc.), and the corresponding power of ten.

**Extracted Text:**

```
– 170 –
61850-7-3 © CEI:2010

Tableau A.5 – Multiplicateur

Valeur    Valeur du multiplicateur    Nom    Symbole
–24       10^-24                    Yocto   y
–21       10^-21                    Zepto   z
–18       10^-18                    Atto    a
–15       10^-15                    Femto   f
–12       10^-12                    Pico    p
–9        10^-9                     Nano    n
–6        10^-6                     Micro   μ
–3        10^-3                     Milli   m
–2        10^-2                     Centi   c
–1        10^-1                     Deci    d
0         1                         1
1         10^1                      Déca    da
2         10^2                      Hecto   h
3         10^3                      Kilo    k
6         10^6                      Méga    M
9         10^9                      Giga    G
12        10^12                     Téra    T
15        10^15                     Péta    P
18        10^18                     Exa    E
21        10^21                     Zetta   Z
24        10^24                     Yotta   Y

NOTE: Une valeur qui représente un pourcentage peut utiliser l'unité 1 (sans dimension) et un multiplicateur -2.
```

This table is part of a larger document, as indicated by the footer which includes a customer name, order number, and copyright information.

---

## Page 173

![Image 1 from page 173](images/iec61850-7-3{ed2.0}b_page173_img108_913e1191.png)

**Image Type:** Page from a Technical Document

**Description:** This image is a page from a technical document, specifically from the CEI (Comité Electrotechnique International) standard series. The page is labeled as "Annexe B" and is described as informative. It discusses functional constraints, which are defined in CEI 61850-7-2. The text indicates that the relevant constraints for the current part of CEI 61850 are included here for better readability of the standard. It also references Table B.1 for further details.

**Key Elements:**
- **Annexe B**: Indicates this is an annex section of the document.
- **Contraintes fonctionnelles**: Functional constraints.
- **CEI 61850-7-2**: The standard that defines these functional constraints.
- **CEI 61850**: The overarching standard being referenced.
- **Table B.1**: A table that provides additional details on the functional constraints.

**Extracted Text:**
```
61850-7-3 © CEI:2010 -171- Annexe B (informative)
Contraintes fonctionnelles
Les contraintes fonctionnelles sont définies dans la CEI 61850-7-2. Celles qui sont pertinentes pour la présente partie de la CEI 61850 sont reprises ici pour une meilleure lecture de la norme. Voir Tableau B.1.
```

This page is part of a larger technical document, likely related to electrical or electronic systems, and provides a reference to a specific standard (CEI 61850) and its annexes for understanding functional constraints.

---

## Page 175

![Image 1 from page 175](images/iec61850-7-3{ed2.0}b_page175_img109_c72cd658.png)

**Image Type:** Table
**Description:** The image is a table from a technical document, specifically from page 173 of a document with the reference number 61850-7-3 © CEI:2010. The table is titled "Valeurs de FunctionalConstraint" and provides information about the semantic, authorized services, and initial values/stockage/explanation for a specific functional constraint (FC) value, EX.

**Key Elements:**
- **FC (Functional Constraint):** EX
- **Sémantique (Semantics):** Extended definition (application name space)
- **Services autorisés (Authorized Services):** DataAttribute must represent an application name space. These spaces are used to define semantic definitions of NL, data object classes as specified in CEI 61850-7-3 and CEI 61850-7-4. DataAttributes with FC=EX should not be inscribable.
- **Valeurs initiales/stockage/explanation (Initial Values/Storage/Explanation):** The value of the DataAttribute must be such that it can be configured; the value must be non-volatile. Note that private extensions of the control blocks can use the (functional constraint) FC EX at the SCSM level.

**Extracted Text:**
```
61850-7-3 © CEI:2010 - 173 -
Valeurs de FunctionalConstraint
FC    Sémantique            Services autorisés    Valeurs initiales/stockage/explanation
EX    Extended definition (application name space) DataAttribute doit représenter un espace de nom d'application. Les espaces de nom d'application sont utilisés pour définir des définitions sémantiques des NL, de la classe d'objets de données et des DataAttributes tels que spécifiés dans la CEI 61850-7-3 et dans la CEI 61850-7-4. Les DataAttributes avec FC=EX ne doivent pas être inscriptibles.
Notez que des extensions privées des blocks de contrôle peuvent utiliser la (contrainte fonctionnelle) FC EX au niveau SCSM.
```

This table is part of a larger document that appears to be related to the IEC 61850 standard, which is used in the automation of electric power systems. The table specifies the semantics and constraints for a particular functional constraint (FC) value, EX, which is related to the representation of application name spaces in the context of data attributes.

---

## Page 176

![Image 1 from page 176](images/iec61850-7-3{ed2.0}b_page176_img110_d224abbb.png)

**Image Type:** Table

**Description:** The image is a structured table, likely from a technical document or standard, detailing the "Suivi des révisions de configuration" (Configuration Revision Tracking). It outlines various problems, their impacts, and where the configuration change is made. The table is divided into columns for "Problème" (Problem), "Impact / Commentaire" (Impact / Commentary), and "Où le changement a-t-il été fait?" (Where was the change made?). The rows are color-coded, with green, yellow, and pink sections, possibly indicating different levels of severity or categories.

**Key Elements:**
- **Problème (Problem):** Lists specific issues related to configuration changes.
- **Impact / Commentaire (Impact / Commentary):** Describes the effect of the problem and any additional comments.
- **Où le changement a-t-il été fait? (Where was the change made?):** Indicates whether the change was made within the IED (Intelligent Electronic Device) configuration file or through other means.
- **Color Coding:** Green, yellow, and pink sections, which might represent different severity levels or categories.

**Extracted Text:**
The OCR text is not clear and appears to be a mix of random characters and numbers, possibly due to image quality or OCR errors. The text is not legible and does not provide any useful information for the analysis.

**Technical Analysis:**
The table is a structured way to track configuration changes in a system, likely related to IEDs in a control or automation system. The problems listed are technical issues that could affect the system's operation, such as semantic changes, device configuration, and data model changes. The "Impact / Commentaire" column provides additional context on how these issues might affect the system, and the "Où le changement a-t-il été fait?" column helps identify the source of the change, whether it was made within the IED configuration file or through another method.

This table is useful for maintaining a record of configuration changes, ensuring that any issues are documented and can be traced back to their origin. It helps in maintaining the integrity and reliability of the system by providing a clear record of what changes were made and why.

---

## Page 177

![Image 1 from page 177](images/iec61850-7-3{ed2.0}b_page177_img111_d5def615.png)

**Image Type:** Table

**Description:** The image is a table with a structured layout designed to categorize and document changes in configuration settings for an IED (Intelligent Electronic Device). The table is divided into several sections, each with specific headers and rows to organize the data systematically.

**Key Elements:**
- **Columns:** The table has three main columns labeled as "Problème" (Problem), "Impact" (Impact), and "Où le changement a-t-il été fait?" (Where was the change made?).
- **Rows:** The rows are divided into different categories of changes, such as "Changement du groupe de réglages actif (SG)" (Active Settings Group Change), "Changement de la valeur d'un attribut CF" (Change of a CF attribute value), and "Attributs de configuration" (Configuration attributes).
- **Content:** Each cell in the table contains specific information related to the problem, its impact, and the location where the change was made. For example, the cell under "Changement du groupe de réglages actif (SG)" in the "Impact" column is marked with an "x," indicating a specific impact or action.

**Extracted Text:** (No text detected)

This table is likely used in a technical documentation or configuration management context to track and document changes made to an IED's configuration settings, ensuring that all modifications are recorded and can be traced back to their origin.

---

## Page 178

![Image 1 from page 178](images/iec61850-7-3{ed2.0}b_page178_img112_91f02627.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically an annex (Annexe D) from a standard or specification document. The document appears to be related to enumerations (Énumérations du SCL) within a control system language (SCL). The XML structure defines various types of enumerations, each with a unique identifier (id) and a list of possible values (EnumVal) with their respective order (ord).

**Key Elements:**
- **Annexe D (normative):** Indicates that this is a normative annex, which means it provides a standard or recommended practice.
- **Enumerations (Énumérations du SCL):** The document lists several types of enumerations, each with a unique identifier (id) and a set of possible values.
- **XML Structure:** The document is formatted in XML, with tags such as `<EnumType>` and `<EnumVal>` used to define the structure of the enumerations.

**Extracted Text:**
```xml
<EnumType id="ControlOutputKind">
    <EnumVal ord="0">pulse</EnumVal>
    <EnumVal ord="1">persistent</EnumVal>
</EnumType>
<EnumType id="CtlModelKind">
    <EnumVal ord="0">status-only</EnumVal>
    <EnumVal ord="1">direct-with-normal-security</EnumVal>
    <EnumVal ord="2">sbo-with-normal-security</EnumVal>
    <EnumVal ord="3">direct-with-enhanced-security</EnumVal>
    <EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
</EnumType>
<EnumType id="SboClassKind">
    <EnumVal ord="0">operate-once</EnumVal>
    <EnumVal ord="1">operate-many</EnumVal>
</EnumType>
<EnumType id="OriginatorCategoryKind">
    <EnumVal ord="0">not-supported</EnumVal>
    <EnumVal ord="1">bay-control</EnumVal>
    <EnumVal ord="2">station-control</EnumVal>
    <EnumVal ord="3">remote-control</EnumVal>
    <EnumVal ord="4">automatic-bay</EnumVal>
    <EnumVal ord="5">automatic-station</EnumVal>
    <EnumVal ord="6">automatic-remote</EnumVal>
    <EnumVal ord="7">maintenance</EnumVal>
    <EnumVal ord="8">process</EnumVal>
</EnumType>
<EnumType id="OccurrenceKind">
    <EnumVal ord="0">Time</EnumVal>
    <EnumVal ord="1">WeekDay</EnumVal>
    <EnumVal ord="2">WeekOfYear</EnumVal>
    <EnumVal ord="3">DayOfMonth</EnumVal>
    <EnumVal ord="4">DayOfYear</EnumVal>
</EnumType>
<EnumType id="MonthKind">
    <EnumVal ord="0">reserved</EnumVal>
    <EnumVal ord="1">January</EnumVal>
    <EnumVal ord="2">February</EnumVal>
    <EnumVal ord="3">March</EnumVal>
    <EnumVal ord="4">April</EnumVal>
    <EnumVal ord="5">May</EnumVal>
    <EnumVal ord="6">June</EnumVal>
    <EnumVal ord="7">July</EnumVal>
</EnumType>
```

This XML document provides a structured representation of various types of enumerations used in a control system language, with each enumeration having a unique identifier and a set of possible values.

---

## Page 179

![Image 1 from page 179](images/iec61850-7-3{ed2.0}b_page179_img113_356f81b9.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The XML structure defines various types of enumerations, each with a unique identifier (id) and a list of enumeration values (EnumVal) with corresponding order (ord) and descriptive names.

**Key Elements:**
- **EnumType**: Defines a type of enumeration.
- **id**: A unique identifier for the enumeration type.
- **EnumVal**: Defines a value within the enumeration, with an order and a descriptive name.
- **ord**: The order of the enumeration value within the type.
- **Name**: The descriptive name of the enumeration value.

**Extracted Text:**
```xml
<EnumVal ord="8">August</EnumVal>
<EnumVal ord="9">September</EnumVal>
<EnumVal ord="10">October</EnumVal>
<EnumVal ord="11">November</EnumVal>
<EnumVal ord="12">December</EnumVal>
</EnumType>
<EnumType id="PeriodKind">
<EnumVal ord="0">Hour</EnumVal>
<EnumVal ord="1">Day</EnumVal>
<EnumVal ord="2">Week</EnumVal>
<EnumVal ord="3">Month</EnumVal>
<EnumVal ord="4">Year</EnumVal>
</EnumType>
<EnumType id="DaWeekdayKind">
<EnumVal ord="0">reserved</EnumVal>
<EnumVal ord="1">Monday</EnumVal>
<EnumVal ord="2">Tuesday</EnumVal>
<EnumVal ord="3">Wednesday</EnumVal>
<EnumVal ord="4">Thursday</EnumVal>
<EnumVal ord="5">Friday</EnumVal>
<EnumVal ord="6">Saturday</EnumVal>
<EnumVal ord="7">Sunday</EnumVal>
</EnumType>
<EnumType id="FaultDirectionKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">forward</EnumVal>
<EnumVal ord="2">backward</EnumVal>
<EnumVal ord="3">both</EnumVal>
</EnumType>
<EnumType id="PhaseFaultDirectionKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">forward</EnumVal>
<EnumVal ord="2">backward</EnumVal>
</EnumType>
<EnumType id="SeverityKind">
<EnumVal ord="0">unknown</EnumVal>
<EnumVal ord="1">critical</EnumVal>
<EnumVal ord="2">major</EnumVal>
<EnumVal ord="3">minor</EnumVal>
<EnumVal ord="4">warning</EnumVal>
</EnumType>
<EnumType id="RangeKind">
<EnumVal ord="0">normal</EnumVal>
<EnumVal ord="1">high</EnumVal>
<EnumVal ord="2">low</EnumVal>
<EnumVal ord="3">high-high</EnumVal>
<EnumVal ord="4">low-low</EnumVal>
</EnumType>
```

This XML document appears to be part of a larger schema or configuration file, possibly related to a system or application that uses these enumerations for various types of data classification or categorization.

---

## Page 180

![Image 1 from page 180](images/iec61850-7-3{ed2.0}b_page180_img114_a6eace67.png)

**Image Type:** XML Document

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The document appears to be part of a larger technical specification, likely related to electrical engineering or standards, as indicated by the copyright notice from CEI (Conférence Electrotechnique Internationale) and the reference to IEC (International Electrotechnical Commission).

**Key Elements:**
- The document is structured as an XML file, with tags such as `<EnumType>`, `<EnumVal>`, and `<EnumVal ord="X">`.
- It defines various enumeration types, each with a unique identifier (e.g., `AngleReferenceKind`, `PhaseAngleReferenceKind`, `PhaseReferenceKind`, `SequenceKind`, `HvReferenceKind`, `CurveCharKind`).
- Each enumeration type contains a series of enumeration values (`EnumVal`), each with an associated order (`ord="X"`), and a descriptive name.
- The document is part of a larger document, as indicated by the page number (178) and the copyright notice at the bottom.

**Extracted Text:**
```xml
<EnumType id="AngleReferenceKind">
    <EnumVal ord="0">V</EnumVal>
    <EnumVal ord="1">A</EnumVal>
    <EnumVal ord="2">other</EnumVal>
    <EnumVal ord="3">Synchrophasor</EnumVal>
</EnumType>
<EnumType id="PhaseAngleReferenceKind">
    <EnumVal ord="0">Va</EnumVal>
    <EnumVal ord="1">Vb</EnumVal>
    <EnumVal ord="2">Vc</EnumVal>
    <EnumVal ord="3">Aa</EnumVal>
    <EnumVal ord="4">Ab</EnumVal>
    <EnumVal ord="5">Ac</EnumVal>
    <EnumVal ord="6">Vab</EnumVal>
    <EnumVal ord="7">Vbc</EnumVal>
    <EnumVal ord="8">Vca</EnumVal>
    <EnumVal ord="9">Vother</EnumVal>
    <EnumVal ord="10">Aother</EnumVal>
    <EnumVal ord="11">Synchrophasor</EnumVal>
</EnumType>
<EnumType id="PhaseReferenceKind">
    <EnumVal ord="0">A</EnumVal>
    <EnumVal ord="1">B</EnumVal>
    <EnumVal ord="2">C</EnumVal>
</EnumType>
<EnumType id="SequenceKind">
    <EnumVal ord="0">pos-neg-zero</EnumVal>
    <EnumVal ord="1">dir-quad-zero</EnumVal>
</EnumType>
<EnumType id="HvReferenceKind">
    <EnumVal ord="0">fundamental</EnumVal>
    <EnumVal ord="1">rms</EnumVal>
    <EnumVal ord="2">absolute</EnumVal>
</EnumType>
<EnumType id="CurveCharKind">
    <EnumVal ord="0">none</EnumVal>
    <EnumVal ord="1">ANSI Extremely Inverse</EnumVal>
    <EnumVal ord="2">ANSI Very Inverse</EnumVal>
    <EnumVal ord="3">ANSI Normal Inverse</EnumVal>
    <EnumVal ord="4">ANSI Moderate Inverse</EnumVal>
    <EnumVal ord="5">ANSI Definite Time</EnumVal>
    <EnumVal ord="6">Long-Time Extremely Inverse</EnumVal>
    <EnumVal ord="7">Long-Time Very Inverse</EnumVal>
    <EnumVal ord="8">Long-Time Inverse</EnumVal>
    <EnumVal ord="9">IEC Normal Inverse</EnumVal>
    <EnumVal ord="10">IEC Very Inverse</EnumVal>
</EnumType>
```

This XML document is likely used to define and standardize the enumeration types for various electrical and engineering applications, ensuring consistency and clarity in the use of these types across different systems or standards.

---

## Page 181

![Image 1 from page 181](images/iec61850-7-3{ed2.0}b_page181_img115_fec0c5b0.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, likely part of a larger technical specification or standard. The document appears to be structured with enumeration types, specifically focusing on "MultiplierKind" and "EnumVal" elements. The XML structure includes various enumerated values with unique identifiers and descriptions.

**Key Elements:**
- **MultiplierKind:** This is an enumeration type that includes values such as "y", "z", "a", "f", "p", "n", and "y".
- **EnumVal:** This is a sub-element of "MultiplierKind" that contains specific values with their corresponding identifiers (e.g., "IEC Inverse", "Polynom 1", "Multiline 1").
- **EnumType:** This defines the structure of the enumeration types, with "MultiplierKind" being one of the defined types.

**Extracted Text:**
```xml
61850-7-3 © CEI:2010 -179-
<EnumVal ord="11">IEC Inverse</EnumVal>
<EnumVal ord="12">IEC Extremely Inverse</EnumVal>
<EnumVal ord="13">IEC Short-Time Inverse</EnumVal>
<EnumVal ord="14">IEC Long-Time Inverse</EnumVal>
<EnumVal ord="15">IEC Definite Time</EnumVal>
<EnumVal ord="16">Reserved</EnumVal>
<EnumVal ord="17">Polynom 1</EnumVal>
<EnumVal ord="18">Polynom 2</EnumVal>
<EnumVal ord="19">Polynom 3</EnumVal>
<EnumVal ord="20">Polynom 4</EnumVal>
<EnumVal ord="21">Polynom 5</EnumVal>
<EnumVal ord="22">Polynom 6</EnumVal>
<EnumVal ord="23">Polynom 7</EnumVal>
<EnumVal ord="24">Polynom 8</EnumVal>
<EnumVal ord="25">Polynom 9</EnumVal>
<EnumVal ord="26">Polynom 10</EnumVal>
<EnumVal ord="27">Polynom 11</EnumVal>
<EnumVal ord="28">Polynom 12</EnumVal>
<EnumVal ord="29">Polynom 13</EnumVal>
<EnumVal ord="30">Polynom 14</EnumVal>
<EnumVal ord="31">Polynom 15</EnumVal>
<EnumVal ord="32">Polynom 16</EnumVal>
<EnumVal ord="33">Multiline 1</EnumVal>
<EnumVal ord="34">Multiline 2</EnumVal>
<EnumVal ord="35">Multiline 3</EnumVal>
<EnumVal ord="36">Multiline 4</EnumVal>
<EnumVal ord="37">Multiline 5</EnumVal>
<EnumVal ord="38">Multiline 6</EnumVal>
<EnumVal ord="39">Multiline 7</EnumVal>
<EnumVal ord="40">Multiline 8</EnumVal>
<EnumVal ord="41">Multiline 9</EnumVal>
<EnumVal ord="42">Multiline 10</EnumVal>
<EnumVal ord="43">Multiline 11</EnumVal>
<EnumVal ord="44">Multiline 12</EnumVal>
<EnumVal ord="45">Multiline 13</EnumVal>
<EnumVal ord="46">Multiline 14</EnumVal>
<EnumVal ord="47">Multiline 15</EnumVal>
<EnumVal ord="48">Multiline 16</EnumVal>
</EnumType>
<EnumType id="MultiplierKind">
<EnumVal ord="-24">y</EnumVal>
<EnumVal ord="-21">z</EnumVal>
<EnumVal ord="-18">a</EnumVal>
<EnumVal ord="-15">f</EnumVal>
<EnumVal ord="-12">p</EnumVal>
<EnumVal ord="-9">n</EnumVal>
<EnumVal ord="-6">y</EnumVal>
</EnumType>
```

**Technical Analysis:**
The XML document appears to be part of a standard or specification related to electrical or industrial control systems, as indicated by terms like "IEC" (International Electrotechnical Commission). The enumerations suggest a structured way of defining different types of multi

---

## Page 182

![Image 1 from page 182](images/iec61850-7-3{ed2.0}b_page182_img116_84e71366.png)

**Image Type:** XML Document

**Description:** The image is a screenshot of an XML document, specifically a section of an enumeration type definition. The document appears to be related to the International Electrotechnical Commission (IEC) standard, as indicated by the copyright notice at the bottom. The XML structure defines various enumeration values with their corresponding order and labels, which are likely used to represent different units of measurement or other standardized values.

**Key Elements:**
- **EnumType**: Defines a type of enumeration.
- **EnumVal**: Represents individual values within the enumeration.
- **ord**: Specifies the order of the enumeration value.
- **id**: Identifies the type of enumeration (e.g., "SIUnitKind").
- **Values**: Contains the actual enumeration values, such as "m" for meter, "kg" for kilogram, "s" for second, etc.

**Extracted Text:**
```xml
<EnumVal ord="-3">m</EnumVal>
<EnumVal ord="-2">c</EnumVal>
<EnumVal ord="-1">d</EnumVal>
<EnumVal ord="0"></EnumVal>
<EnumVal ord="1">da</EnumVal>
<EnumVal ord="2">h</EnumVal>
<EnumVal ord="3">k</EnumVal>
<EnumVal ord="6">M</EnumVal>
<EnumVal ord="9">G</EnumVal>
<EnumVal ord="12">T</EnumVal>
<EnumVal ord="15">P</EnumVal>
<EnumVal ord="18">E</EnumVal>
<EnumVal ord="21">Z</EnumVal>
<EnumVal ord="24">Y</EnumVal>
</EnumType>
<EnumType id="SIUnitKind">
<EnumVal ord="1"></EnumVal>
<EnumVal ord="2">m</EnumVal>
<EnumVal ord="3">kg</EnumVal>
<EnumVal ord="4">s</EnumVal>
<EnumVal ord="5">A</EnumVal>
<EnumVal ord="6">K</EnumVal>
<EnumVal ord="7">mol</EnumVal>
<EnumVal ord="8">cd</EnumVal>
<EnumVal ord="9">deg</EnumVal>
<EnumVal ord="10">rad</EnumVal>
<EnumVal ord="11">sr</EnumVal>
<EnumVal ord="21">Gy</EnumVal>
<EnumVal ord="22">Bq</EnumVal>
<EnumVal ord="23">°C</EnumVal>
<EnumVal ord="24">Sv</EnumVal>
<EnumVal ord="25">F</EnumVal>
<EnumVal ord="26">C</EnumVal>
<EnumVal ord="27">S</EnumVal>
<EnumVal ord="28">H</EnumVal>
<EnumVal ord="29">V</EnumVal>
<EnumVal ord="30">ohm</EnumVal>
<EnumVal ord="31">J</EnumVal>
<EnumVal ord="32">N</EnumVal>
<EnumVal ord="33">Hz</EnumVal>
<EnumVal ord="34">Ix</EnumVal>
<EnumVal ord="35">Lm</EnumVal>
<EnumVal ord="36">Wb</EnumVal>
<EnumVal ord="37">T</EnumVal>
<EnumVal ord="38">W</EnumVal>
<EnumVal ord="39">Pa</EnumVal>
<EnumVal ord="41">m²</EnumVal>
</EnumType>
```

**Technical Analysis:**
The XML document defines two types of enumerations:
1. **EnumType**: This enumeration includes values such as "m", "c", "d", etc., which might represent different units or categories.
2. **EnumType id="SIUnitKind"**: This enumeration defines standard International System of Units (SI) units, including "m" for meter, "kg" for kilogram, "s" for second, and so on. This type of enumeration is commonly used in technical and scientific contexts to ensure consistency and standardization.

The document is part of a larger standard, as indicated by the copyright notice and the reference to CEI:2010. The structure and content suggest that this XML is used to define and standardize units of measurement or other technical parameters in a structured format, which is typical in technical documentation and standards.

---

## Page 183

![Image 1 from page 183](images/iec61850-7-3{ed2.0}b_page183_img117_30cb1037.png)

**Image Type:** Table

**Description:** The image appears to be a table listing various EnumVal (enumerated values) with their corresponding numerical identifiers and physical units or properties. The table is structured to provide a comprehensive list of units and properties, likely used in a technical or scientific context, such as in electrical, mechanical, or physical measurements.

**Key Elements:**
- Each row represents an EnumVal with a unique identifier (ord value).
- The ord value is a numerical identifier for the EnumVal.
- The EnumVal itself is a string representing a specific unit or property.
- The table includes a wide range of units and properties, such as length (m, m/s), time (s, s), temperature (K), power (W), energy (J), and electrical properties (VA, Watts, VAr, etc.).

**Extracted Text:**
```plaintext
61850-7-3 © CEI:2010 = 181-
<EnumVal ord="42">m?</EnumVal>
<EnumVal ord="43">m/s</EnumVal>
<EnumVal ord="44">m/s*</EnumVal>
<EnumVal ord="45">m*/s</EnumVal>
<EnumVal ord="46">m/m?</EnumVal>
<EnumVal ord="47">M</EnumVal>
<EnumVal ord="48">kg/m*</EnumVal>
<EnumVal ord="49">m?/s</EnumVal>
<EnumVal ord="50">W/m K</EnumVal>
<EnumVal ord="51">J/K</EnumVal>
<EnumVal ord="52">ppm</EnumVal>
<EnumVal ord="53">1/s</EnumVal>
<EnumVal ord="54">rad/s</EnumVal>
<EnumVal ord="55">W/m?</EnumVal>
<EnumVal ord="56">J/m?</EnumVal>
<EnumVal ord="57">S/m</EnumVal>
<EnumVal ord="58">K/s</EnumVal>
<EnumVal ord="59">Pa/s</EnumVal>
<EnumVal ord="60">J/kg K</EnumVal>
<EnumVal ord="61">VA</EnumVal>
<EnumVal ord="62">Watts</EnumVal>
<EnumVal ord="63">VAr</EnumVal>
<EnumVal ord="64">phi</EnumVal>
<EnumVal ord="65">cos(phi)</EnumVal>
<EnumVal ord="66">Vs</EnumVal>
<EnumVal ord="67">V?</EnumVal>
<EnumVal ord="68">As</EnumVal>
<EnumVal ord="69">A?</EnumVal>
<EnumVal ord="70">A*t</EnumVal>
<EnumVal ord="71">VAh</EnumVal>
<EnumVal ord="72">Wh</EnumVal>
<EnumVal ord="73">VArh</EnumVal>
<EnumVal ord="74">V/Hz</EnumVal>
<EnumVal ord="75">Hz/s</EnumVal>
<EnumVal ord="76">char</EnumVal>
<EnumVal ord="77">char/s</EnumVal>
<EnumVal ord="78">kgm?</EnumVal>
<EnumVal ord="79">dB</EnumVal>
<EnumVal ord="80">J/Wh</EnumVal>
<EnumVal ord="81">W/s</EnumVal>
<EnumVal ord="82">I/s</EnumVal>
<EnumVal ord="83">dBm</EnumVal>
</EnumType>
```

This table is likely part of a larger document or database, possibly related to standards or specifications from the International Electrotechnical Commission (IEC) as indicated by the copyright notice. The content is technical and detailed, suggesting it is used for reference or as part of a larger system for managing units and properties.

---

## Page 184

![Image 1 from page 184](images/iec61850-7-3{ed2.0}b_page184_img118_73a88b9a.png)

**Image Type:** Bibliography Page

**Description:** The image is a page from a technical document, specifically a bibliography section. It lists various standards and specifications related to communication networks and systems for power utility automation, as well as industrial automation systems. The page includes references to CEI (Comité Electrotechnique International) standards and ISO (International Organization for Standardization) standards, indicating a focus on international technical standards.

**Key Elements:**
- CEI 61850-8-x: Communication networks and systems for power utility automation, Part 8: Specific Communication Service Mapping (SCSM) (available only in English).
- CEI 61580-9-x: Communication networks and systems for power utility automation, Part 9: Specific Communication Service Mapping (SCSM) (available only in English).
- ISO 9506: Systèmes d'automatisation industrielle — Spécification de messagerie industrielle (Industrial Automation Systems — Industrial Messaging Specification).

**Extracted Text:**
```
— 182 — 61850-7-3 © CEI:2010 Bibliographie
CEI 61850-8-x (toutes les parties), Communication networks and systems for power utility automation — Part 8: Specific Communication Service Mapping (SCSM) (disponible en anglais seulement)
CEI 61580-9-x (toutes les parties), Communication networks and systems for power utility automation — Part 9: Specific Communication Service Mapping (SCSM) (disponible en anglais seulement)
ISO 9506 (toutes les parties), Systèmes d'automatisation industrielle — Spécification de messagerie industrielle
```

This page serves as a reference for technical professionals working in the field of power utility automation and industrial automation, providing a list of relevant standards and specifications.

---

## Page 185

![Image 1 from page 185](images/iec61850-7-3{ed2.0}b_page185_img119_48a278d8.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank page from a technical document, likely a schematic or a flowchart, given the context of the OCR text. The page is devoid of any graphical elements, text, or annotations, suggesting it might be a placeholder or a page break within a larger document.

**Key Elements:** None, as the image contains no discernible content.

**Extracted Text:** None detected.

---

## Page 186

![Image 1 from page 186](images/iec61850-7-3{ed2.0}b_page186_img120_b284b55e.png)

**Image Type:** Diagram

**Description:** The image appears to be a simple, blank white space with no discernible technical elements, components, or data. It lacks any text, symbols, or graphical representations that would typically be found in a technical diagram.

**Key Elements:** None

**Extracted Text:** No text detected

This image does not provide any technical information or data that can be analyzed or described further. It is a completely blank white space, which suggests it might be a placeholder or an error in the document. If this is part of a larger technical document, it is recommended to check the surrounding pages for any relevant information or context.

![Image 2 from page 186](images/iec61850-7-3{ed2.0}b_page186_img121_0fce55c5.png)

**Image Type:** Document

**Description:** The image is a scanned page from a technical document, specifically from the International Electrotechnical Commission (IEC). It appears to be a reference or contact page, providing information about the organization and contact details for inquiries.

**Key Elements:**
- **Organization Name:** International Electrotechnical Commission (IEC)
- **Address:** 3, rue de Varembé, PO Box 131, CH-1211 Geneva 20, Switzerland
- **Telephone Number:** +41 22 919 02 11
- **Fax Number:** +41 22 919 03 00
- **Email Address:** <EMAIL>
- **Website:** www.iec.ch

**Extracted Text:**
INTERNATIONAL
ELECTROTECHNICAL
COMMISSION

3, rue de Varembé
PO Box 131
CH-1211 Geneva 20
Switzerland

Tel: + 41 22 919 02 11
Fax: + 41 22 919 03 00
<EMAIL>
www.iec.ch

---

