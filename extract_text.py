#!/usr/bin/env python3
"""
Enhanced GPU-Accelerated PDF Text Extraction with OCR Integration

This script uses the enhanced PDF processor with GPU-accelerated OCR
to extract text from PDFs while avoiding overlap between native text
and OCR text. Optimized for RAG (Retrieval Augmented Generation) applications.

Features:
- GPU-accelerated OCR (PaddleOCR, EasyOCR, or Tesseract)
- Intelligent text overlap detection
- Native PDF text extraction with OCR fallback
- Image and diagram preservation
- Markdown output optimized for RAG systems

Requirements:
pip install paddlepaddle-gpu paddleocr easyocr pytesseract PyPDF2 pymupdf Pillow requests langdetect
"""

import os
import sys
from pathlib import Path
from typing import Optional

# Import the enhanced processor
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("pdf_processor", "pdf-to-markdown-processor.py")
    if spec and spec.loader:
        pdf_processor = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(pdf_processor)
        EnhancedPDFProcessor = pdf_processor.EnhancedPDFProcessor
        ProcessingConfig = pdf_processor.ProcessingConfig
    else:
        raise ImportError("Could not load pdf-to-markdown-processor.py")
except ImportError:
    print("Error: Could not import EnhancedPDFProcessor")
    print("Make sure pdf-to-markdown-processor.py is in the same directory")
    sys.exit(1)


def extract_text_with_gpu_ocr(pdf_path: str, output_dir: Optional[str] = None, use_gpu: bool = True) -> bool:
    """
    Extract text from PDF using GPU-accelerated OCR with overlap detection.

    Args:
        pdf_path: Path to the PDF file
        output_dir: Output directory for markdown files (optional)
        use_gpu: Whether to use GPU acceleration for OCR

    Returns:
        bool: True if extraction was successful
    """

    # Configure the processor for optimal RAG performance
    config = ProcessingConfig(
        # OCR Configuration
        use_gpu_ocr=use_gpu,
        ocr_engine="paddleocr",  # Best GPU performance
        ocr_languages=["en"],
        ocr_confidence_threshold=0.6,  # Higher threshold for better quality
        ocr_fallback_to_cpu=True,
        text_overlap_threshold=0.8,  # Detect 80%+ similarity as overlap
        prefer_native_text=True,  # Prefer native PDF text when available

        # Image Processing
        min_image_size=100,  # Larger minimum for better quality
        image_quality=90,  # Higher quality for RAG applications
        extract_tables=True,

        # LLM Configuration (optional)
        ollama_url="http://localhost:11434",
        model_name="llama2",
        temperature=0.1,

        # Processing
        chunk_size=3000,  # Larger chunks for better context
        max_retries=3,
        timeout=120  # Longer timeout for GPU processing
    )

    # Get input directory from PDF path
    pdf_file = Path(pdf_path)
    if not pdf_file.exists():
        print(f"Error: PDF file not found: {pdf_path}")
        return False

    input_dir = pdf_file.parent
    if output_dir is None:
        output_dir = str(input_dir / "markdown_output")

    try:
        # Initialize the enhanced processor
        print("Initializing Enhanced PDF Processor with GPU OCR...")
        processor = EnhancedPDFProcessor(
            input_dir=str(input_dir),
            output_dir=str(output_dir),
            config=config
        )

        # Process the specific PDF file
        print(f"Processing: {pdf_file.name}")
        success = processor.process_pdf(pdf_file)

        if success:
            print(f"✅ Successfully processed {pdf_file.name}")
            print(f"📁 Output saved to: {output_dir}")

            # Show what was created
            output_path = Path(output_dir)
            md_files = list(output_path.glob("*.md"))
            image_files = list((output_path / "images").glob("*")) if (output_path / "images").exists() else []

            print(f"📄 Created {len(md_files)} markdown file(s)")
            print(f"🖼️  Extracted {len(image_files)} image(s)")

            return True
        else:
            print(f"❌ Failed to process {pdf_file.name}")
            return False

    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(
        description="GPU-Accelerated PDF Text Extraction with OCR",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python extract_text.py document.pdf
  python extract_text.py document.pdf --output ./output --no-gpu
  python extract_text.py document.pdf --ocr-engine easyocr
        """
    )

    parser.add_argument("pdf_path", help="Path to the PDF file to process")
    parser.add_argument("--output", "-o", help="Output directory for markdown files")
    parser.add_argument("--no-gpu", action="store_true", help="Disable GPU acceleration")
    parser.add_argument("--ocr-engine", choices=["paddleocr", "easyocr", "tesseract"],
                       default="paddleocr", help="OCR engine to use")

    args = parser.parse_args()

    # Set environment variables for configuration
    os.environ["OCR_ENGINE"] = args.ocr_engine
    os.environ["USE_GPU_OCR"] = "false" if args.no_gpu else "true"

    print("🚀 Enhanced PDF Text Extraction with GPU OCR")
    print("=" * 50)

    success = extract_text_with_gpu_ocr(
        pdf_path=args.pdf_path,
        output_dir=args.output,
        use_gpu=not args.no_gpu
    )

    if success:
        print("\n✅ Text extraction completed successfully!")
        print("📋 The output is optimized for RAG applications with:")
        print("   • Clean text extraction with OCR fallback")
        print("   • Preserved images and diagrams")
        print("   • Structured markdown format")
        print("   • No duplicate content from overlapping sources")
    else:
        print("\n❌ Text extraction failed!")
        sys.exit(1)


if __name__ == "__main__":
    # For backward compatibility, support direct execution with hardcoded path
    if len(sys.argv) == 1:
        # Default behavior for the original use case
        pdf_path = "pdfs/iec61850-7-2{ed2.0}en.pdf"
        if Path(pdf_path).exists():
            print("Using default PDF path (for backward compatibility)")
            success = extract_text_with_gpu_ocr(pdf_path)
            if success:
                print("Text extraction and formatting completed successfully.")
            else:
                print("Text extraction failed.")
        else:
            print(f"Default PDF not found: {pdf_path}")
            print("Usage: python extract_text.py <pdf_path>")
            sys.exit(1)
    else:
        main()
