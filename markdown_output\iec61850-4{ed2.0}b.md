# Iec61850 4{Ed2.0}B

## Summary

The document discusses various aspects of Utility Automation Systems (UAS) and Intelligent Electronic Devices (IEDs), including:

1. **Definitions**: The document defines key terms such as:
	* Utility Automation System (UAS)
	* Intelligent Electronic Device (IED)
	* Network Control Centre (NCC)
	* Telecommunication Environment (TE)
	* Process Environment (PE)
	* Human Machine Interface (HMI)

2. **System Structure**: The UAS consists of different IEDs that communicate with each other via communication channels, executing tasks concerning interactions with the environment.

3. **Parameter Categories**: Parameters are divided into two categories:
	* Configuration parameters: define the global behavior of the whole UAS and its IEDs
	* Operating parameters: define the behavior of partial functions of the system

4. **Parameter Types**:
	* System parameters (configuration data that determines co-operation of IEDs)
	* Process parameters (related to process environment)
	* Functional parameters (related to functional requirements)

5. **Engineering Requirements**: The engineering of a UAS is based on a system requirement specification, which defines the scope, functions, boundaries, and additional restrictions and requirements for the system.

6. **Testing and Validation**:
	* Conformance test
	* System test
	* Type test
	* Factory acceptance test (FAT)
	* Site acceptance test (SAT)
	* System requirements specification

## Key Points

* A UAS consists of different IEDs that communicate with each other via communication channels.
* Parameters are divided into two categories: configuration parameters and operating parameters.
* System parameters determine the co-operation of IEDs, including internal structures and procedures.
* The engineering of a UAS is based on a system requirement specification.
* Various testing and validation procedures are discussed.

## Parameterization

### Configuration Parameters

Configuration parameters define the global behavior of the whole UAS and its IEDs.

### Process Parameters

Process parameters describe all types of information that is exchanged between the PE and the UAS. They include:

* Command output times
* Suppression of transient events (filter time)
* Measured value damping (threshold value)
* Assignment of texts to events for visualization at the IED-level

### Functional Parameters

Functional parameters describe the qualitative and quantitative features of functionality used by the customer. Normally, they are changeable on-line.

They include:

* Target values (set points) of controllers
* Starting and tripping conditions of protection relays
* Automatic sequences such as operations after measurement overflow or commands in relation to specific events

Functional parameters are divided into switchable and non-switchable parameter value groups. A set of functional parameter values for a group of functional parameters can be resident in an IED in parallel with other sets of functional parameter values.

### Page 17

61850-4 (c) IEC:2011

## Additional Content

ble to switch between the sets
on-line.
5.3
Engineering tools
5.3.1
Engineering process
The system engineering process creates the conditions for designing and configuring an auto-
mation system to the specific plant (e.g. substation) and to the operating philosophy of the cus-
tomer based on the system requirements specification from the customer.
Within the engineering process, we can distinguish different actor roles:
The project requirement engineer sets up the scope of the project, its boundaries, interfac-
es, functions and special requirements ranging from needed environmental conditions, reli-
ability and availability requirements up to process related naming and eventual specific ad-
dress range restrictions or product usage. He defines what he wants to have application
wise and how he wants to operate the system (project requirement specification). He finally
accepts the delivered system.
The project design engineer defines, based on the requirements specification, how the sys-
tem shall look like; its architecture, requirements on the products needed to fulfil the re-
quired functions, how the products should work together. He thus defines the system de-
sign specification.
The manufacturer supplies the products from which the system is built. If necessary, he
supplies a project specific IED configuration.
The system integrator builds the system, engineers the interoperation between its compo-
nents based on the system design specification and the concretely available products from
the manufacturers, and integrates the products into a running system. This results in a sys-
tem configuration description.
The IED parameterizing engineer uses the set-up possibilities of the system and device
configuration to adjust the process, functional and system parameters of an IED to the pro-
ject-specific characteristics.
The testing and commissioning engineer tests the system on the basis of the system con-
figuration description, system design and requirements specification and additional docu-
mentation, and puts the system into operation.
It can be that the same person or organisation has more than one role, e.g. a manufacturer is
also system integrator, or a customer does system integration by himself. This influences the
packaging and formal organisation, however not the tasks which have principally to be per-
formed.
The concrete engineering process is dependent also on responsibilities for parts of the system,
and how they relate together. Even if a system integrator is also manufacturer, he might have
to integrate products from other manufacturers. A customer might want to have a system with
interfaces to a system of another customer. Across these organisational interfaces a data ex-
change in a standardized form should be possible.
A typical project will start with the project requirement engineer creating a project requirement
specification that defines the scope of the project, single line diagrams, device ratings and oth-
er required data. The aim is to create a set of technical specifications that can be used for ten-
dering and engineering, irrespective of whether design and installation work will be done in-
house or by external parties. Beneath general interfacing requirements, this includes also the
identification or at least naming rules for primary and secondary equipment, and any communi-
--- Page 18 ---
61850-4 © IEC:2011
cation addresses or addressing schemes needed to interface with other systems of the cus-
tomer. Further needed redundancy requirements, response times, availability and safety
measures have to be stated beneath the environmental, physical and geographical restrictions
for the project.
IEC 61850-6 provides a formal means to define the single line diagram with customer’s func-
tional names and the intended automation system functionality at the primary equipment identi-
fied in the single line description (SSD, system specification description). This formal descrip-
tion is based on the hierarchical structure of IEC 81346-1, allows however instead of identifica-
tions according to IEC 81346-2 also customer specific identifications, and additionally customer
specific descriptive text. It further defines a formal way to exchange function and communica-
tion related interface descriptions between systems respective between system projects (by
means of an SED, system exchange description).
Based on this requirement specification and its knowledge about existing solutions and prod-
ucts, the project design engineer designs the functional and physical system architecture inclu-
sive communication system to reach the needed response times and reliability, and produces
the specifications for the products to be used. The details form a system design specification,
which is typically approved by the project requirement engineer, and is then used as a base for
the product manufacturer to deliver the needed products with the specified configuration. The
resulting system design specification can be supported by a formal description of IEDs, the
functions on them, and their relation to the process functionality as defined in IEC 61850-6
(SCD, system configuration description). The system integrator uses this specification to order
the fitting products and to build the system from the products. The manufacturer supplied IEDs,
before integration into the system, come with a formal description of their functional and com-
munication engineering capability (ICD, IED capability description), which is then used as base
to engineer the system configuration.
Often a part of the system design specification is produced by the project design engineer dur-
ing the tendering process. This first order system design specification together with the system
requirement specification is then the start for the project system design.
The basic engineering process shown in Figure 3 starts with producing the system design
specification (system design) based on the tender specification already approved by the project
requirements engineer:
System design
Check lists for
 process signals
 functionality
Hardware configuration
Parameterization
Source parameter set
Process data lists
Substation
automation
system
Documentation
Hardware
documentation
Parameter
documentation
IEC   106/02
System specification
System configuration
Figure 3 – Engineering tasks and their relationship
System design is the definition of the technological concept to solve the required automation
system tasks including the choice of structure, IED type selection and IED basic configuration
as well as the determination of interfaces between the IEDs and the PE. The result is the sys-
tem design specification.
--- Page 19 ---
61850-4 © IEC:2011
In the configuration process the required system functions will be created or activated within a
selected group of IEDs. With that a set of parameters containing system and IED configuration
data will be available. Depending on the IEDs capability this can be performed in a pre-
engineering phase either by the manufacturer, the IED parameterization engineer or by the
project design engineer.
Parameterization, often called detail engineering, is the generation of the parameter set for the
UAS. The system configuration data (system parameter set) is produced by the system integra-
tor. The IED configuration data (IED parameter set) is produced by the IED parameterizing
engineer.
Documentation is the description of all project and parameterization agreements about the fea-
tures of the system and its link to the PE according to the required standards.
In practice, engineering tools are useful for efficient handling of the engineering tasks. To bet-
ter support interoperability between tools of different IEDs and different manufacturers, within
this standard conceptually three kinds of tools are envisaged:
system specification tool: allows specifying the system and device requirements regarding
the needed system functional and process capabilities;
system configuration (system design) tool: allows selection of needed IEDs based on a sys-
tem (requirements) specification, and defines the communication connections between the
IEDs of the system and the logical relations between IED functionality and the primary
equipment. Often the system configuration tool includes a system specification tool;
IED configuration (parameterization) tool: allows making the detailed parameterization of an
IED based on a system design and requirement specification beforehand and a system de-
scription delivered by the system configuration tool after the system configuration process.
To enable interoperable exchange of engineering data between IED parameterization tools of
different manufacturers and the system configuration tool, as well as between different system
configuration tools handling different system parts as separate projects, appropriate configura-
tion data exchange formats are defined in IEC 61850-6.
5.3.2
System specification tool
In the project requirement phase a system specification tool allows to describe parts of the
process to be controlled at the level of a single line as well as process related names and the
required functions to be performed in parts of the process in a formalized way. This formal de-
scription can support evaluation of needed products as well as be input to a system configura-
tion tool in the system design phase. Mostly the tool is based on a template data base for the
standardized functions and their needed signals and typical parts of the process.
The standard language defined in IEC 61850-6 offers a standardized description of a part of
the system requirements specification.
5.3.3
System configuration tool
The system configuration tool offers the choice of components with functional assignments in
the design stage of an automation system project. Mostly the tool is based on an IED or solu-
tion database and requires as minimal input the required functions and process signals. It pro-
vides the first results using, for example, tables and check lists, which have to be agreed upon
between project requirements engineer and project design engineer. As a result, the system
structure and configuration, including the interfaces to the PE, will be defined. In a second step
then the communication connections between the IEDs are configured by the system integra-
tor, so that the intended system functionality is implemented.
The standard SCL language defined in IEC 61850-6 allows configuration data exchange be-
tween system configuration tool and IED configuration tool as well as between two different
system configuration tools respective projects, and also of the functions and communication
--- Page 20 ---
61850-4 © IEC:2011
capabilities of IEDs, which might be used as external inputs to the system configuration tool for
product selection.
It is the intention of this standard to enable IED type and manufacturer independent implemen-
tations of this type of tools in that sense, that system configuration tasks can be done inde-
pendent from the used IEDs, and the engineering result transferred to the IED respective IED
tool in a standardized form. For this purpose a system configuration tool shall be able to import
IED descriptions and system interface descriptions in SCL and export system configuration
descriptions in SCL.
5.3.4
IED configuration tool
The IED configuration tool supports the creation of the consistent IED parameter set for a spe-
cific IED within the system. This (set of) tool(s) is mostly manufacturer specific, or even IED
type specific. The basic IED function specification as well as all system related configuration
data is imported from the system configuration description produced with the system configura-
tion tool. For this purpose an IED configuration tool shall support the import of system configu-
ration descriptions in SCL language as defined in IEC 61850-6. Further IED specific configura-
tion data like implementation of special functions and settings or IED specific parameters are
performed with this tool.
The main tasks of the tool are the generation of process data lists based on the IED parameter
set and the secure management of the process data lists for the IEDs. The tool must be capa-
ble of reading actual parameter values.
Additionally, the tool supports the management, archiving and documentation of the IED pa-
rameter set.
Essential components of the tool are shown in Figure 4.
Project engineer’s task
Process parameters
Functional parameters
Operating parameters
System integrator`s task
System requirements
System parameters
IED configuration tool
Input module
Data management
Output module
Process data lists
for input into the UAS
IED parameter set
for archive and
modification management
Transfer to
documentation tool
IEC   107/02
Figure 4 – IED configuration process
The tool’s data input module supports the interactive input of parameters as well as the import
of the system description as created by means of the system configuration tool. The structure
of input data should be technically oriented towards the substation architecture, i.e. structured
according to the hierarchical approach to substation, voltage level, bay, equipment and func-
tion.
The repeated input of similar information should be avoided as much as possible by using for
example templates of typical solutions or copy functions (for example, copy of switch, bay,
busbar sections, etc.).
--- Page 21 ---
61850-4 © IEC:2011
The entry of a parameter should only be necessary once. The assignment of this parameter to
other processes should be carried out automatically in order to guarantee parameter con-
sistency at all times.
The data management module checks the entered parameter values with respect to their con-
sistency and plausibility. Parameters with multiple use will be assigned to the respective pro-
cesses.
Furthermore, the data management module includes the system information management with
respect to the IED parameter set. The system information contains a unique identification of
the parameter set, including
substation identification;
document identification and version identification;
parameter set version identification;
engineer’s identification;
access permission;
date of creation / modification;
software releases of the IEDs and the parameterization tool;
IED instance name in the project.
The data management module generates the process data lists, which are the base for the
behaviour of the automation system in accordance with the substation and the customer re-
quirements.
The output module is responsible for the transfer of process data lists to an archive (internal or
external) or for the direct input into the system and its IEDs. Additionally, it provides the service
to recall and view the source parameters stored in the archive. The output module must pro-
vide the source parameters for the documentation tool.
5.3.5
Documentation tool
The documentation tool generates uniform, project specific documentation in accordance with
the required standards (IEC 61175, IEC 60848, IEC 81346 series, IEC 61082 series). The doc-
umentation consists of:
hardware documentation for the representation of all external connections between the
system components and the PE which are defined in the project design process;
software documentation in form of (principle) function charts, sequence diagrams, flow
charts as needed;
parameter documentation for the representation of all internal qualitative and quantitative
relations, which are agreed in the parameterization process.
The documentation tool should be capable of creating a “revision history”, documenting all
changes known to the tool itself.
5.4
Flexibility and expandability
Flexibility and expandability of an automation system requires the expandability of the hardware
and software configuration of the system. It also depends on the functional and physical archi-
tecture and the resulting dependency between functional parts.
The flexible extension of the hardware configuration with additional IEDs or with IEDs of differ-
ent functionality is the first requirement in order to meet flexibility and expandability of the sys-
tem.
--- Page 22 ---
61850-4 © IEC:2011
The flexibility and the expandability also depend on the engineering tools. The most essential
engineering tool with respect to the behaviour and maintenance of the automation system is
the IED configuration tool and its handling of different parameter sets in relation to the IED.
Observe that an IED configuration tool is specific for a manufacturer or even an IED type, and
therefore several IED configuration tools might be needed in a project containing IEDs from
several manufacturers.
Therefore, functionality, compatibility and expandability of the IED configuration tool are signifi-
cant for further functional expansion of the system. As a minimum it shall support the compati-
bility features for different versions of this standard as defined in IEC 61850-6 and in all parts
of IEC 61850-7.
The IED configuration tool of a manufacturer shall be backwards compatible, i.e. it shall be
possible to parameterize all existing IEDs of the same family supplied by the manufacturer us-
ing the most recent parameterization tool.
All configuration tools shall be able to run on commercial hardware with a commercial operat-
ing system. They shall be able to support flexible and consistent modification of existing pa-
rameter sets with version identification.
The system configuration tool shall provide open interfaces for data exchange with other con-
figuration tools, for example for dispatching centres and tools from other manufacturers. As a
minimum it shall support export and import of SCL files as defined in IEC 61850-6.
5.5
Scalability
The system configuration tool should be able to be used for all typical UAS applications. Gen-
erally, the UAS systems are designed in such a manner that they can cover the whole range of
applications by using a modular device system with respect to
task (transmission or distribution network) and voltage range (medium, high or ultra high
voltage) of the substation;
completion level of the application (simple centralized telecontrol unit or integrated
substation control, monitoring and protection with distributed artificial intelligence);
complexity of the functionality (from simple SCADA up to sophisticated automation tasks);
telecommunication functions (simple telecommunication to one dispatching centre, node
functionality with different telecommunication protocols, master in the common mode with
integration of other substations).
The system configuration tool should permit scalability in such a way that the configuration task
for different application levels can be carried out with a minimum of resources and costs. The
lowest level, for example, requires only the input of parameters for a simple telecommunication
unit, and on the highest level all available options of the system must be managed.
Furthermore, the system configuration tool should support the engineering rationalization by
using, for example, templates, macros and copy functions.
5.6
Automatic project documentation
5.6.1
General
The documentation of an UAS consists of two project specific components (see Figure 5).
--- Page 23 ---
61850-4 © IEC:2011
Parameter documentation
Relation by
uniform identifiers
Parameter documentation tool
Configuration list
Function diagrams for
internal features
Signal list
Parameter lists
Graphical displays
Hardware documentation
HW documentation tool
Circuit diagrams
Function diagrams for
external equipment
Follow-up
documentation
– terminal
connection
– cable list
Signal list
IEC   108/02
Figure 5 – Project related documentation of UAS
The hardware documentation consists of:
circuit diagrams for the link between the UAS components and for their connection
with the PE;
signal connection lists;
function diagrams for external schemes;
cubicle layouts and wiring / cabling lists.
The system and IED parameter documentation consists of:
the configuration list;
signal lists;
parameter lists;
communication network addresses;
graphical representation of all displays and operation menu sequences;
function diagrams or function descriptions.
The requirement on the engineering tools is that the documentation should be generated as:
a) hardware documentation with the help of the input values of the planning tool on a CAD (or
similar) system;
b) parameter documentation using the IED parameter set from the parameterization tool;
c) system configuration documentation using the system parameter set from the system
configuration tool as needed.
The interfaces between hardware and parameter documentation are the signal lists, which
should have uniform and unique signal identifiers in both documents, preferably based on the
semantically standardized identifications defined in other parts of this standard.
The generation of documentation, based on the inputs of the planning and parameterization
tool, should ensure the consistency between documentation on one hand and the project check
lists, the IED parameter set and process data lists on the other hand.
--- Page 24 ---
61850-4 © IEC:2011
5.6.2
Hardware documentation
The hardware documentation of the system should be carried out according to the same struc-
ture as the documentation of the other substation equipment.
Concerning the identification and the structure of the hardware documentation the use of inter-
national standards (for example, IEC 61175, IEC 81346 series) is recommended.
5.6.3
Parameter documentation
*******
General
Parameter documentation is typically done in lists and tables, supported by figures showing
principle solutions. To get a better overview it is recommended to produce the documentation
for typical objects and functions, and then have a higher level list about the object instances of
each documented type.
*******
Configuration list
The configuration list and the single line diagram of the substation are the starting point for the
parameter documentation. The configuration list consists of:
an overview of IEDs and components of the system with identification of the hardware and
software releases;
identification of the software release of the configuration tool(s);
identification of the parameter sets according to the requirements in 5.3.4.
The parameter documentation is carried out in different ways for the different parameter types.
*******
System parameter documentation
The system parameters to be set onto the IEDs can be taken over as a chosen set from the
manufacturer’s standard documentation into the project specific documentation. Project specif-
ic system parameter sets are generated by the system configuration tool and can also be doc-
umented by it.
*******
Process parameter documentation
The documentation of process parameters consists of the description of all signals at the sys-
tem border, and details their further management and marshalling inside the system. The fol-
lowing description documents are typically included in the process parameter documentation
set:
signal lists are the base for the further process parameter lists. The signal lists give the
overview of all analogue and binary signals and their assignment to the inputs and outputs
of the IEDs of the system and to the specific parts of the documentation;
telecontrol mapping lists determine the assignment of individual signals to the addresses of
the telecontrol protocol;
message texts can be defined by the customer and assigned to the binary signals for
representation in different reports;
characteristic curves can be assigned to the analogue values;
HMI lists describe the presentation features of signals on displays and printers;
archiving lists cover all information about values of which signals have been archived under
which conditions and with which attributes;
acquisition lists include all information about qualitative attributes of signal acquisition such
as filter times of binary inputs or command times.
--- Page 25 ---
61850-4 © IEC:2011
*******
Functional parameter documentation
The functional parameters should be documented as parameter lists and graphically as func-
tion diagrams.
To provide greater clarity, and in accordance with the rules of circuit diagrams, the function
diagrams should be structured as follows:
control (automatic single and double commands, group commands, switching sequences);
position indication (assignment to commands, parallel work of transformers, voltage
definition for busbar section);
event/alarm indication (group information, automatic operation);
interlocking;
measurement linking (overflow, bimetal);
algorithms for closed loop control;
protection.
The operation sequences and the structure and symbols of the overview and detail displays
should be documented graphically.
The number and type of report lists and protocols should be documented as a parameter list.
Requirements concerning the design and the structure of the function diagrams are defined in
international and national standards (for example, IEC 61082 series).
5.6.3.6
Operating parameter documentation
The operating parameters should be documented as a parameter list with their ranges of val-
ues and basic settings. The values changed by the customer are documented in the operations
report.
5.6.4
Requirements of the documentation tool
The input of the documentation tool is the IED parameter set, which is created with the param-
eterization tool. The parameter documentation tool produces the complete parameter docu-
mentation as a book with automatic generation of a table of contents.
The parameter documentation tool should be able to generate partial documentation according
to different sorting criteria with practical benefit, for example:
reference lists for telecontrol information;
message lists, sorted by IED addresses;
function diagrams for interlocking.
All changes of parameters must be flagged in the documentation. The parameter documenta-
tion tool should be able to support the requirements with respect to such modification services.
5.7
Standard documentation
The standard documentation is the description of the device and the functions of one IED or
the UAS product family of a manufacturer which is universally valid and which is not changed
for purposes of specific projects.
As a general rule, the standard documentation includes:
equipment description;
--- Page 26 ---
61850-4 © IEC:2011
instruction and maintenance manual;
system concept description;
description of functions;
operating instructions;
instruction for service programs;
fault detection and maintenance instruction;
user manual for the engineering tools.
The standard documentation should complete the project specific documentation for each in-
stalled system.
5.8
System integrator's support
In most cases, the engineering tasks are included in the system integrator’s offer for the UAS
project.
In all cases, however, the system integrator has to offer the engineering tools needed for sys-
tem maintenance and appropriate customer training for the use of these tools so that the cus-
tomer may maintain and expand the system installation.
The system integrator should support this process with consultative services, training and regu-
lar information regarding updates and extended functionality of the system installation and the
engineering tools.
System life cycle
6.1
Requirements of product versions
The life cycles of an UAS and its IEDs are subject to differences of the manufacturer’s and the
customer’s point of view, as shown in Figure 6:
the manufacturer’s product life cycle contains the period between the start of production
and the discontinuation of the UAS product family;
the customer’s system life cycle contains the period between the site commissioning of the
first system installation, often based on several UAS product families, and the
decommissioning of the latest system installation. The system installation may be carried
out by a system integrator who is different from the product manufacturer.
--- Page 27 ---
61850-4 © IEC:2011
Customer's life cycle
Decommissioning
SAS 1
Site commissioning
Project n
Decommissioning
SAS n
Update
Extension
Maintenance
Contract
Project n
Site commissioning
Project 1
Contract
Project 1
Start of
production
Discontinuation
Change requirements
Manufacturer's life cycle
V = Version
IEC   109/02
Figure 6 – Two meanings of the system life cycle
During the manufacturer’s life cycle of the UAS and its IEDs, a number of changes and exten-
sions are required for various reasons:
functional improvements and extensions;
technology changes in the hardware;
correction of recognized problems.
These changes lead to updated IED versions of hardware, software and supporting tools.
A new version of an IED can produce different impacts:
it influences changes needed to the configuration compatibility list of the UAS-product
family, in that the new version of the IED requires version changes in other IEDs or in the
engineering tool, for example to fulfil new overreaching functions. A system test together
with relevant IEDs is necessary and leads to a new system configuration list;
it is independent of other IEDs and compatible with the current configuration list. The
system test of the IED has to check the compatibility with the other IEDs in the system.
Only the version of the IED will be changed. The configuration list version of the system has
to be modified.
The manufacturer is obliged to provide identification of the IED versions:
in the case of IED software or the supporting tools software, the version information is
available in a self identifying manner (for example, on display or PC);
for the hardware, the version information is available at the board and at the device levels;
if the functionality has changed or a function has been removed, a new configuration
compatibility list shall be distributed.
The co-ordination of the manufacturer’s and the customer’s life cycles requires that new ver-
sions of the IEDs with identical model numbers shall comply with the following rules.
a) The hardware shall be compatible. All interfaces must perform the same function in the
same places. The sizes of the boards and the devices must be identical.
--- Page 28 ---
61850-4 © IEC:2011
b) The functional changes from the previous version of the product software should be
declared.
c) The supporting tools shall be downward compatible, which means that the new version of
the supporting tool shall serve all existing versions of the same product family.
The manufacturer has to inform the customer about all of the functional changes and exten-
sions that are carried out between the last delivery and a new offer.
From an UAS system maintenance perspective identical or backwards compatible products are
preferred for replacement of failed parts. In case that functionally but not engineering wise
compatible products are used in this case, a re-engineering of a part of the UAS might be nec-
essary.
6.2
Announcement of product discontinuation
The manufacturer is to inform all customers of the product discontinuation in time to ensure
that the customers have the option to order spare products or to prepare extensions.
In the case where the product discontinuation will be carried out without a subsequent func-
tionally compatible product, the required notice shall be published in a defined period in ad-
vance.
In the case where a subsequent functionally compatible product will follow, the notice may be
published in a shorter period in advance. An overlap for delivery of both products for a mini-
mum period is required (an example is given in Annex A).
6.3
Support after discontinuation
During the customer’s life cycle of a system and its IEDs, a number of changes, extensions and
maintenance issues will occur. The manufacturer is obliged to support this process after the
discontinuation of the UAS product family and its compatible IEDs according to the agreement
between system integrator respective customer and manufacturer. The following examples
could be used for such agreements:
special customer agreement for further supply with a minimum annual order with special
agreed prices and delivery conditions in an agreed time period;
supply of the same or compatible IEDs (from the point of view of functionality, mounting and
wiring) for extensions under specific delivery conditions for an agreed time period;
supply of spare parts and repair service under specific delivery conditions for an extended
time period;
administration, maintenance and delivery of all supplied versions of the IED software and
the service tool software in accordance with the agreed delivery conditions by the
manufacturer. The maintenance of parameter sets is the responsibility of the customers;
support in the integration of new products using adaptive interfaces.
An example for the corresponding time conditions is shown in Annex B.
The above requirements concerning the “system life cycle” exclude the use of commercially
available computing products (for example, PCs, CD ROMs).
In the case where the manufacturer and the system integrator are different, the support after
discontinuation shall be agreed in relevant contracts.
--- Page 29 ---
61850-4 © IEC:2011
Quality assurance
7.1
Division of responsibility
7.1.1
General
The quality assurance of a system is a common task of the system integrator/manufacturer and
of the customer with different areas of responsibility. If two or more parties are involved, then
the responsibilities of each party shall be defined at the time of procurement.
7.1.2
Responsibility of the manufacturer and system integrator
*******
Quality system
The manufacturer and the system integrator should establish and maintain a quality system in
accordance with ISO 9001.
The stages of quality assurance as a responsibility of the manufacturer and system integrator
are shown in Figure 7.
Customer 1
Verification *
Customer x
Customer n
Project 1
Development
(EN ISO 9001)
System test
Prototype series
Type test
Project x
Routine test
of products
FAT *
of system
equipment
Site
commissioning
Project n
Project n
Trial operation *
Warranty
Service
maintenance
support
* optional
Site
acceptance
test
Decommissioning
Market approval
Delivery and putting online
Product realization
Customer's life cycle
Conformance
Test
IEC   110/02
Figure 7 – Stages of quality assurance – Responsibility of manufacturer
and system integrator
*******
Test responsibilities
The manufacturer is responsible for the correct handling of type tests and system tests of his
individual products. Type tests and system tests are preconditions for starting the regular de-
livery.
All IEDs have to pass device specific routine tests defined by the manufacturer to ensure quali-
ty before the products are handed over for delivery.
Customer specific verifications and approvals may be required according to the customer’s
philosophy and shall be negotiated between the system integrator and the customer. These
might be done by the customer at product level as well as at system level.
--- Page 30 ---
61850-4 © IEC:2011
The system integrator is obliged to prepare and carry out these special investigations with indi-
vidual products and the overall system. Furthermore, the system integrator is obliged to prove
the fulfilment of the technical requirements, including performance criteria. An IED conform-
ance test reduces here the risk for the system integrator.
When introducing a system, the system integrator is responsible for ensuring that all functions
are jointly tested by the representatives of the system integrator and the customer during the
optional factory acceptance test (FAT) and the mandatory site acceptance test (SAT) with the
specific configuration and parameter set of the customer. Observe that before these tests ap-
propriate integration and commissioning phases take place, which are in the responsibility of
the system integrator. The successful finishing of the FAT (if required) is the precondition for
the equipment delivery and the further site acceptance test at the customer’s premises. FAT
and SAT, as well as their contents, shall be negotiated between the customer and the system
integrator.
The commissioning of the system on site before the SAT is normally the responsibility of the
system integrator. Commissioning is followed by a trial operation phase (for example, one
month). The length of this phase and the conditions to be met, e.g. trial operation before or
after SAT, should be negotiated between the customer and the system integrator.
It is the responsibility of the manufacturer to maintain a quality assurance process, by which
any product related errors found during project tests will go back into the next product version.
The handling of new versions is described in 6.1.
*******
Warranty and after sales service
After the site commissioning, the warranty begins in accordance with the agreed conditions for
the hardware;
the engineering;
the software.
Any faults of a product type detected during the warranty phase that may also appear in other
projects shall be communicated to the respective system integrators and customers. It is the
responsibility of the customer to decide if a new version of the product shall be installed or not.
After the warranty, the system integrator or the manufacturer should provide after sales ser-
vice:
the supply of spare parts for an agreed period;
the support in diagnosing failures;
the mandatory provision of urgent information to the customers about malfunctions;
the correction of detected software errors and hardware defects;
the offer and introduction of software updates.
7.1.2.4
Diagnostic
The manufacturer should develop and offer special diagnostic tools for
failure definition inside or outside the system;
failure localization inside the system and the individual IED’s.
The diagnostic tools should be designed to be used remotely, if appropriate.
The technical documentation of the system and its individual products shall include the recom-
mended preventive maintenance (for example, for batteries, capacitors).
--- Page 31 ---
61850-4 © IEC:2011
7.1.3
Responsibility of the customer
The customer is responsible for ensuring that the relevant environmental and operating condi-
tions of the system satisfy the conditions described in the technical documentation of the sys-
tem and its individual products.
The customer has to carry out preventive maintenance for service or exchange of maintainable
parts in accordance with the instructions of the manufacturer.
The inspection and regular check of individual products and their inter-related function (for ex-
ample, protection – circuit breaker) will be necessary from time to time in accordance with the
recommendations of the manufacturer or the customer’s standards organization (IEE, VDEW,
IEEE, etc.).
Corrective maintenance should be carried out immediately after detection of defects, to obtain
the highest possible availability.
7.2
Test equipment
7.2.1
General
The test equipment includes all equipment that is required for the acceptance test and commis-
sioning. The test equipment is used to provide the verification of all inputs and outputs of the
primary equipment, the communication with the network control centre and the functionality of
the individual IEDs of the automation system (for example, protection).
Additionally, the test equipment is necessary to prove the behaviour and the performance
characteristics of the system. With respect to the functionality and performance requirements,
the test equipment is divided into three categories:
normal process simulation;
transient and fault process simulation;
communication check and simulation.
7.2.2
Normal process test equipment
This test equipment, in its simplest form, must be able to provide all alarms and position indica-
tions for the substation control system, enable the simulation of measured values (including
over range) and be able to display all commands from the UAS.
More complex test equipment must be able to simulate reactions of the switchgear in real time.
Such test equipment can be used to check dynamic processes such as switching sequences or
synchronization. There is a need to be able to generate various conditions for the reactions, for
example to produce intermediate positions of switchgear or to simulate an earth fault on one
busbar section during a switching sequence.
Test equipment should also be capable of generating a large quantity of data traffic in a short
time or intermittent data traffic on a regular basis.
7.2.3
Transient and fault test equipment
This test equipment should be capable of injecting programmable transients of voltages and
currents in a three-phase power system, simulating many kinds of faults or other abnormal
processes such as power swing, saturation of current transformers and others. The test equip-
ment should be capable of producing simulated faults, thus producing disturbance records.
--- Page 32 ---
61850-4 © IEC:2011
7.2.4
Communication test equipment
This test equipment is used for performing tests at all communication channels for:
internal links of the system;
telecommunication.
The communication test system should be a convenient and efficient tool which enables the
performance of the following functions at all required levels (network control centre, substation,
bay and process level):
simulation of a server, simulation of a client, monitoring of the data traffic;
quality analysis of the data traffic (for example, the quality of electrical signals, time breaks,
etc.).
7.3
Classification of quality tests
7.3.1
Basic test requirements
The manufacturer should provide a test concept that covers all activities beginning with proto-
type functional tests in the development state to the final type and system tests. The scope and
object of tests, the test procedures and the passing criteria must be specified.
All tests shall be documented in such a way that the results are reproducible, if required.
All tests should be performed by an internal part of the manufacturer’s organization that is
qualified for performing the tests and has the organizational independence to state whether a
product has passed the tests or not, or by an independent external organisation, qualified for
the tests by a third party.
7.3.2
System test
The system test is the proof of correct functionality and the performance of each IED under
different application conditions (different configuration and parameters) and in co-operation
with other IEDs of the overall UAS product families including all too

**Introduction**

It appears that you have provided an excerpt from a standard or technical document related to automation systems for electric companies, specifically the IEC 61850 standard. Here is an analysis and translation of the excerpt:

**Introduction**

The excerpt describes the requirements related to the study of an automation system for electric companies. This is a standard that defines the principles and procedures for designing, developing, and implementing automation systems for electrical networks.

**Requirements Related to the Study**

The study of an automation system is based on a specification of the system's requirements, which defines the scope, functions, limits, and other restrictions and requirements for the system. The study includes:

1. Definition of the necessary material configuration of the UAS: definition of all IEDs (Intelligent Electronic Devices) and different interfaces with the environment.
2. Adaptation of features and signal magnitudes to specific operational needs using parameters.
3. Documentation of all specific definitions (e.g., parameter sets, connections, etc.).

**Structure of the UAS and its Environment**

Figure 1 shows the structure of the UAS and its environment. The UAS consists of several IEDs that communicate with each other through communication channels and execute tasks in interaction with the system automation environment.

**Categories and Types of Parameters**

Parameters are data that control and condition the operation:

* Of the material configuration (decomposition into IEDs)
* Of IED software
* Of the process environment (primary and auxiliary equipment)
* Of the IHM with different support tools
* Of the telecommunications environment

The entire set of parameters and configuration data for a UAS is called a UAS parameter set.

**Conclusion**

In summary, the excerpt describes the requirements related to the study of an automation system for electric companies. It defines the structure of the UAS and its environment, as well as the categories and types of parameters necessary to operate the site and meet specific client needs.

If you have any questions or would like more information on the IEC 61850 standard, please do not hesitate to ask.

---

# Images and Diagrams

## Page 1

![Image 1 from page 1](images/iec61850-4{ed2.0}b_page1_img1_28019bb6.jpeg)

**Image Type:** Schematic

**Description:** The image appears to be a technical schematic, likely representing a system or network layout. It features a combination of curved and straight lines, possibly indicating connections or pathways between different components. The design is abstract and geometric, with a focus on symmetry and precision.

**Key Elements:**
- Curved lines: These could represent signal paths, flow lines, or connections between nodes.
- Straight lines: These might indicate fixed connections or boundaries.
- Symmetry: The design is symmetrical, suggesting a balanced or structured system.
- Geometric shapes: The use of circles and arcs indicates circular or radial components within the system.

**Extracted Text:** No text detected

This image is likely used in technical documentation to illustrate a system or network layout, possibly for a technical report, presentation, or design document. The lack of text suggests it is a visual representation rather than a textual description.

![Image 2 from page 1](images/iec61850-4{ed2.0}b_page1_img2_41ec8811.png)

**Image Type:** Logo

**Description:** The image is a logo consisting of the letters "IEC" in white, bold, sans-serif font. Below the letters, there are three horizontal lines with a small circle at the end of the third line. The background is a solid blue color.

**Key Elements:**
- The letters "IEC" are prominently displayed in white.
- The font is bold and sans-serif.
- Below the letters, there are three horizontal lines.
- The third line ends with a small circle.

**Extracted Text:** IEC

![Image 3 from page 1](images/iec61850-4{ed2.0}b_page1_img3_df73e7ad.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document. It is designed to provide an overview of the document's content and its relevance to the field of power utility automation. The document is part of a series, specifically IEC 61850-4, which is an international standard published by the International Electrotechnical Commission (IEC).

**Key Elements:**
- **Title:** "Communication networks and systems for power utility automation – Part 4: System and project management"
- **Subtitle:** "Réseaux et systèmes de communication pour l'automatisation des systèmes électriques – Partie 4: Gestion du système et gestion de projet"
- **Standard Number:** IEC 61850-4
- **Edition:** 2.0
- **Publication Date:** 2011-04
- **Publisher:** IEC (International Electrotechnical Commission)
- **Contact Information:** Email and phone number for customer service inquiries.

**Extracted Text:**
```
IEC 61850-4
Edition 2.0 2011-04
INTERNATIONAL
NORME
INTERNATIONAL
Communication networks and systems for power utility automation –
Part 4: System and project management
Réseaux et systèmes de communication pour l'automatisation des systèmes
électriques –
Partie 4: Gestion du système et gestion de projet
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 2

![Image 1 from page 2](images/iec61850-4{ed2.0}b_page2_img4_edd3df81.png)

**Image Type:** Warning Symbol

**Description:** The image depicts a standard warning symbol, commonly used in technical and safety documentation to indicate a potential hazard or caution. The symbol consists of a yellow triangle with a black border, and inside the triangle is a black exclamation mark, which is universally recognized as a symbol for warning or caution.

**Key Elements:**
- **Shape:** Triangle
- **Color Scheme:** Yellow background with a black border and a black exclamation mark
- **Purpose:** Indicates a warning or caution, signaling the presence of a potential hazard or important information that requires attention.

**Extracted Text:** No text detected

This image is typically used in technical documents to draw attention to areas where caution is necessary, such as in safety instructions, warning labels, or hazard notices. It is a standard symbol used across various industries and technical fields to convey the need for caution or to highlight a potential risk.

---

## Page 3

![Image 1 from page 3](images/iec61850-4{ed2.0}b_page3_img5_41ec8811.png)

**Image Type:** Logo

**Description:** The image is a logo consisting of the letters "IEC" in white, bold, sans-serif font. Below the text, there are three horizontal lines of varying lengths, with a small circle at the end of the longest line. The background is a solid blue color.

**Key Elements:**
- The letters "IEC" are prominently displayed in white.
- The font is bold and sans-serif.
- Below the text, there are three horizontal lines of different lengths.
- The longest line ends with a small circle.

**Extracted Text:** IEC

![Image 2 from page 3](images/iec61850-4{ed2.0}b_page3_img6_69a3415d.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document published by the International Electrotechnical Commission (IEC). It is specifically IEC 61850-4, Edition 2.0, dated April 2011. The document focuses on communication networks and systems for power utility automation, with a particular emphasis on system and project management.

**Key Elements:**
- **Title:** IEC 61850-4
- **Edition:** 2.0
- **Date:** 2011-04
- **Standard Description:** Communication networks and systems for power utility automation – Part 4: System and project management
- **French Title:** Réseaux et systèmes de communication pour l'automatisation des systèmes électriques – Partie 4: Gestion du système et gestion de projet
- **Publisher:** International Electrotechnical Commission (IEC)
- **Price Code:** W
- **ISBN:** 978-2-88912-439-8
- **Copyright Information:** Copyright of IEC, Geneva, Switzerland. All rights reserved.
- **Contact Information:** <EMAIL>, +41 22 919 02 11

**Extracted Text:**
Edition 2.0 2011-04
INTERNATIONAL ELECTROTECHNICAL COMMISSION
COMMISSION ELECTROTECHNIQUE INTERNATIONALE
Communication networks and systems for power utility automation – Part 4: System and project management
Réseaux et systèmes de communication pour l'automatisation des systèmes électriques – Partie 4: Gestion du système et gestion de projet
INTERNATIONAL ELECTROTECHNICAL COMMISSION
COMMISSION ELECTROTECHNIQUE INTERNATIONALE
PRICE CODE CODE PRIX W
ICS 33.200
ISBN 978-2-88912-439-8
Custserv Dial Number: +41 22 919 02 11
Order MeranuNi@p8¢ 11OO 9Battissitd P@aANTUT inlerfatoisle opyright of IEC, Geneva, Switzerland. All rights reserved.
Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

---

## Page 5

![Image 1 from page 5](images/iec61850-4{ed2.0}b_page5_img7_f9ab50ef.png)

**Image Type:** Table of Contents

**Description:** This image is a table of contents for a technical document, likely related to a standard or specification from the International Electrotechnical Commission (IEC). The document appears to be structured around various tests, stages, and tasks related to the acceptance and configuration of a User Acceptance System (UAS). It includes references to Factory Acceptance Test (FAT), Site Acceptance Test (SAT), and other related documentation and testing stages.

**Key Elements:**
- Various sections and figures are listed, each with a page number.
- The document includes annexes (A and B) with examples of announcements and delivery obligations.
- It references figures and tables related to the structure of the UAS, engineering tasks, IED configuration, and quality assurance stages.

**Extracted Text:**
```plaintext
61850-4 © IEC:2011 -3- 7.3.6 Factory Acceptance Test (FAT).......32
7.3.7 Site Acceptance Test (SAT).....32
Annex A (informative) Announcement of discontinuation (example).....34
Annex B (informative) Delivery obligations after discontinuation (example)..35
Bibliography «O.O.O... 22... cece cee ee cece eeeeee eee eeesaeeeeeneaeeeesessaaeecesesaeesessseeseseseeeseesasesersseeeessss OO
Figure 1 — Structure of the UAS and its environment...11
Figure 2 — Structure of UAS and IED parameters...11
Figure 3 — Engineering tasks and their relationship................16
Figure 4 — IED configuration process...18
Figure 5 — Project related documentation of UAS...27
Figure 6 — Two meanings of the system life CyCle..........25
Figure 7 — Stages of quality assurance — Responsibility of manufacturer and system Integrator o.oo...25
Figure 8 — Contents of system test...30
Figure 9 — Contents of type test...31
Figure 10 — Contents of routine test...32
Figure 11 — Testing stages for site acceptance testo...33
Figure A.1 — Announcement conditions................34
Figure B.1 — Periods for delivery obligations...35
```

This table of contents provides a structured overview of the document, detailing the various sections and their corresponding page numbers, which is essential for navigating and referencing the document effectively.

---

## Page 7

![Image 1 from page 7](images/iec61850-4{ed2.0}b_page7_img8_706cc9e7.png)

**Image Type:** OCR Text Image
**Description:** The image is a page from a technical document, specifically page 5 of a publication related to the IEC 61850 series. The document is drafted in accordance with ISO/IEC Directives, Part 2. It provides information about the stability of the publication and the conditions under which it will be updated or replaced.
**Key Elements:**
- Publication title: "Communication networks and systems for power utility automation"
- Publication number: 61850-4 © IEC:2011
- Stability date: Indicated on the IEC website under "http://webstore.iec.ch"
- Conditions for publication update: reconfirmed, withdrawn, replaced by a revised edition, or amended.
**Extracted Text:**
61850-4 © IEC:2011
-5-
This publication has been drafted in accordance with the ISO/IEC Directives, Part 2.
A list of all parts of the IEC 61850 series, under the general title: Communication networks and systems for power utility automation, can be found on the IEC website.
The committee has decided that the contents of this publication will remain unchanged until the stability date indicated on the IEC web site under "http://webstore.iec.ch" in the data related to the specific publication. At this date, the publication will be
- reconfirmed,
+ withdrawn,
+ replaced by a revised edition, or
- amended.

---

## Page 13

![Image 1 from page 13](images/iec61850-4{ed2.0}b_page13_img9_baa946f6.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the structure of a Utility Automation System (UAS) and its environment. It shows the interconnections and communication channels between various Intelligent Electronic Devices (IEDs) and the environment, including human interaction and telecommunication.

**Key Elements:**
- **UAS (Utility Automation System):** The central element of the diagram, representing the entire automation system.
- **IEDs (Intelligent Electronic Devices):** Various IEDs are shown, labeled as IED1, IED2, ..., IEDn, indicating different types of devices within the UAS.
- **Network Control Centre(s):** A central control unit for the network.
- **Human:** Represents human interaction with the system.
- **Telecommunication:** Channels for communication between IEDs and the environment.
- **Sublevel Telecommunication:** Lower-level communication channels.
- **Teleprotection:** Protection mechanisms for the system.
- **Primary Equipment and Auxiliaries:** The physical equipment and auxiliary systems that the UAS controls.
- **Communication Channels:** Arrows indicating the flow of communication between IEDs and the environment.
- **UAS-environment:** The external environment interacting with the UAS.

**Extracted Text:**
61850-4 © IEC:2011 -11- 5 Engineering requirements 5.1 Overview The engineering of a utility automation system is based on a system requirement specification, which defines the scope, functions, boundaries and additional restrictions and requirements for the system, and includes: — the definition of the necessary hardware configuration of the UAS: i.e. the definition of the IEDs and their interfaces with one another and to the environment as shown in Figure 1; — the adaptation of functionality and signal quantities to the specific operational requirements by use of parameters; — the documentation of all specific definitions (i.e. parameter set, terminal connections, etc.). Network control centre(s) Human telecommunication \ /, [es] Celt fe] cation Sublevel_ ~~ [ieD, telecommunication” [eos] 1EDn i “Primary equipment and auxiliaries Teleprotection ~~ I UAS-environment lec 1O4/O2 Figure 1 — Structure of the UAS and its environment As shown in Figure 1, the UAS consists of different IEDs which communicate with each other via communication channels and which execute tasks concerning interactions with the environment of the automation system, such as: — telecommunication environment (TE); e network control centre(s); e subordinate systems; e teleprotection; — the human as a local operator; — process environment (PE) like switchgear, transformer, auxiliaries. Typical IEDs may be: — for the telecommunication environment: ° gateways; * converters; e RTUs (telecommunication side);

This diagram and text together provide a comprehensive overview of the structure and components of a utility automation system, highlighting the interconnections and communication channels necessary for its operation.

---

## Page 14

![Image 1 from page 14](images/iec61850-4{ed2.0}b_page14_img10_574b98de.png)

**Image Type:** Screenshot of a technical document page

**Description:** The image is a screenshot of a page from a technical document, specifically page 12 of a document with the reference number 61850-4 and the copyright notice © IEC:2011. The document appears to be related to the International Electrotechnical Commission (IEC) standards for automation systems, focusing on the categories and types of parameters used in such systems.

**Key Elements:**
- **Page Number and Reference:** Page 12, document reference 61850-4, copyright © IEC:2011.
- **Content Sections:**
  - **5.2 Categories and types of parameters:**
    - **5.2.1 Classification:**
      - Parameters are data that control and support the operation of hardware configuration, software of IEDs, process environment, HMI, and telecommunication environment.
      - The total set of parameters and configuration data of an UAS (Unified Automation System) is termed the UAS-parameter set.
      - Parameters are divided into handling methods and input procedures, with two categories: configuration parameters and operating parameters.
      - Parameters are also divided into origin and function, with three types: system parameters, process parameters, and functional parameters.
    - **Overview of the parameter structure:** Referenced in Figure 2.

**Extracted Text:**
```plaintext
-12- 61850-4 © IEC:2011
- protection relays (teleprotection side);
- for the human machine interface (HMI):
  - gateways;
  - personal computers;
  - workstations;
  - other IEDs with integrated HMIs;
- for the process environment (PE):
  - bay control units;
  - protection relays;
  - RTUs (process side);
  - meters;
  - autonomous controllers (i.e. voltage controllers);
  - transducers;
  - digital switchgear interface;
  - digital power transformer interface;
  - digital VTs and CTs.
5.2 Categories and types of parameters
5.2.1 Classification
Parameters are data, which control and support the operation of:
- hardware configuration (composition of IEDs);
- software of IEDs;
- process environment (primary equipment and auxiliaries);
- HMI with different supporting tools; and
- telecommunication environment
in an automation system and its IEDs in such a way that the operations of the plant and customer specific requirements are fulfilled.
The total set of parameters and configuration data of an UAS is termed the UAS-parameter set.
It consists of the used parts of the parameter sets of all participating IEDs.
With respect to handling methods and input procedure, parameter set contents is divided into two categories:
- configuration parameters;
- operating parameters.
With respect to origin and function, the parameters are divided into types:
- system parameters;
- process parameters;
- functional parameters.
In Figure 2, the overview of the parameter structure is given.
```

This technical analysis provides a clear understanding of the document's content, structure, and key elements, which can be useful for technical documentation and search purposes.

---

## Page 23

![Image 1 from page 23](images/iec61850-4{ed2.0}b_page23_img11_943e3ff6.png)

**Image Type:** Diagram

**Description:** The image is a flowchart or diagram illustrating the documentation process for a UAS (Unmanned Aerial System) project. It outlines the hardware documentation and parameter documentation, detailing the components and relationships between them.

**Key Elements:**
- **Hardware Documentation:**
  - Circuit diagrams
  - Signal connection lists
  - Function diagrams for external schemes
  - Cubicle layouts and wiring/cabling lists
- **Parameter Documentation:**
  - Configuration list
  - Signal lists
  - Parameter lists
  - Communication network addresses
  - Graphical representation of all displays and operation menu sequences
  - Function diagrams or function descriptions
- **Relationships:**
  - Relation by uniform identifiers
  - Follow-up documentation
  - Signal list
  - Parameter lists
  - Function diagrams for internal features
  - Function diagrams for external equipment

**Extracted Text:**

```
61850-4 © IEC:2011 -21-
Hardware documentation Parameter documentation
HW documentation tool Parameter documentation tool
Follow-up documentation Relation by Signal list Parameter lists
Signal connection lists documentationI uniform identifiers ~ terminal
Graphical displays connection ~ cable list Function diagrams for Function diagrams for internal features
external equipment lec 108/02
Figure 5 — Project related documentation of UAS

The hardware documentation consists of:
- circuit diagrams for the link between the UAS components and for their connection with the PE;
- signal connection lists;
- function diagrams for external schemes;
- cubicle layouts and wiring / cabling lists.

The system and IED parameter documentation consists of:
- the configuration list;
- signal lists;
- parameter lists;
- communication network addresses;
- graphical representation of all displays and operation menu sequences;
- function diagrams or function descriptions.

The requirement on the engineering tools is that the documentation should be generated as:
a) hardware documentation with the help of the input values of the planning tool on a CAD (or similar) system;
b) parameter documentation using the IED parameter set from the parameterization tool;
c) system configuration documentation using the system parameter set from the system configuration tool as needed.

The interfaces between hardware and parameter documentation are the signal lists, which should have uniform and unique signal identifiers in both documents, preferably based on the semantically standardized identifications defined in other parts of this standard.

The generation of documentation, based on the inputs of the planning and parameterization tool, should ensure the consistency between documentation on one hand and the project check lists, the IED parameter set and process data lists on the other hand.
```

This diagram and text provide a structured approach to documenting a UAS project, ensuring consistency and standardization across hardware and parameter documentation.

---

## Page 26

![Image 1 from page 26](images/iec61850-4{ed2.0}b_page26_img12_d4c7ed99.png)

**Image Type:** Page from a Technical Manual

**Description:** This image is a page from a technical manual, specifically from a section discussing the documentation and support provided by a system integrator for a Unmanned Aerial System (UAS) project. The page includes a list of standard documentation items, details about the system integrator's support, and an overview of the system life cycle, including the requirements for product versions.

**Key Elements:**
- **Standard Documentation Items:** Includes instruction and maintenance manual, system concept description, description of functions, operating instructions, instruction for service programs, fault detection and maintenance instructions, and user manual for engineering tools.
- **System Integrator's Support:** The system integrator is responsible for offering engineering tools for system maintenance and providing customer training. They should also support the process with consultative services, training, and regular updates on the system and engineering tools.
- **System Life Cycle:** Discusses the differences in life cycles between the manufacturer's and the customer's perspectives, with the manufacturer's life cycle covering the period from production start to discontinuation, and the customer's life cycle covering the period from the first system installation to the decommissioning of the latest system installation.

**Extracted Text:**

```
—24- 61850-4 © IEC:2011
— instruction and maintenance manual;
— system concept description;
— description of functions;
— operating instructions;
— instruction for service programs;
— fault detection and maintenance instruction;
— user manual for the engineering tools.
The standard documentation should complete the project specific documentation for each installed system.

5.8 System integrator's support
In most cases, the engineering tasks are included in the system integrator’s offer for the UAS project.
In all cases, however, the system integrator has to offer the engineering tools needed for system maintenance and appropriate customer training for the use of these tools so that the customer may maintain and expand the system installation.
The system integrator should support this process with consultative services, training and regular information regarding updates and extended functionality of the system installation and the engineering tools.

6 System life cycle
6.1 Requirements of product versions
The life cycles of an UAS and its IEDs are subject to differences of the manufacturer's and the customer's point of view, as shown in Figure 6:
— the manufacturer's product life cycle contains the period between the start of production and the discontinuation of the UAS product family;
— the customer's system life cycle contains the period between the site commissioning of the first system installation, often based on several UAS product families, and the decommissioning of the latest system installation.
The system installation may be carried out by a system integrator who is different from the product manufacturer.
```

This page is part of a larger technical document, likely related to the design, installation, and maintenance of UAS systems, with a focus on the role of the system integrator and the importance of comprehensive documentation.

---

## Page 27

![Image 1 from page 27](images/iec61850-4{ed2.0}b_page27_img13_c5a85e85.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the life cycle of a system, specifically focusing on the manufacturer's life cycle and the customer's life cycle. It includes a timeline that spans from the start of production to discontinuation, highlighting various stages such as contract, site commissioning, decommissioning, and maintenance. The diagram also outlines the relationship between the manufacturer's life cycle and the customer's life cycle, emphasizing the need for changes and extensions during the manufacturer's life cycle to meet functional improvements, technology changes, and the correction of recognized problems. These changes lead to updated IED (Intelligent Electronic Device) versions of hardware, software, and supporting tools.

**Key Elements:**
- **Timeline:** Represents the sequence of events from start of production to discontinuation.
- **Stages:** Includes contract, site commissioning, decommissioning, and maintenance.
- **Life Cycles:** Manufacturer's life cycle and customer's life cycle.
- **IED Versions:** Indicates the need for updated versions of IEDs due to changes and extensions.
- **Compatibility List:** The configuration compatibility list of the UAS-product family.
- **Version Information:** Details on how version information is available for hardware and software.
- **Functionality Changes:** The impact of changes in functionality on the configuration compatibility list.

**Extracted Text:**

```
61850-4 © IEC:2011 —25—

Start of production
V1
V2
Vm
Discontinuation
Change requirements
V = Version
Manufacturer's life cycle
Contract Site commissioning Decommissioning
Project 1 Project 1
SAS 1
Contract Site commissioning Decommissioning
Project n Project n
SAS n
Update Extension Maintenance
Customer's life cycle
IEC 109/02

Figure 6 — Two meanings of the system life cycle

During the manufacturer's life cycle of the UAS and its IEDs, a number of changes and extensions are required for various reasons:
- functional improvements and extensions;
- technology changes in the hardware;
- correction of recognized problems.

These changes lead to updated IED versions of hardware, software and supporting tools.

A new version of an IED can produce different impacts:
- it influences changes needed to the configuration compatibility list of the UAS-product family, in that the new version of the IED requires version changes in other IEDs or in the engineering tool, for example to fulfil new overreaching functions. A system test together with relevant IEDs is necessary and leads to a new system configuration list;
- it is independent of other IEDs and compatible with the current configuration list. The system test of the IED has to check the compatibility with the other IEDs in the system. Only the version of the IED will be changed. The configuration list version of the system has to be modified.

The manufacturer is obliged to provide identification of the IED versions:
- in the case of IED software or the supporting tools software, the version information is available in a self identifying manner (for example, on display or PC);
- for the hardware, the version information is available at the board and at the device levels;
- if the functionality has changed or a function has been removed, a new configuration compatibility list shall be distributed.

The co-ordination of the manufacturer's and the customer's life cycles requires that new versions of the IEDs with identical model numbers shall comply with the following rules.
a) The hardware shall be compatible. All interfaces must perform the same function in the same places. The sizes of the boards and the devices must be identical.
```

This technical analysis provides a clear understanding of the system life cycle, the changes and extensions required during the manufacturer's life cycle, and the implications of these changes on the IED versions and configuration compatibility lists.

---

## Page 29

![Image 1 from page 29](images/iec61850-4{ed2.0}b_page29_img14_e0157814.png)

**Image Type:** Diagram

**Description:** The image is a flowchart that illustrates the stages of quality assurance as a responsibility of the manufacturer and system integrator. It outlines the process from development to decommissioning, highlighting the roles and responsibilities of the manufacturer and system integrator in ensuring quality at each stage.

**Key Elements:**
- **Development:** The initial stage where the product is developed.
- **Prototype series:** Testing and validation of the prototype.
- **Type test:** Testing of the product to ensure it meets the required specifications.
- **System test:** Testing of the system to ensure it functions correctly.
- **Verification:** Ensuring the product meets customer requirements.
- **Customer 1, Customer x, Customer n:** The customer at different stages of the process.
- **Project 1, Project x, Project n:** Projects associated with the product.
- **Market approval:** Approval from the market.
- **Delivery and putting online:** Delivery of the product to the customer.
- **Routine test of products:** Testing of individual products.
- **Routine test of system equipment:** Testing of the system equipment.
- **Site commissioning:** Commissioning of the system at the site.
- **Site acceptance test:** Acceptance testing at the site.
- **Trial operation:** Testing the system in a trial operation.
- **Warranty:** Providing warranty services.
- **Service maintenance support:** Providing maintenance and support services.
- **Decommissioning:** The end of the product's lifecycle.

**Extracted Text:**
61850-4 © IEC:2011 -27- 7 Quality assurance 7.1 Division of responsibility 7.1.1 General The quality assurance of a system is a common task of the system integrator/manufacturer and of the customer with different areas of responsibility. If two or more parties are involved, then the responsibilities of each party shall be defined at the time of procurement. 7.1.2 Responsibility of the manufacturer and system integrator ******* Quality system The manufacturer and the system integrator should establish and maintain a quality system in accordance with ISO 9001. The stages of quality assurance as a responsibility of the manufacturer and system integrator are shown in Figure 7. Market approval Delivery and putting online Development Project 1 (EN ISO 9001) Customer 1 3 eaten Sette of system commissioning 5 equipment; = cue test ° Test maintenance support lec 11002 Figure 7 — Stages of quality assurance — Responsibility of manufacturer and system integrator ******* Test responsibilities The manufacturer is responsible for the correct handling of type tests and system tests of his individual products. Type tests and system tests are preconditions for starting the regular delivery. All IEDs have to pass device specific routine tests defined by the manufacturer to ensure quality before the products are handed over for delivery. Customer specific verifications and approvals may be required according to the customer's philosophy and shall be negotiated between the system integrator and the customer. These might be done by the customer at product level as well as at system level.

**Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved. This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

---

## Page 32

![Image 1 from page 32](images/iec61850-4{ed2.0}b_page32_img15_8d3ece45.png)

**Image Type:** Schematic/Flowchart

**Description:** The image is a technical schematic that outlines the requirements and procedures for conducting communication tests and system tests on a communication test equipment. It includes a flowchart that illustrates the components and functions of the system test, as well as the basic test requirements and the scope of the tests.

**Key Elements:**
- **Communication Test Equipment:** Used for testing communication channels, including internal links and telecommunication.
- **System Test Components:**
  - Function of each IED (Intelligent Electronic Device) with various parameterization.
  - Compatibility with other IEDs using various configuration and parameterization.
  - Performance of the overall system.
- **Basic Test Requirements:**
  - The manufacturer should provide a test concept covering all activities from prototype functional tests to final type and system tests.
  - The scope and object of tests, test procedures, and passing criteria must be specified.
  - Tests should be documented for reproducibility.
  - Tests should be performed by an internal or external qualified organization.
- **System Test Purpose:** Proves correct functionality and performance of each IED under different application conditions and in cooperation with other IEDs of the overall UAS product families.

**Extracted Text:**

```
-3O- 61850-4 © IEC:2011
7.2.4 Communication test equipment
This test equipment is used for performing tests at all communication channels for:
- internal links of the system;
- telecommunication.

The communication test system should be a convenient and efficient tool which enables the performance of the following functions at all required levels (network control centre, substation, bay and process level):
- simulation of a server, simulation of a client, monitoring of the data traffic;
- quality analysis of the data traffic (for example, the quality of electrical signals, time breaks, etc.).

7.3 Classification of quality tests
7.3.1 Basic test requirements
The manufacturer should provide a test concept that covers all activities beginning with prototype functional tests in the development state to the final type and system tests. The scope and object of tests, the test procedures and the passing criteria must be specified.

All tests shall be documented in such a way that the results are reproducible, if required.

All tests should be performed by an internal part of the manufacturer’s organization that is qualified for performing the tests and has the organizational independence to state whether a product has passed the tests or not, or by an independent external organisation, qualified for the tests by a third party.

7.3.2 System test
The system test is the proof of correct functionality and the performance of each IED under different application conditions (different configuration and parameters) and in co-operation with other IEDs of the overall UAS product families including all tools, for example for parameterization, diagnostic (see Figure 8).

Function of each IED with various parameterization
Compatibility with other IEDs using various configuration and parameterization
Performance of the overall system

Figure 8 – Contents of system test
A successfully finished system test is the precondition for starting the type test.
```

This technical analysis provides a clear understanding of the document's content and structure, which is essential for technical documentation and search purposes.

---

## Page 33

![Image 1 from page 33](images/iec61850-4{ed2.0}b_page33_img16_4a5b89d7.png)

**Image Type:** Diagram

**Description:** The image is a flowchart or diagram illustrating the process of a type test for a newly designed product. It outlines the steps and criteria involved in ensuring the product's fitness for use, including mechanical withstandability, electromagnetic compatibility, climatic influences, and functional correctness and completeness.

**Key Elements:**
- **Series Product:** The starting point of the process.
- **Type Test:** The central process where the product is verified against specified technical data.
- **Approved Firmware (System Test):** The output of the type test, which is further verified for system functionality.
- **Mechanical:** One of the criteria for the type test.
- **Environmental + Climatic:** Another criterion for the type test.
- **EMC (Electromagnetic Compatibility):** Another criterion for the type test.
- **Functional Correctness:** The final criterion for the type test, ensuring the product functions correctly and completely.

**Extracted Text:**
61850-4 © IEC:2011 -31- 7.3.3 Type test
The “fitness for use” of a newly designed product shall be proven by a type test. The type test shall be performed using samples from the manufacturing process. The type test is the verification of the product against the technical data (see Figure 9) which are specified, such as:
- mechanical withstandability;
- electromagnetic compatibility;
- climatic influences;
- functional correctness and completeness.

The type test shall be carried out by the use of system tested software. The type test shall be passed before regular production delivery can be started.

Figure 9 – Contents of type test
The type test shall be carried out by the use of system tested software. The type test shall be passed before regular production delivery can be started.

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved. This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

---

## Page 34

![Image 1 from page 34](images/iec61850-4{ed2.0}b_page34_img17_bb30e78f.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that outlines the contents of routine tests, conformance tests, factory acceptance test (FAT), and site acceptance test (SAT) for a system. It includes a flowchart and textual descriptions of each test type and their respective components.

**Key Elements:**
- **Routine Test:** Consists of special hardware and functionality tests.
- **Function Test:** One of the components of the routine test.
- **Insulation Test:** Another component of the routine test.
- **Burn In:** Another component of the routine test.
- **Conformance Test:** Performed on the communication channels of IEDs and verifies the communication procedure.
- **Factory Acceptance Test (FAT):** Validates and verifies a system and its functions from the customer's point of view. It is optional and requires discussion and agreement between the system integrator and the customer.
- **Site Acceptance Test (SAT):** Ensures the correct installation and connection of all system components. It is carried out on the completely installed equipment in individual steps.

**Extracted Text:**

```
-32- 61850-4 © IEC:2011
7.3.4 Routine test
The routine test consists of special hardware and functionality tests as shown in Figure 10.
Figure 10 — Contents of routine test
The routine tests should be carried out for each product before leaving the manufacturer.
7.3.5 Conformance test
The conformance tests are performed on the communication channels of IEDs and include the verification of the communication procedure in accordance with the standard or its parts (see IEC 61850-10).
7.3.6 Factory Acceptance Test (FAT)
The factory acceptance test (FAT) serves to validate and verify a system and its functions from the customer's point of view. The factory acceptance test is optional. The scope and object of the FAT have to be discussed and agreed between system integrator and customer and should be documented in checklists. The checklists are part of the contract. The result of the FAT should be documented and signed by both the system integrator and the customer. The focus of a FAT is to test typical solutions and their behaviour in normal and abnormal situations. A process simulation allows making also tests for abnormal process conditions and process failure situations.
7.3.7 Site Acceptance Test (SAT)
The main purpose of the acceptance test of the system on site (SAT) is to show the correct installation and connection of all system components. It shall be carried out on the completely installed equipment in individual steps (see Figure 11).
```

This technical analysis provides a clear understanding of the structure and content of the image, which is useful for technical documentation and search.

---

## Page 35

![Image 1 from page 35](images/iec61850-4{ed2.0}b_page35_img18_6d2cfc5f.png)

**Image Type:** Diagram

**Description:** The image is a schematic diagram illustrating the testing stages for a site acceptance test (SAT) in a control system. It outlines the hierarchical structure of the control levels and the sequence of testing stages that must be followed to ensure the system's functionality and information exchange.

**Key Elements:**
- **Process:** The starting point of the control system.
- **Bay control level:** The second level in the control hierarchy.
- **Station control level:** The third level in the control hierarchy.
- **Network control centre(s):** The top level in the control hierarchy.
- **Testing stages:** The sequence of testing phases that must be completed at each level to ensure the system's functionality and information exchange.

**Extracted Text:**
```
61850-4 © IEC:2011 —33—
Bay control level
Station control level
Network control level
Testing stages
Process
Bay control level - station control level
Station control level - network control centre(s)
Testing stage: process - network control centre(s)
Testing stages
Testing stages
Testing stages
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s)
Testing stage: process - network control centre(s

---

## Page 36

![Image 1 from page 36](images/iec61850-4{ed2.0}b_page36_img19_6f89fd7b.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that illustrates the timeline for the announcement and discontinuation of a product, along with the conditions under which a subsequent product can be introduced. The diagram is divided into two main parts, each representing different scenarios of product discontinuation and the introduction of a new product.

**Key Elements:**

- **Announcement:** The point at which the discontinuation of the product is announced.
- **Discontinuation:** The point at which the product is no longer available.
- **2 years:** The period during which the product is available after the announcement.
- **1 year:** The period during which the product is available after the announcement, but with a shorter timeline.
- **Subsequent product:** The new product that can be introduced after the discontinuation of the original product.
- **Start of production:** The point at which the subsequent product begins production.
- **Functionally compatible product:** A product that can replace the original product without requiring significant changes in the user's setup or operations.

**Extracted Text:**

```
-34- 61850-4 © IEC:2011
Annex A (informative)
Announcement of discontinuation (example)
Announcement Discontinuation 2 years IEC 115/02
Figure A.1.a — Without subsequent functionally compatible product
Announcement Discontinuation 1 year IEC 116/02
Figure A.1.b - Functionally compatible product follows
Figure A.1 — Announcement conditions
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

This diagram is part of a larger technical document, likely related to product lifecycle management or product discontinuation policies, as indicated by the copyright notice and the reference to IEC (International Electrotechnical Commission).

---

## Page 37

![Image 1 from page 37](images/iec61850-4{ed2.0}b_page37_img20_42a55c5e.png)

**Image Type:** Diagram

**Description:** The image is a horizontal timeline diagram that illustrates the timeline for the discontinuation of various technical services and products. The timeline is marked in years, ranging from 5 to 20, and includes horizontal bars representing the duration of each service or product's availability.

**Key Elements:**
- **Discontinuation:** Indicates the end of service or product availability.
- **Special agreement with minimum annual order:** A service or product that requires a minimum annual order to continue.
- **Compatible products for extension:** Products that can be extended or continued.
- **Spare parts and repair service:** Services related to spare parts and repair.
- **Firmware and software versions:** Updates or versions of firmware and software.
- **Adaptive interfaces:** Interfaces that adapt to new requirements or technologies.

**Extracted Text:**
```
Discontinuation
Special agreement with minimum annual order
Compatible products for extension
Spare parts and repair service
Firmware and software versions
Adaptive interfaces
5 10 15 20 years
IEC 117/02
```

This diagram is useful for technical documentation and search as it clearly outlines the timeline for the discontinuation of various technical services and products, which can be crucial for planning and decision-making in the technical field.

![Image 2 from page 37](images/iec61850-4{ed2.0}b_page37_img21_92907ada.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the periods for delivery obligations after discontinuation of a product or service. It is part of Annex B of a document, specifically related to delivery obligations after discontinuation, as indicated by the OCR text.

**Key Elements:**
- **Discontinuation:** The point at which the product or service is no longer available.
- **Special agreement with minimum annual order:** A period where a special agreement is in place, requiring a minimum annual order.
- **Compatible products for extension:** A period where compatible products are available for extension.
- **Spare parts and repair service:** A period where spare parts and repair services are provided.
- **Firmware and software versions:** A period where firmware and software versions are supported.
- **Adaptive interfaces:** A period where adaptive interfaces are available.
- **Time Periods:** The diagram shows the time periods in years, ranging from 5 to 20 years.

**Extracted Text:**
```
61850-4 © IEC:2011
—35—
Annex B (informative)
Delivery obligations after discontinuation (example)
Discontinuation
Special agreement with minimum annual order
Compatible products for extension
Spare parts and repair service
Firmware and software versions
Adaptive interfaces
5 10 15 20 years
Figure B.1 — Periods for delivery obligations
```

This diagram is useful for technical documentation and search as it clearly outlines the different periods of delivery obligations after a product or service is discontinued, providing a structured view of the support and services available during these periods.

---

## Page 38

![Image 1 from page 38](images/iec61850-4{ed2.0}b_page38_img22_4f175ab9.png)

**Image Type:** Bibliography Page
**Description:** This image is a page from a technical document, specifically a bibliography section. It lists references to standards and guidelines related to communication networks and quality management systems. The page number is 36, and it is part of a document with the reference number 61850-4, copyrighted by IEC:2011.

**Key Elements:**
- Page Number: 36
- Reference Number: 61850-4
- Copyright: © IEC:2011
- Title: Bibliography
- Standards Referenced:
  - IEC 61850-10: Communication networks and systems in substations – Part 10: Conformance testing
  - ISO 9001:2008: Quality management systems – Requirements

**Extracted Text:**
```
– 36 –
61850-4 © IEC:2011

Bibliography

IEC 61850-10, Communication networks and systems in substations – Part 10: Conformance testing
ISO 9001:2008, Quality management systems – Requirements
```

---

## Page 39

![Image 1 from page 39](images/iec61850-4{ed2.0}b_page39_img23_48a278d8.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank or placeholder page, possibly from a technical manual or document. It is devoid of any technical content, illustrations, or text, suggesting it might be a page intended for a diagram or figure that has not been inserted or is missing.

**Key Elements:** None

**Extracted Text:** None detected

The image does not contain any technical elements or text that can be analyzed or described further. It seems to be a placeholder or an empty page within a technical document.

---

## Page 41

![Image 1 from page 41](images/iec61850-4{ed2.0}b_page41_img24_4fa1deef.png)

**Image Type:** Table of Contents

**Description:** This image is a table of contents for a technical document, likely related to a standard or guideline from the International Electrotechnical Commission (IEC) as indicated by the copyright notice at the top. The document appears to be structured around various tests, procedures, and annexes related to the acceptance and quality assurance of an Unmanned Aerial System (UAS) and its associated equipment.

**Key Elements:**
- Various sections and figures are listed, each with a description and page number.
- The document includes tests such as "Essai de réception usine (ERU)" and "Essai de réception sur site (ERS)".
- Annexes A and B provide additional information on the end-of-production announcement and delivery obligations.
- Figures are referenced for various aspects of the UAS and its environment, parameters, study tasks, configuration processes, project documentation, life cycle significances, quality assurance steps, system test content, type test content, individual series test content, and site reception test steps.
- The document also includes conditions for announcement and delivery obligations.

**Extracted Text:**
```plaintext
61850-4 © CEI:2011 - 39 - 
7.3.6 Essai de réception usine (ERU)...........70
7.3.7 Essai de réception sur site (ERS)........70
Annexe A (informative) Annonce de l’arrêt de la fabrication (exemple)...........72
Annexe B (informative) Obligations de livraison après l’arrêt de la fabrication (exemple)...........72
Bibliographie...........74
Figure 1 – Structure de l’UAS et de son environnement...........48
Figure 2 – Structure des paramètres UAS et IED...........50
Figure 3 – Les tâches d’étude et leurs relations...........54
Figure 4 – Processus de configuration de l’IED...........56
Figure 5 – Documentation de projet de l’UAS...........58
Figure 6 – Deux significations du cycle de vie du système...........62
Figure 7 – Etapes de l’assurance qualité – Responsabilité du constructeur et de l’intégrateur système...........65
Figure 8 – Contenu de l’essai du système...........68
Figure 9 – Contenu de l’essai de type...........69
Figure 10 – Contenu de l’essai individuel de série...........69
Figure 11 – Les étapes d’essai de l’essai de réception sur site...........70
Figure A.1 – Conditions d’annonce...........72
Figure B.1 – Périodes des obligations de livraison...........73
```

This table of contents provides a structured overview of the document's content, which is essential for navigating and understanding the document's structure and the topics it covers.

---

## Page 43

![Image 1 from page 43](images/iec61850-4{ed2.0}b_page43_img25_af41bcc7.png)

**Image Type:** Document Page
**Description:** This image is a page from a technical document, specifically from a standard or normative publication. The document appears to be related to the International Electrotechnical Commission (IEC) and is part of the CEI series, as indicated by the copyright and reference to CEI:2011. The page number is 41, and it includes a reference to the FDIS (Final Draft International Standard) and RVD (Revision Vote Document) numbers, which are part of the IEC's standardization process.

**Key Elements:**
- **Document Type:** Standard or Normative Publication
- **Standard Number:** 61850-4
- **Year:** 2011
- **Reference to FDIS and RVD Numbers:** 57/1103/FDIS and 57/1122/RVD
- **Approval Process:** The document mentions the approval process through a vote, as indicated by the reference to the RVD number.
- **Publication Directive:** The document was written according to ISO/CEI Part 2 Directives.
- **Publication Availability:** A list of all parts of the CEI 61850 series, titled "Réseaux et systèmes de communication pour l'automatisation des systèmes électriques," is available on the CEI website.
- **Publication Stability:** The content of the publication will not be modified until the date of stability, which can be found on the CEI website.

**Extracted Text:**
```
61850-4 © CEI:2011 - 41 - Le texte de la présente Norme est issu des documents suivants:
57/1103/FDIS 57/1122/RVD
Le rapport de vote indiqué dans le tableau ci-dessus donne toute information sur le vote ayant abouti à l'approbation de la présente Norme.
La présente publication a été rédigée selon les Directives ISO/CEI, Partie 2.
Une liste de toutes les parties de la série CEI 61850, publiée sous le titre général Réseaux et systèmes de communication pour l'automatisation des systèmes électriques est disponible sur le site web de la CEI.
Le comité a décidé que le contenu de cette publication ne sera pas modifié avant la date de stabilité indiquée sur le site web de la CEI sous "http://webstore.iec.ch" dans les données relatives à la publication recherchée. A cette date, la publication sera
* reconduite,
* supprimée,
* remplacée par une édition révisée, ou
* amendée.
```

This document provides important information about the standardization process, the availability of the publication, and the future status of the document. It is crucial for anyone involved in the field of electrical systems automation to understand the stability and revision process of this standard.

---

## Page 49

![Image 1 from page 49](images/iec61850-4{ed2.0}b_page49_img26_49590788.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically page 47, which appears to be part of a larger report or manual. The document is structured with sections and subsections, including an abbreviation list and a section detailing requirements related to the study of a utility automation system (UAS) for electricity companies. The document is in French and includes definitions for various abbreviations and terms related to automation systems.

**Key Elements:**
- **Abbreviations Section:** Lists abbreviations such as ASDU, CD ROM, CAO, TC, ERU, IHM, IED, PE, RTU, SAS, ERS, SCADA, ET, UAS, and TT, along with their definitions.
- **Requirements Section (5 Exigences liées à l'étude):** Discusses the study of a UAS for electricity companies, focusing on the definition of the system's requirements, including the material configuration, adaptation of functionalities and signal sizes to operational needs, and documentation of specific definitions.
- **Definitions (at the bottom):** Provides definitions for terms such as ASDU, IED, PE, RTU, SAS, SCADA, and UAS.

**Extracted Text:**

61850-4 © CEI:2011 -47- 4 Abréviations ASDU5 unité de données de service application CD ROM compact disc read only memory CAO conception assistée par ordinateur TC transformateur de courant ERU essai de réception usine IHM interface homme-machine IED& dispositif électronique intelligent PE? environnement de procédé RTU8s terminal a distance SAS9 systéme d'automatisation de poste ERS essai de réception sur site SCADA1O systéme de supervision, contréle et acquisition de données ET environnement de télécommunication UAS11 systéme d'automatisation pour les compagnies d'électricité TT transformateur de tension 5 Exigences liées a I’étude 5.1. Vue d’ensemble L'étude d'un systéme d'automatisation pour les compagnies d'électricité est basée sur une spécification des exigences du systéme, qui définit le champ d'application, les fonctions, les limites et les autres restrictions et exigences pour le systeme. L'étude comprend: - la définition de la configuration matérielle nécessaire de I'UAS: définition de tous les IED et des différentes interfaces avec l'environnement, comme indiqué dans la Figure 1; - l'adaptation des fonctionnalités et des grandeurs de signaux aux besoins opérationnels spécifiques, a l'aide de paramétres; — la documentation de toutes les définitions spécifiques (par exemple jeu de paramétres, raccordements, etc.). 5 ASDU = Application Service Data Unit. 6 ED = Intelligent Electronic Device. 7 PE = Process Environment. 8 RTU = Remote Terminal Unit. 9 SAS = Substation Automation System. 10 SCADA = Supervisory Control And Data Acquisition. 11 UAS = Utility Automation System.

**Technical Analysis:**
This document appears to be part of a technical report or manual related to the design and implementation of automation systems for electricity companies. It includes an abbreviation list, definitions, and a detailed section on the requirements for a utility automation system. The document is structured to provide a comprehensive overview of the system's configuration, functionality, and operational requirements, with a focus on adapting the system to specific operational needs and documenting all relevant definitions. The use of abbreviations and definitions suggests that the document is intended for technical professionals or engineers working in the field of automation systems.

---

## Page 50

![Image 1 from page 50](images/iec61850-4{ed2.0}b_page50_img27_97dba0cb.png)

**Image Type:** Diagram

**Description:** The image is a schematic diagram illustrating the structure of an Unattended Automation System (UAS) and its environment. It depicts the communication and interaction between various Intelligent Electronic Devices (IEDs) and their connections to different environments such as telecommunication, network control centers, sub-systems, teleprotection, and human operators. The diagram also highlights the interaction with the primary and auxiliary equipment and the process environment.

**Key Elements:**
- **UAS (Unattended Automation System):** Central element of the diagram, representing the automation system.
- **IEDs (Intelligent Electronic Devices):** Devices that communicate with each other and interact with the UAS environment.
- **Communication:** The channels through which IEDs communicate with each other and with the UAS environment.
- **Environments:**
  - **Télécommunication (Telecommunication):** Environment related to telecommunication.
  - **Centre(s) de conduite du Réseau (Network Control Center):** Central control centers for the network.
  - **Sous-niveaux (Sub-systems):** Lower-level systems or sub-systems.
  - **Téléprotection (Teleprotection):** Protection systems for the network.
  - **Humain (Human):** Human interaction, such as local operators.
  - **Appareillage primaire et auxiliaires (Primary and Auxiliary Equipment):** Primary and auxiliary equipment related to the automation system.
  - **Environnement UAS (UAS Environment):** The environment surrounding the UAS.
- **Process Environment (EP):** Environment related to process control, including commutation apparatus, transformers, and auxiliary equipment.
- **Interface Homme-Machine (IHM) (Human-Machine Interface):** Interaction points between humans and the automation system, including workstations, PCs, and other integrated IEDs.

**Extracted Text:**
```plaintext
-48- 61850-4 © CEI:2011
Télécommunication
Centre(s) de conduite du Réseau
Humain
Sous-niveaux
télécommunication
Appareillage primaire et auxiliaires
Téléprotection
Environnement UAS
UAS

Figure 1 — Structure de l’UAS et de son environnement
Comme indiqué dans la Figure 1, l'UAS est constitué de plusieurs IED qui communiquent entre eux à travers des canaux de communication et qui exécutent des tâches en interaction avec l'environnement du système d'automatisation, par exemple:
- l'environnement télécommunication (ET);
- le ou les centres de conduite du réseau;
- les systèmes subordonnés;
- la téléprotection;
- l'être humain en tant qu'opérateur local;
- l'environnement de procédé (EP) tel que l'appareillage de commutation, le transformateur de mesure, les auxiliaires.
Les IED sont typiquement:
- pour l'environnement télécommunication:
  - les passerelles;
  - les convertisseurs;
  - les RTU (côté télécommunication);
  - les relais de protection (côté téléprotection);
- pour l'interface homme-machine (IHM):
  - les passerelles;
  - les PC;
  - les postes de travail;
  - les autres IED avec IHM intégré;
- pour l'environnement de procédé (PE):
  - les unités de commande de cellule;
  - les relais de protection;
  - les RTU (côté procédé);
  - les compteurs;
```

This comprehensive description and analysis should be useful for technical documentation and search purposes.

---

## Page 51

![Image 1 from page 51](images/iec61850-4{ed2.0}b_page51_img28_980421b8.png)

**Image Type:** Screenshot of a technical document page

**Description:** The image is a screenshot of a page from a technical document, specifically page 49 of a document with the reference number 61850-4 © CEI:2011. The content is in French and discusses categories and types of parameters in the context of an automation system with Intelligent Electronic Devices (IEDs). It explains the classification of parameters, their categories, and types, and how they relate to the configuration and operation of the system.

**Key Elements:**
- **Categories and Types of Parameters:** The document discusses the classification of parameters into categories such as configuration, operation, system, procedure, and functional parameters.
- **Parameters and Their Roles:** Parameters are described as data that control and condition the operation of the system, including material configuration, software of IEDs, process environment, Human Machine Interface (HMI) with support tools, and communication environment.
- **Automation System with IEDs:** The document explains that the set of parameters and configuration data of an Unattended Automation System (UAS) is called the UAS parameter set. It consists of all the parts used in the parameter sets of all participating IEDs.
- **Methods of Processing and Data Entry:** The content is divided into two categories: configuration parameters and operation parameters.
- **Parameter Types by Origin and Function:** Parameters are categorized by their origin and the functions they relate to, such as system parameters, procedure parameters, and functional parameters.

**Extracted Text:**
```plaintext
61850-4 © CEI:2011 -49-

- les contrôleurs autonomes (c'est-à-dire les contrôleurs de tension);
- les transducteurs;
- les interfaces d'appareillages de commutation numériques;
- les interfaces de transformateurs de puissance numériques;
- les TT et les TC numériques.

5.2. Catégories et types de paramètres

5.2.1 Classification

Les paramètres sont des données qui contrôlent et conditionnent le fonctionnement:
- de la configuration matérielle (décomposition en IED);
- des logiciels des IED;
- de l'environnement du procédé (appareillage primaire et auxiliaires);
- de l'IHM avec différents outils de support; et
- de l'environnement de télécommunication

dans un système d'automatisation avec ses IED, de façon à réaliser l'exploitation du site et à répondre aux besoins spécifiques du client.

On appelle l'ensemble des paramètres et données de configuration d'un UAS le jeu de paramètres UAS. Il est constitué de toutes les parties utilisées des jeux de paramètres de tous les IED participants.

Du point de vue des méthodes de traitement et de la procédure de saisie, le contenu du jeu de paramètres est réparti en deux catégories:
- les paramètres de configuration;
- les paramètres d'exploitation.

Du point de vue de leur origine et des fonctions auxquelles ils se rapportent, les paramètres se répartissent en plusieurs types:
- les paramètres système;
- les paramètres procédé;
- les paramètres fonctionnels.

La Figure 2 donne une vue synoptique de la structure des paramètres.
```

This technical analysis provides a clear understanding of the content and structure of the document, which is crucial for technical documentation and search purposes.

---

## Page 64

![Image 1 from page 64](images/iec61850-4{ed2.0}b_page64_img29_2ac9472e.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that illustrates the lifecycle of a system, specifically focusing on the lifecycle of an Unmanned Aerial System (UAS) and its Integrated Electronic Devices (IEDs). It highlights the differences in lifecycle interpretation between the manufacturer and the client, and it includes a timeline of events and phases related to the lifecycle.

**Key Elements:**

- **Lifecycle Phases:** The diagram outlines the lifecycle phases for both the manufacturer and the client, including:
  - **Manufacturer's Lifecycle:**
    - Start of production
    - End of production
    - Version updates (V1, V2, Vm)
    - Customer requests for modifications
  - **Client's Lifecycle:**
    - Site installation of the first system
    - Site installation of subsequent systems
    - System maintenance and upgrades
    - System retirement
  - **Timeline:** The timeline includes key events such as contract, site installation, maintenance, and retirement.

- **Cycle of Life for the Manufacturer:** This cycle includes the period from the start of production to the end of production for the UAS and its IEDs.

- **Cycle of Life for the Client:** This cycle includes the period from the site installation of the first system to the retirement of the last system.

- **Figure 6:** This figure visually represents the two different interpretations of the lifecycle for the manufacturer and the client.

- **Textual Information:** The text provides additional context and details about the lifecycle, including the need for maintenance, training, and regular information updates from the system integrator.

**Extracted Text:**

```plaintext
-62- 61850-4 © CEI:2011
5.8 Support de l'intégrateur système
Dans la majorité des cas, les tâches d'étude font partie de l'offre de l'intégrateur système pour le projet UAS.
Toutefois, dans tous les cas, l'intégrateur système doit proposer les outils d'étude nécessaires à la maintenance du système et à la formation du client sur l'utilisation des outils afin que le client puisse maintenir et étendre l'installation du système.
Il convient que l'intégrateur système supporte ce processus en fournissant des services de conseil, de formation et d'information régulière sur les mises à niveau et les fonctionnalités nouvelles de l'installation du système et des outils d'étude.

6 Cycle de vie du système
6.1 Exigences liées aux versions des produits
Les cycles de vie d'un UAS et de ses IED ont une signification différente pour le constructeur et pour le client, comme indiqué dans la Figure 6:
- du point de vue du constructeur, le cycle de vie du produit comprend la période entre le démarrage de la production et l'arrêt de fabrication de la famille de produits UAS;
- du point de vue du client, le cycle de vie du système comprend la période entre la mise en service sur site de la première installation du système souvent basée sur plusieurs familles de produits UAS et la mise hors service de la dernière installation du système.
L'installation du système peut être réalisée par un intégrateur système différent du constructeur du produit.

Démarrage de la production
Arrêt
Demandes de modifications
V = Version
Cycle de vie pour le fabricant
Contrat Mise en service sur site Mise hors service projet 1 projet 1 SAS 1
Contrat Mise en service sur site Mise hors service projet n projet n SASn
Mise à niveau Extension Maintenance
Cycle de vie pour le client
Figure 6 - Deux significations du cycle de vie du système
Au cours du cycle de vie pour le constructeur de l'UAS et de ses IED, un certain nombre de modifications et d'extensions sont nécessaires pour différentes raisons:
- améliorations fonctionnelles et extensions;
- modifications technologiques du matériel;
```

This technical analysis provides a clear understanding of the lifecycle of a UAS and its IEDs, highlighting the differences in interpretation between the manufacturer and the client, and the necessary support and maintenance provided by the system integrator.

---

## Page 70

![Image 1 from page 70](images/iec61850-4{ed2.0}b_page70_img30_95784b7b.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that outlines the classification and requirements for quality tests in the context of industrial equipment (IED). It details the basic test requirements, system test, and type test, emphasizing the need for reproducibility, documentation, and the involvement of qualified internal or external organizations for testing.

**Key Elements:**
- **7.3.1 Exigences d'essai de base (Basic Test Requirements):** Defines the need for a comprehensive test concept covering all activities from functional tests of the prototype to type and system final tests. It emphasizes the definition of the test scope, objectives, procedures, and acceptance criteria.
- **7.3.2 Essai système (System Test):** Describes the system test as proof of the good functionality and performance of each IED under different application conditions (configurations and different parameters) and in association with other IEDs of the UAS global family, including tools for parameterization and diagnostics.
- **7.3.3 Essai de type (Type Test):** Explains that the "aptitude à l'utilisation" (suitability for use) of a newly designed product must be proven by a type test. The type test should be conducted using samples from the manufacturing process and verifies the product against technical data.

**Extracted Text:**
```plaintext
— 68 — 61850-4 © CEI:2011
7.3. Classification des essais de qualité
7.3.1 Exigences d'essai de base
Il convient que le constructeur fournisse un concept d'essai qui couvre toutes les activités, à partir des essais fonctionnels du prototype en état de développement jusqu'aux essais de type et aux essais système finaux. Le champ d'application et l'objet des essais, les procédures d'essai et les critères d'acceptation doivent être définis.
Tous les essais doivent être documentés de façon à ce que les résultats soient reproductibles, si nécessaire.
Il convient que tous les essais soient réalisés par une partie interne de l'organisation du constructeur qui est qualifiée pour la réalisation des essais et qui a l'indépendance organisationnelle nécessaire pour constater si un produit a satisfait ou non aux essais, ou par une organisation externe indépendante, qualifiée pour les essais par une tierce partie.

7.3.2 Essai système
L'essai système est la preuve de la bonne fonctionnalité et de la performance de chaque IED selon différentes conditions d'application (configuration et paramètres différents) et, en association avec d'autres IED des familles de produits de l'UAS global, y compris tous les outils, par exemple pour le paramétrage, le diagnostic (Figure 8).

Figure 8 – Contenu de l'essai du système
La réussite de l'essai système est le préalable au démarrage de l'essai de type.

7.3.3 Essai de type
L'«aptitude à l'utilisation» d'un produit nouvellement conçu doit être prouvée par un essai de type. L'essai de type doit être réalisé en utilisant des échantillons provenant du processus de fabrication. L'essai de type est la vérification du produit par rapport aux données techniques (voir Figure 9) telles que:
- la tenue mécanique;
- la compatibilité électromagnétique;
- les influences climatiques;
- l'exactitude et la complétude fonctionnelles.
```

This technical analysis provides a clear understanding of the document's content and structure, which is essential for technical documentation and search purposes.

---

## Page 71

![Image 1 from page 71](images/iec61850-4{ed2.0}b_page71_img31_dc88772c.png)

**Image Type:** Diagram

**Description:** The image is a technical flowchart or diagram that outlines the content and structure of a type test (essai de type) and a series individual test (essai individuel de série) for a product. The diagram is divided into two main sections, each detailing the components and processes involved in these tests.

**Key Elements:**

1. **Type Test (Essai de type):**
   - **Product of Series (Produit de série):** The starting point of the type test.
   - **Type Test (Essai de type):** The main process of the type test.
   - **Micrologiciel agréé (Micrologiciel agréé):** Approved microsoftware.
   - **System Test (Essai du système):** The system test process.
   - **Mechanical (Mécanique):** Mechanical aspects of the test.
   - **Environmental + Climatic (Environnement + climatique):** Environmental and climatic conditions.
   - **CEM (CEM):** Compliance with European Union regulations.
   - **Functional Accuracy (Exactitude fonctionnelle):** The accuracy of the function.

2. **Series Individual Test (Essai individuel de série):**
   - **Functional Test (Essai de fonction):** Testing the functionality of the product.
   - **Isolation Test (Essai d'isolation):** Testing the isolation of the product.
   - **Determination (Détermining):** Determining the characteristics of the product.

**Extracted Text:**

```
61850-4 © CEI:2011 -69- Micrologiciel
Produit de série
———______p» Essai de type <—_—_—_ agréé (essai du systéme)
Environnement
Mécanique + climatique
Exactitude
fonctionnelle
lec 11202
Figure 9 — Contenu de l’essai de type
L’essai de type doit être réalisé en utilisant des logiciels qui ont réussi l’essai système.
L’essai de type doit être réussi avant que la livraison de production régulière ne puisse commencer.
7.3.4 Essai individuel de série
L’essai individuel de série comprend des essais spécifiques de matériel et de fonctionnalité comme indiqué dans la Figure 10.
Essai individuel de série
lec 11902
Figure 10 — Contenu de l’essai individuel de série
Il convient que les essais individuels de série soient réalisés pour chaque produit avant que celui-ci ne sorte de chez le constructeur.
```

This diagram and text provide a structured approach to conducting type and series individual tests for a product, ensuring compliance with standards and regulations.

---

## Page 72

![Image 1 from page 72](images/iec61850-4{ed2.0}b_page72_img32_cee18018.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the steps involved in the "Essai de réception sur site" (ERS) process, which is part of a larger system testing and validation framework. The diagram outlines the four stages of the ERS process, which is a part of the conformity testing and system reception testing procedures as described in the text.

**Key Elements:**

- **Title:** Essai de réception sur site (ERS)
- **Objective:** To demonstrate that all system components are correctly installed and connected.
- **Stages of ERS:**
  1. Procédé — niveau commande de la cellule (Procedure — cell command level)
  2. Niveau commande de la cellule — niveau commande du poste (Cell command level — post command level)
  3. Niveau commande du poste — centre de conduite du réseau (Post command level — network control center)
  4. Procédé — centre de conduite du réseau (Procedure — network control center)

**Extracted Text:**

```plaintext
-70- 61850-4 © CEI:2011
7.3.5 Essai de conformité
Les essais de conformité sont réalisés sur les voies de communication des IED et comprennent la vérification de la procédure de communication selon la norme ou ses parties (voir la CEI 61850-10).
7.3.6 Essai de réception usine (ERU)
L’essai de réception usine (ERU) permet la validation et la vérification d'un système et de ses fonctions du point de vue du client. L’essai de réception usine est facultatif.
Le champ d’application et l'objet de l'ERU doivent être discutés et convenus entre l'intégrateur système et le client, et il convient qu’ils soient documentés dans des listes de contrôle. Les listes de contrôle font partie du contrat.
Il convient que le résultat de l'ERU soit documenté et signé par l'intégrateur système ainsi que par le client.
Un ERU a pour objectif de réaliser des essais de solutions typiques et de leur comportement dans des situations normales et anormales. Une simulation de procédé permet également de réaliser des essais pour des conditions de procédé anormales et des situations de défaillance de procédé.
7.3.7 Essai de réception sur site (ERS)
Le principal objectif de l'essai de réception du système sur site (ERS) est de montrer que tous les composants du système sont correctement installés et connectés. Il doit être réalisé sur l'équipement entièrement installé suivant une série d'étapes (voir Figure 11).
Procédé
Niveau de commande de la cellule
Niveau de commande du poste
Niveau de commande du réseau
Etapes d'essai
Etapes d'essai
Etapes d'essai
Etapes d'essai
Procédé — niveau commande de la cellule
Niveau commande de la cellule — niveau commande du poste
Niveau commande du poste — centre de conduite du réseau
Procédé — centre de conduite du réseau
Figure 11 — Les étapes d’essai de l’essai de réception sur site
La Figure 11 montre quatre étapes de l'ERS:
a) procédé — niveau commande de la cellule;
b) niveau commande de la cellule — niveau commande du poste;
```

This technical analysis provides a clear understanding of the ERS process and its components, which is crucial for anyone involved in system testing and validation.

---

## Page 73

![Image 1 from page 73](images/iec61850-4{ed2.0}b_page73_img33_09de2b8c.png)

**Image Type:** Document Page
**Description:** This image is a page from a technical document, specifically page 71, as indicated by the page number at the top right corner. The document appears to be related to a standard or guideline, as suggested by the copyright notice "© CEI:2011" at the top left corner. The content discusses the command level of a post or network control center, the procedure for network control center, and the service implementation plan. It emphasizes the documentation of each step's results and the client's acceptance of the system implementation.

**Key Elements:**
- **Page Number:** 71
- **Document Title/Reference:** 61850-4 © CEI:2011
- **Sections Discussed:**
  - c) niveau commande du poste – centre(s) de conduite du réseau;
  - d) procédé – centre(s) de conduite du réseau.
- **Plan of Service Implementation:** The steps are carried out according to a service implementation plan that must cover the verification of all information exchanges and functions.
- **ERS Procedure:** The ERS procedure must document the results of each step and summarize the client's acceptance of the system implementation.

**Extracted Text:**
61850-4 © CEI:2011 -71- c) niveau commande du poste — centre(s) de conduite du réseau; d) procédé — centre(s) de conduite du réseau. Les étapes sont réalisées selon un plan de mise en service, qui doit couvrir la vérification de tous les échanges d’informations et toutes les fonctions. La procédure de l'ERS doit documenter les résultats de chaque étape et elle résume l'acceptation par le client de la mise en œuvre du système.

This document appears to be part of a technical standard or guideline related to network or system implementation, focusing on the documentation and verification of service steps and client acceptance.

---

## Page 74

![Image 1 from page 74](images/iec61850-4{ed2.0}b_page74_img34_1aaee53f.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that illustrates the timeline for the announcement and cessation of production of a product. It includes two scenarios: one where no compatible product is available after the production stop, and another where a compatible product is available. The diagram is part of an annex (Annexe A) in a document, likely related to product lifecycle management or manufacturing guidelines.

**Key Elements:**
- **Announcement (Annonce):** The start of the timeline where the product is announced.
- **Production Start (Début de la production):** The beginning of the production period.
- **Production Stop (Arrêt de la fabrication):** The end of the production period.
- **2 years (2 ans):** The period after the production stop where no compatible product is available.
- **1 year (1 an):** The period after the production stop where a compatible product is available.
- **6 months (6 mois):** The period after the production stop where a compatible product is available, but it is not functional.
- **Product (Produit ultérieur):** The compatible product available after the production stop.
- **IEC 115/02 and IEC 116/02:** Standards or guidelines referenced in the diagram.

**Extracted Text:**
```
-72- 61850-4 © CEI:2011 Annexe A (informative) Annonce de l'arrêt de la fabrication (exemple) Annonce 2 ans Arrêt de la fabrication lec 11802 Figure A.1.a - Sans aucun produit fonctionnellement compatible ultérieurement Annonce 1 an Arrêt de la fabrication 6 mois LS Produit ultérieur Début de la production lec 11602 Figure A.1.b — Avec un produit fonctionnellement compatible ultérieurement Figure A.1 — Conditions d'annonce
```

This diagram is useful for technical documentation and search as it provides a clear visual representation of the product lifecycle and compatibility considerations.

---

## Page 75

![Image 1 from page 75](images/iec61850-4{ed2.0}b_page75_img35_209024a0.png)

**Image Type:** Diagram

**Description:** The image is a timeline diagram that outlines the phases of a product's lifecycle, specifically focusing on the end of production and related services. The timeline is divided into several segments, each representing a different phase or service related to the product.

**Key Elements:**
- **Arrêt de la fabrication (End of Production):** This marks the point where the production of the product ceases.
- **Contrat spécial avec commande annuelle minimale (Special Contract with Annual Minimum Order):** This segment indicates a special contract that includes an annual minimum order requirement.
- **Produits compatibles pour une extension (Compatible Products for Extension):** This phase focuses on products that can be used to extend the functionality or capabilities of the original product.
- **Pièces de rechange d'origine et de dépannage (Original Spare Parts and Repair):** This segment covers the availability of original spare parts and repair services.
- **Versions du micrologiciel et des logiciels (Microsoftware and Software Versions):** This phase deals with the different versions of microsoftware and software that may be available for the product.
- **Interfaces adaptatives (Adaptive Interfaces):** This final phase likely pertains to the adaptability of the product's interfaces to new or changing requirements.

**Extracted Text:**
```
Arrêt de la fabrication
Contrat spécial avec commande annuelle minimale
Produits compatibles pour une extension
Pièces de rechange d'origine et de dépannage
Versions du micrologiciel et des logiciels
Interfaces adaptatives
5 10 15 20
Années
IEC 117/02
```

![Image 2 from page 75](images/iec61850-4{ed2.0}b_page75_img36_fe8ab67b.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the obligations of delivery after the cessation of production. It is part of an annex (Annexe B) in a document, specifically page 73, as indicated by the OCR text. The diagram outlines the timeline of obligations related to the delivery of products and services after the end of production, including contracts, compatible products, spare parts, and software versions.

**Key Elements:**

- **Title:** Obligations de livraison aprés l’arrêt de la fabrication (exemple) (Delivery obligations after the cessation of production (example))
- **Timeline:** The diagram spans from 5 to 20 years, marked as "Années" (Years).
- **Sections:**
  - Arrêt de la fabrication (End of production)
  - Contrat spécial avec commande annuelle minimale (Special contract with minimum annual order)
  - Produits compatibles pour une extension (Compatible products for extension)
  - Pièces de rechange d'origine et de dépannage (Original spare parts and repair parts)
  - Versions du micrologiciel et des logiciels (Microsoftware and software versions)
  - Interfaces adaptatives (Adaptive interfaces)
- **Legend:** The diagram uses horizontal lines to represent different obligations, with the length of each line corresponding to the duration of the obligation.

**Extracted Text:**

```
61850-4 © CEI:2011 -73- Annexe B (informative) Obligations de livraison aprés l’arrêt de la fabrication (exemple) Arrêt de la fabrication Contrat spécial avec commande annuelle minimale Produits compatibles pour une extension Piéces de rechange dorigine et de dépannage Versions du micrologiciel et des logiciels Interfaces adaptatives 5 10 15 20 Années Figure B.1 — Périodes des obligations de livraison
```

This technical analysis provides a clear understanding of the content and structure of the diagram, which is crucial for technical documentation and search purposes.

---

## Page 76

![Image 1 from page 76](images/iec61850-4{ed2.0}b_page76_img37_1dac40bd.png)

**Image Type:** Bibliography Page

**Description:** This image is a page from a technical document, specifically a bibliography section. It lists references related to communication networks and systems in substations, as well as quality management systems. The page number is 74, and it is part of a document with the identifier 61850-4, copyrighted by CEI in 2011.

**Key Elements:**
- Page number: 74
- Document identifier: 61850-4
- Copyright: CEI:2011
- Bibliography title: Bibliographie
- Reference 1: CEI 61850-10, "Communication networks and systems in substations – Part 10: Conformance testing" (available only in English)
- Reference 2: ISO 9001:2008, "Systèmes de management de la qualité – Exigences" (Quality Management Systems – Requirements)

**Extracted Text:**
```
– 74 –
61850-4 © CEI:2011

Bibliographie

CEI 61850-10, Communication networks and systems in substations – Part 10: Conformance testing (disponible en anglais seulement)

ISO 9001:2008, Systèmes de management de la qualité – Exigences
```

---

## Page 77

![Image 1 from page 77](images/iec61850-4{ed2.0}b_page77_img38_bfb32b20.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank or placeholder diagram, possibly used in a technical document to indicate a section where a specific diagram or illustration should be placed. It is likely part of a larger document, such as a technical manual or a presentation slide, where the actual diagram is intended to be inserted.

**Key Elements:** 
- The image is completely white, indicating no content or a placeholder.
- There are no visible technical elements, components, or data.
- The text at the bottom of the image provides information about the customer, order number, and copyright details.

**Extracted Text:** 
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

This text suggests that the image is part of a technical document related to a specific order or project, and it includes copyright and contact information for the International Electrotechnical Commission (IEC).

---

## Page 78

![Image 1 from page 78](images/iec61850-4{ed2.0}b_page78_img39_b284b55e.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank white space with no visible content, text, or technical elements. It seems to be a placeholder or an error in the document, as there is no discernible information or technical data to analyze.

**Key Elements:** None

**Extracted Text:** No text detected

This image does not provide any technical information or data for analysis. It is likely a placeholder or an error in the document. If you have another image or a different section of the document, please provide it for a detailed analysis.

![Image 2 from page 78](images/iec61850-4{ed2.0}b_page78_img40_0fce55c5.png)

**Image Type:** Document

**Description:** The image is a scanned page from a technical document, specifically from the International Electrotechnical Commission (IEC). It appears to be a cover or introductory page, providing contact information and details about the organization.

**Key Elements:**
- **Organization Name:** International Electrotechnical Commission (IEC)
- **Address:** 3, rue de Varembé, PO Box 131, CH-1211 Geneva 20, Switzerland
- **Contact Information:**
  - Telephone: +41 22 919 02 11
  - Fax: +41 22 919 03 00
  - Email: <EMAIL>
  - Website: www.iec.ch

**Extracted Text:**
```
INTERNATIONAL
ELECTROTECHNICAL
COMMISSION

3, rue de Varembé
PO Box 131
CH-1211 Geneva 20
Switzerland

Tel: +41 22 919 02 11
Fax: +41 22 919 03 00
<EMAIL>
www.iec.ch
```

This page serves as a reference for contacting the IEC, which is a global organization that develops international standards for electrical and related technologies. The provided contact details are essential for any organization or individual seeking to communicate with or collaborate with the IEC.

---

