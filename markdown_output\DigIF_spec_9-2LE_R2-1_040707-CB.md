# Digif Spec 9 2Le R2 1 040707 Cb

--- Page 1 ---
## Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2

### SCOPE
This document gives additional information on how to implement a digital communication interface to non conventional instrument transformers according to IEC 61850-9-2 and IEC 60044-7/8. The purpose of the document is to define a subset of IEC 61850-9-2 that shall support a fast market introduction of this standard.

### SUMMARY
The subset of IEC 61850-9-2 defined in this document only supports the service SendMSVMessage. As a consequence, the communication is unidirectional from the merging unit to the bay level devices and does not need to support the MMS stack. Therefore, implementation in existing bay level devices is straightforward.

### SCOPE (continued)
The document further defines a logical device merging unit and a dataset used for the transmission of the sampled values.

## Revision History
This Revision 2.1 incorporates an improvement of the text in clause 6.2.3 and the following two corrections of mistakes that where identified during implementation and testing:

* ConfRev is of length 4 instead of length 1 (Figure 4)
* Component source of data attribute type quality is encoded in one bit instead of two bits (Figure 5)

## Acknowledgments
UCA International Users Group, 10604 Candler Falls Court, Raleigh, NC 27614

### Authors and Contributors
The following persons have prepared the document:

* <PERSON>, ABB Switzerland Ltd, Zürich, Switzerland
* <PERSON>, SIEMENS AG, Berlin, Germany
* Frederic Leconte, AREVA T&D, Paris, France
* Fred Steinhauser, OMICRON electronics, Klaus, Austria

The following persons have contributed to the document by either reviewing it and/or by participation in the working group “Digital IT” of the UCA international users group:

* Mark Adamiak, GE Power Management, King of Prussia PA, U.S
* Khalid Alem, AREVA T&D  Montpellier, France
* Lars Andersson, ABB Switzerland Ltd, Zürich, Switzerland
* Alex Apostolov, AREVA T&D, Los Angeles CA, U.S
* Jim Buckingham, Landis+Gyr, U.S.
* Dac-Phuoc Bui, Hydro Quebec, Montreal, Canada
* Denis Chatrefou, AREVA T&D, Paris, France
* Fernando Cobelo, ZIV  Bilbao, Spain
* Bill Dickerson  Arbiter Systems Inc. Paso Robles CA, U.S
* Fred Engler, ABB Switzerland Ltd, Zürich, Switzerland
* Pascal Erni, ABB Switzerland Ltd, Zürich, Switzerland
* Hans-Joachim Herrmann  SIEMENS AG, Nürnberg, Germany
* Clemens Hoga, SIEMENS AG, Nürnberg, Germany
* Hachidai Ito, TMT&D, Tokyo, Japan
* Alf Johansson  ABB Switzerland Ltd, Zürich, Switzerland
* Andreas Jurisch, SIEMENS AG, Berlin, Germany
* Thomas Kern, ABB Switzerland Ltd, Zürich, Switzerland
* Andrew Klimek, NxtPhase Corp., Vancouver BC, Canada
* Pierre Martin, Hydro Quebec, Montreal, Canada
* Jean-François Mironneau, AREVA T&D  France
* Bruce Muschlitz, AREVA T&D, Bethlehem PA, U.S
* Koichi Okuno, Toshiba International (Europe) Ltd  Durham, U.K
* Veselin Skendzic, Schweitzer Engineering Laboratories Inc., U.S
* Damien Tholomier, AREVA T&D, Leval, France
* Timothy P  Tibbals, Schweitzer Engineering Laboratories Inc. Pullmann WA,

--- Page 2 ---
Page 2 / 31

## Additional Content

 U.S.
Jakob Widmer, Landis+Gyr AG, Zug, Switzerland
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 3 ---
Page 3 / 31
Table of Content
Scope .................................................................................................................. 1
Summary ............................................................................................................ 1
Normative Reference.......................................................................................... 4
Abbreviations ..................................................................................................... 4
Use of IEC 60044-8 ............................................................................................ 5
Use of IEC 61850-9-2 ......................................................................................... 5
6.1
Protocol implementation conformance statement (PICS)...............................................5
6.1.1 Notation...................................................................................................................5
6.1.2 Profile conformance ..................................................................................................5
6.1.3 SV services...............................................................................................................5
6.2
Additional specifications.............................................................................................6
6.2.1 Physical Layer ([9-2] 5.3.3) .......................................................................................6
6.2.2 Link layer ([9-2] 5.3.3)..............................................................................................6
6.2.3 Extension of data attribute type Quality ([7-3] 6.2)......................................................6
Specification of the logical device "Merging Unit"............................................. 7
7.1
Definition of the objects according to IEC 61850-7-2....................................................7
7.1.1 The logical device instance "MU" ([7-2] 8.1.1).............................................................7
7.1.2 The logical node instance "LLN0" ([7-2] 9.1.1) ............................................................7
7.1.3 The dataset "PhsMeas1" ([7-2] 11.2.1) .......................................................................8
7.1.4 The multicast sampled value control block "MSVCB01" and "MSVCB02" ([7-2] 16.2.1) ....9
7.2
Further specifications ................................................................................................9
7.2.1 Operating modes ......................................................................................................9
7.2.2 Synchronization ......................................................................................................10
7.2.3 Reference arrow orientation system..........................................................................12
7.2.4 Calculations to be done in the Merging Unit...............................................................12
7.3
Configuration of the Merging Unit.............................................................................12
Physical devices................................................................................................13
Open points ......................................................................................................13
Appendix A: Content of an Ethernet frame ................................................................14
Appendix B: XML File..................................................................................................17
Appendix C: Tables with measurement accuracy requirements ................................20
Appendix D: Nomograms for checking of dynamic ranges ........................................22
Appendix E: Reference arrow orientation system......................................................25
Change Information ...................................................................................................31
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 3 / 31
Table of Content
1 SCOPE wcsscsscesssesscesccesccaeceesesaeesaeeesnesscesscuescnasecaeseaesesgeesaeessnessessoneseasesassessessesiees L
2 SUMMALY .vsecscssccescsesccaeeesesesasesseesseesscesscnsscasccaeseaesesueesaeesseessessonsseasesassesesessesiees L
3 Normative Reference....ssssssssssssssssssssessssesssasseseseesnsaesessseesneassanesssassssansesesassnsasees &
4 Abbreviations .....ssssssssssssssssssssssssssesssssesssssssnssesssersssnsssnscsessssessssesssssorsssscsesssorsses &
5 Use Of TEC 60044-8 ......sscscsssessessnssesseneeseeseenenseeecsenseeseeseesaeseessesaeeessessasseessssaees 5
6 Use of TEC 61850-9-2 ......cscssscssessessesseseseseensenseesecseeseeseeneenaeseensesaeesesserseeseesessases 5
6.1 Protocol implementation conformance statement (PICS)....ccccesccscse cess seeeeseeeeeeeD
6.1.1 NOtation......cccccsscsesesseseseeesseecesseseessssssscsesssssensescscsssssesensnsessseaeaeeeeeesssasasaseeseeeseseseeeeD
6.1.2 Profile COMFOrMANCE .......ssssssesesssetetesssstetsesesesetsescscsesesesenenssssseaeseseeenesssseaeseeesetenssseseseD
6.1.3 SV SCLVICES....escsseseseseseseseseneeeeseseseenscsceceeaesessnsescscsesesesensnscssasseaesenenssssseaeeeeeeeessseeseeeD
6.2 — Additional specifications.......ccccccssccccsecseccscsescesssscsescsecsessesssesseasesseesceessessessenassseees®
6.2.1 Physical Layer ([9-2] 5.3.3) sssscescsssescssssecssssesssssesssssesssssseessssesssseseesseeessseeeessseeesssesessses
6.2.2 Link layer ([9-2] 5.3.3) sessecesssssescssseecssssecssssesssssesesssesesssseessseeeesseesesseescsssveessseseessesessses
6.2.3 Extension of data attribute type Quality ([7-3] 6.2) ....cceecccccs sess seesseeceeeeeeeeeeeee®
7 Specification of the logical device "Merging Unit" .......cscsssssssssesssesessseseeseees 7
7.1 Definition of the objects according to IEC 61850-7-2.....cecccccsecseesssesesssceeeeseseneesees
7.1.1 The logical device instance "MU" ([7-2] 8.1.1) ..ccccsccccsecsccscessssesscessseceesaseesesserses
7.1.2. The logical node instance "LLNO" ([7-2] 9.1.1) .ccccecccsecseececeeseseesscessseceessseeseeseees
7.1.3. The dataset "PhsMeasi" ([7-2] 11.2.1) sssecssssessssssesssssesssssesessssecssssesesssesesssesesssveessseeeens®
7.1.4. The multicast sampled value control block "MSVCB01" and "MSVCBO02" ([7-2] 16.2.1) ....9
7.2 Further Specifications .....ccccccccsccsscescceescecseeesseesesesesscsesceeseessessesscsesseessessensenseesseee nD
7.2.1 Operating MOCES ....ceeccccecceceeceecsseceeceesceesseeseesesuseescoesceasoeseessessceseeessessessenseessene nD
7.2.2 SYMCHTONIZATION vo... eee cect cee eceeseeeeeeeceeceeeceeseessesseeesseceeseesseessessescssesscoesseaseesessers LO
7.2.3 Reference arrow orientation SYStOM......ccccccccecssseecssscecseecseseesssscessssceeseasersenees LO
7.2.4 Calculations to be done in the Merging Unit... cc cccccececsscecsesseessesesseeseneneeeeee L2
7.3 Configuration of the Merging Unit........ccccccccscssseecssscecsessesesssseesssscessasersesees LO
8 Physical devices.....cssccsessesssesscesscesscasescesesnseseesseessesseesenaseseessessssessesess LS
9 Open POINts .....cccscessesscesssesesaesesesesseesseesseesssesseeesesesesaeseaesesaeeseessessessessesees LS
Appendix A: Content of an Ethernet frame ......csscsssessesssesssesscesscesscaesesesesseeseessessees L&
Appendix B: XML File....sssssssssssssssscsesseessseneesesnesesacsesesaesssaesesesaesesassesaseesesassesasensneassesen LZ
Appendix C: Tables with measurement accuracy requireMent .....ssssessseeeeeeseees 20
Appendix D: Nomograms for checking of dynamic ranges ......cssssssssesseseeseeeseesees 22
Appendix E: Reference arrow orientation systeM......ccssssssssseseeeeseseseeseeseeeeesses 2D
Change Information ......ccssccssscssesseesseesseesscesscnesccasccaeseaesesaeesaeessnesscessonsssasesassesesessens OL
rn a
S Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
@y cA
IY Modification Index: R2-1
premnatic Last date of storage: 2004-07-07
--- Page 4 ---
Page 4 / 31
NORMATIVE REFERENCE
[44-8]
International Standard IEC 60044-8: 2002(E), Instrument Transformers – Part 8;
Electronic current transformers, First edition 2002-07
[6]
IEC 61850-6: Communication networks and systems in substations – Part 6:
Configuration description language for communication in electrical substations
related to IEDs; Draft FDIS
[7-2]
IEC 61850-7-2: Communication networks and systems in substations – Part 7-2:
Basic communication structure for substation and feeder equipment – Abstract
communication service interface (ACSI); First edition 2003-05
[7-4]
IEC 61850-7-4: Communication networks and systems in substations – Part 7-4:
Basic communication structure for substation and feeder equipment – Compatible
logical node classes and data classes; First edition 2003-05
[9-2]
IEC 61850-9-2 : Communication networks an systems in substations, Part 9-2:
Specific communication service mapping (SCSM) – Sampled values over ISO / IEC
8802-3; 57/690/FDIS
ABBREVIATIONS
1PPS
One Pulse Per Second. Time synchronization signal
A-Profile
Application profile
Circuit breaker
Current transducer
Identifier
IEC
International electrotechnical commission
Merging unit
PICS
Protocol implementation conformance statement
PID
Protocol implementation document
SCL
Substation configuration language
Sampled values
T-Profile
Transport profile
VLAN
Virtual local area network
Voltage transducer
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 5 ---
Page 5 / 31
USE OF IEC 60044-8
The clauses of IEC 60044-8 related to the digital output (clause 5.3 and 6.2) are not applicable.
The communication interface shall be designed according to IEC 61850-9-2 and the additional
specifications made in this document instead. However, the concept of the merging unit as
explained in clause 1.3 shall apply in principle. More details of the merging unit are specified in
clause 7 of this document.
The maximum delay time as specified in clause 5.3.2, NOTE 2 shall apply as maximum allowed
delay.
The specification of the clock input according to clause 6.2.5 of [44-8] shall apply as well.
USE OF IEC 61850-9-2
6.1
Protocol implementation conformance statement (PICS)
The implementation of IEC 61850-9-2 shall be according to the following protocol implementation
conformance statement (see also clause 9 of IEC 61850-9-2).
6.1.1
Notation
For the following clause, the following definitions apply:
m: mandatory support. The item shall be implemented.
c: conditional support. The item shall be implemented if the stated condition exists.
o: optional support. The implementers may decide to implement the item.
x: excluded: The implementers shall not implement this item.
i: out-of-scope: The implementation of the item is not within the scope of this guideline
6.1.2
Profile conformance
Table 1 and Table 2 define the basic conformance statement.
Table 1 – PICS for A-profile support
Client
Server
Value/Comment
Client/Server A-Profile
Refer to clause 5.2 of 61850-9-2
SV A-Profile
Refer to clause 5.3 of 61850-9-2
Table 2 – PICS for T-profile support
Client
Server
Value/Comment
TCP/IP T-Profile
SV T-Profile
c1 – shall be ‘m’ if support for A1 is declared. Otherwise, shall be “i”
6.1.3
SV services
This clause describes the Protocol Implementation Conformance Statement for sampled values
services based on the IEC 61850-7-2 basic conformance statement.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 5 / 31
5 Use oF IEC 60044-8
The clauses of IEC 60044-8 related to the digital output (clause 5.3 and 6.2) are not applicable.
The communication interface shall be designed according to IEC 61850-9-2 and the additional
specifications made in this document instead. However, the concept of the merging unit as
explained in clause 1.3 shall apply in principle. More details of the merging unit are specified in
clause 7 of this document.
The maximum delay time as specified in clause 5.3.2, NOTE 2 shall apply as maximum allowed
delay.
The specification of the clock input according to clause 6.2.5 of [44-8] shall apply as well.
6 Use OF IEC 61850-9-2
6.1 Protocol implementation conformance statement (PICS)
The implementation of IEC 61850-9-2 shall be according to the following protocol implementation
conformance statement (see also clause 9 of IEC 61850-9-2).
6.1.1 Notation
For the following clause, the following definitions apply:
*® —m: mandatory support. The item shall be implemented.
ec: conditional support. The item shall be implemented if the stated condition exists.
e 0: optional support. The implementers may decide to implement the item.
e x: excluded: The implementers shall not implement this item.
e i: out-of-scope: The implementation of the item is not within the scope of this guideline
6.1.2 Profile conformance
Table 1 and Table 2 define the basic conformance statement.
Table 1 — PICS for A-profile support
far [atenisenerseranie [> | fo |__| Reterto cause 2 of 105092 |
fo [svete fm | fm | (Retro eae 53 oF 185092 |
Table 2 — PICS for T-profile support
tiene server vatercomment |
in [renprrene fa | fa || ————*d
fe [svrrene fm | [| | S—S
6.1.3 SV services
This clause describes the Protocol Implementation Conformance Statement for sampled values
services based on the IEC 61850-7-2 basic conformance statement.
r CE
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
aw sr. 61850-9-2
UXKAN
PX Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 6 ---
Page 6 / 31
Table 3 – SV conformance statement
Services
Client/
subscriber
Server/
publisher
Value/Comment
Multicast
SendMSVMessage
GetMSVCBValues
SetMSVCBValues
Unicast
SendUSVMessage
GetUSVCBValues
SetUSVCBValues
6.2
Additional specifications
6.2.1
Physical Layer ([9-2] 5.3.3)
Fiber optic transmission system 100Base-FX full duplex with ST connectors is recommended. The
only allowed alternate solutions are 100Base-FX with MT-RJ connectors or electrical transmission
using 100Base-TX full duplex with RJ-45 connectors.
6.2.2
Link layer ([9-2] 5.3.3)
The default settings for priority tag and VLAN ID shall be used as specified in [9-2]. The APPID
shall always be 0x4000.
6.2.3
Extension of data attribute type Quality ([7-3] 6.2)
The data attribute type Quality defined in IEC 61850-7-3 is extended by adding the following
component at the end:
Quality Type Definition
Attribute Name
Attribute Type
Value/Value Range
M/O/C
derived
BOOLEAN
DEFAULT FALSE
derived
This identifier shall be set to FALSE, if the value is based on a real sensor in the process
(optionally including some additional calculations behind, e.g. for a RMS calculation). If the
identifier is set to TRUE, it is meant that there is no physical sensor within the system to
determine the value, but the value is derived from a combination of values from other physical
sensors.
EXAMPLE 1 – There may be a CT used to measure the neutral current. In that case, the identifier derived shall be set to
FALSE. If the neutral current is calculated from the phase values, the identifier shall be set to TRUE.
EXAMPLE 2 – A disconnector and an earthing switch may be combined to one physical switch having multiple positions. In
that case, the position values for the disconnector and the earting switch – each modeled in a logical node XDIS – would
have the identifier derived set to TRUE.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 6 / 31
Table 3 — SV conformance statement
Client/ Server/ Value/Comment
subscriber publisher
6.2 Additional specifications
6.2.1 Physical Layer ({9-2] 5.3.3)
Fiber optic transmission system 100Base-FX full duplex with ST connectors is recommended. The
only allowed alternate solutions are 100Base-FX with MT-RJ connectors or electrical transmission
using 100Base-TX full duplex with RJ-45 connectors.
6.2.2 Link layer ((9-2) 5.3.3)
The default settings for priority tag and VLAN ID shall be used as specified in [9-2]. The APPID
shall always be 0x4000.
6.2.3 Extension of data attribute type Quality ({7-3] 6.2)
The data attribute type Quality defined in IEC 61850-7-3 is extended by adding the following
component at the end:
Quality Type Definition
Attribute Type Value/Value Range M/O/C
Lise —— BOOLEAN DEFAULT FALSE [ow
derived
This identifier shall be set to FALSE, if the value is based on a real sensor in the process
(optionally including some additional calculations behind, e.g. for a RMS calculation). If the
identifier is set to TRUE, it is meant that there is no physical sensor within the system to
determine the value, but the value is derived from a combination of values from other physical
sensors.
EXAMPLE 1 - There may be a CT used to measure the neutral current. In that case, the identifier derived shall be set to
FALSE. If the neutral current is calculated from the phase values, the identifier shall be set to TRUE.
EXAMPLE 2 - A disconnector and an earthing switch may be combined to one physical switch having multiple positions. In
that case, the position values for the disconnector and the earting switch — each modeled in a logical node XDIS - would
have the identifier derived set to TRUE.
r CE
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
Ona fr -9-:
es “es \ 61850-9-2
NIV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 7 ---
Page 7 / 31
SPECIFICATION OF THE LOGICAL DEVICE "MERGING UNIT"
IEC 61850 does not specify logical devices. Logical devices may be described in SCL and
configured through several services of IEC 61850. To reduce the first implementations to a
minimum of required services without loosing interoperability, this guideline provides a detailed
specification of the logical device merging unit as used within the scope of this guideline.
7.1
Definition of the objects according to IEC 61850-7-2
7.1.1
The logical device instance "MU" ([7-2] 8.1.1)
The attributes of the logical device MU shall have the following values:
Table 4 – Logical device instance "MU"
Attribute
Name
Value
M/0
Comment
LDName
xxxxMUnn
xxxx is configurable according to [6], clause 8.4.2 and
MUnn is the Attribute Inst of the element LDevice in the
IED section of the SCL (nn shall identify the measuring
point within the bay)
LDRef
xxxxMUnn
LogicalNode
LLN0
LPHD
InnATCTR1
InnBTCTR2
InnCTCTR3
InnNTCTR4
UnnATVTR1
UnnBTVTR2
UnnCTVTR3
UnnNTVTR4
1 ..5 is the attribute Inst of the element LN in the IED
section
Unn / Inn is the identification of the Sensor; A, B, Cand N
are the phase identification. Both values are part of the
substation section of the SCL and are used to build the
name according to [6], clause 8.4.2
7.1.2
The logical node instance "LLN0" ([7-2] 9.1.1)
The attributes of the logical node LLN0 shall have the following values:
Table 5 – LLN0
Attribute Name
Value
M/O
Comment
LNName
LLN0
LNRef
xxxxMUnn/LLN0
Data
As defined in 61850-7-4
DataSet
PhsMeas1
MultiCastSampledValueC
ontrolBlock
MSVCB01
MSVCB02
c1 – At least one of the two MulticastSampledValueControlBlock shall be implemented
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 7 / 31
7 SPECIFICATION OF THE LOGICAL DEVICE "MERGING UNIT"
IEC 61850 does not specify logical devices. Logical devices may be described in SCL and
configured through several services of IEC 61850. To reduce the first implementations to a
minimum of required services without loosing interoperability, this guideline provides a detailed
specification of the logical device merging unit as used within the scope of this guideline.
7.1 Definition of the objects according to IEC 61850-7-2
7.1.1 The logical device instance "MU" ([7-2] 8.1.1)
The attributes of the logical device MU shall have the following values:
Table 4 — Logical device instance "MU"
LDName xxxxMUnn xxxx is configurable according to [6], clause 8.4.2 and
MUnn is the Attribute Inst of the element LDevice in the
IED section of the SCL (nn shall identify the measuring
point within the bay)
LogicalNode | LLNO 1 ..5 is the attribute Inst of the element LN in the IED
LPHD section
InnATCTR1I Unn / Inn is the identification of the Sensor; A, B, Cand N
InnBTCTR2 are the phase identification. Both values are part of the
InnCTCTR3 substation section of the SCL and are used to build the
InnNTCTR4 name according to [6], clause 8.4.2
UnnATVTR1
UnnBTVTR2
UnnCTVTR3
UnnNTVTR4
7.1.2 The logical node instance "LLNO" ({7-2] 9.1.1)
The attributes of the logical node LLNO shall have the following values:
Table 5 — LLNO
[Aibutename [value [wo[ comment
MultiCastSampledValueC | MSVCBO1 ct
ontrolBlock MsVCBO2 ct
c1 - At least one of the two MulticastSampledValueControlBlock shall be implemented
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
Ao fr 61850-9-2
V VV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 8 ---
Page 8 / 31
7.1.3
The dataset "PhsMeas1" ([7-2] 11.2.1)
The attributes of the dataset shall have the following values:
Table 6 – Dataset "PhsMeas1"
Attribute Name
Value
Comment
DSName
PhsMeas1
DSRef
xxxxMUnn/LLN0$PhsMeas1
DSMemberRef
InnATCTR1.Amp[MX]
InnBTCTR2.Amp[MX]
InnCTCTR3.Amp[MX]
InnNTCTR4.Amp[MX]
UnnATVTR1.Vol[MX]
UnnBTVTR2.Vol[MX]
UnnCTVTR3.Vol[MX]
UnnNTVTR4.Vol[MX]
The common data class SAV used for the data above shall support the following MX attributes:
Table 7 – Common data class SAV
Attribute Name
Attribute Type
Comment
instMag.i
INT32
Quality
This includes validity information and test flag and
an indication if the value is derived or based on a
real sensor
sVC.scaleFactor
FLOAT32
0.001 for current; 0.01 for voltage
sVC.offset
FLOAT32
Always 0
This implementation guideline defines a fixed scaling. See Appendices C and D for more details.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 8 / 31
7.1.3 The dataset "PhsMeast" ({7-2] 11.2.1)
The attributes of the dataset shall have the following values:
Table 6 — Dataset "PhsMeas1"
[aetibuteneme[ vee [Somme
DSMemberRef InnATCTR1.Amp[MX]
InnBTCTR2.Amp[MX]
InnCTCTR3.Amp[MX]
InnNTCTR4.Amp[MX]
UnnATVTR1.Vol[MX]
UnnBTVTR2.Vol[MX]
UnnCTVTR3.Vol[MX]
UnnNTVTR4.Vol[MX]
The common data class SAV used for the data above shall support the following MX attributes:
Table 7 — Common data class SAV
Quality This includes validity information and test flag and
an indication if the value is derived or based on a
real sensor
sVC.scaleFactor | FLOAT32 0.001 for current; 0.01 for voltage
sVC.offset FLOAT32 Always 0
This implementation guideline defines a fixed scaling. See Appendices C and D for more details.
4 CE
CS Implementation Guideline for Digital Interface to Instrument Transformers using IEC
ASLAN 61850-9-2
Pn Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 9 ---
Page 9 / 31
7.1.4
The multicast sampled value control block "MSVCB01" and "MSVCB02" ([7-2] 16.2.1)
The sampled value control block shall be preconfigured as follows:
Table 8 – Multicast sampled value control block "MSVCBxx"
Attribute
Name
Value MSVCB01
Value MSVCB02
Comment
MsvCBNam
MSVCB01
MSVCB02
MsvCBRef
xxxxMUnn/LLN0$MSVCB01
xxxxMUnn/LLN0$MSVCB02
SvEna
TRUE / FALSE
TRUE / FALSE
Value is defined by
configuration (see clause
7.3)
MsvID
xxxxMUnn01
xxxxMUnn02
xxxxMUnn is the LDName;
01/02 is the number of the
MSVCB instance
DatSet
xxxxMUnn/LLN0$PhsMeas1
xxxxMUnn/LLN0$PhsMeas1
ConfRev
SmpRate
256
OptFlds
refresh-time
TRUE / FALSE
TRUE / FALSE
sample-
synchronized
TRUE
TRUE
sample-rate
FALSE
FALSE
NOTE – since this implementation guideline defines both the datasets used for the transmission of the sampled values as
well as the values of the MSVCB, the attribute ConfRev always has the same value.
In addition, the mapping specific attributes shall be preconfigured as follows:
Table 9 – Mapping specific attributes of "MSVCBxx"
Attribute Name
Value
MSVCB01
Value
MSVCB02
Comment
NoASDU
MACDestinationAddress
Needs to be configured; the recommendations of [9-2],
Annexe C shall be followed
OptFlds
security
FALSE
FALSE
data-set
FALSE
FALSE
7.2
Further specifications
7.2.1
Operating modes
The following operation modes are supported (see [7-4] clause 6)
This is the normal operation mode. In this operation mode, the function of the merging unit is
active, the merging unit transmits the frames.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 9 / 31
7.1.4 The multicast sampled value control block, "MSVCBO1" and "MSVCBO2" ({7-2] 16.2.1)
The sampled value control block shall be preconfigured as follows:
Table 8 — Multicast sampled value control block "MSVCBxx"
Attribute Value MSVCBO1 Value MSVCBO2
Name
vieveBNam | MSVCBOI SvC502 LT
MsvCBRef xxxxMUnn/LLNO$MSVCBO1 | xxxxMUnn/LLNO$MSVCBO2 | tt—SY
SvEna TRUE / FALSE TRUE / FALSE Value is defined by
configuration (see clause
7.3)
MsvID xxxxMUnn01 xxxxMUnn02 xxxxMUnn is the LDName;
01/02 is the number of the
MSVCB instance
[Datser _ouetunn/LuNogPheveast [moomunnsuinosrheweesi |
sample- TRUE TRUE
synchronized
NOTE - since this implementation guideline defines both the datasets used for the transmission of the sampled values as
well as the values of the MSVCB, the attribute ConfRev always has the same value.
In addition, the mapping specific attributes shall be preconfigured as follows:
Table 9 — Mapping specific attributes of "MSVCBxx"
Value Value
MSVCBO1 | MSVCBO2
MACDestinationAddress Needs to be configured; the recommendations of [9-2],
Annexe C shall be followed
7.2 Further specifications
7.2.1 Operating modes
The following operation modes are supported (see [7-4] clause 6)
This is the normal operation mode. In this operation mode, the function of the merging unit is
active, the merging unit transmits the frames.
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
Ao fr 61850-9-2
V VV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 10 ---
Page 10 / 31
TEST
While in test mode, the merging unit transmits the frames, but the information is flagged as
"test" with the respective bit in the quality information. The implementation of test mode is
optional. The PID has to specify if it is implemented or not and what kind of test data are sent.
OFF
During startup of the device, the merging unit is in the mode OFF. No frames are transmitted
until the merging unit is fully operational.
7.2.2
Synchronization
The MU shall have the capability to accept an external synchronizing signal, so that its sampling
can be synchronized both between MUs and to an external time reference. The synchronization
signal shall be a 1PPS input according the specification in IEC 60044-8, clause 6.2.5, subclause
"optical input" with the fiber as specified in IEC 60044-8, clause 6.2.2.1, Table 10, column "glass
fiber".
The PPS pulse rise time may have an impact on the synchronization of the internal MU clock. This
impact can be ignored, if a maximum trigger time imperfection of  ±10% and the rise time of 200
ns will be assumed.  See Figure 1.
rise time
200 ns
light
100 %
50 %
0 %
Clock jitter ± 2 µs
trigger range ± 10%
Figure 1 – Definition of the maximum clock jitter and rise time at the MU clock input
Synchronization accuracy
The source for time synchronization shall have an accuracy of ± 1µsec. The samples from a
merging unit shall be time stamped with an accuracy of class 4 according to IEC 61850-5 (±
4µsec). It is assumed that the communication network may add 0..2µsec delay. Therefore, the
MU clock input may have a jitter of ± 2µsec (see Figure 2 for more details). If there is more than
2µsec propagation delay between 1 PPS signal output of the clock and merging unit input, each
merging unit shall be able to compensate the signal delays.
NOTE – If more than one clock is needed, GPS based clocks are necessary. The specified accuracy shall be a global
accuracy between each clocks or merging units.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 11 ---
Page 11 / 31
Output of time master
Input MU 1 (no delay)
Input MU 2 (max delay)
Allowed sampling inaccuracy
Max jitter range
Max propagation delay
Precise Synchronisation pulse
Figure 2 – Accuracy of synchronization
Operating with synchronization
As long as the merging unit is synchronized, the attribute "SmpSynch" in the SV message shall be
set to TRUE. The attribute "SmpCnt" shall behave as specified in IEC 61850-7-2.
Loss of synchronization signal
If the synchronization signal is lost, the merging unit may go in a hold-over mode. This means,
for a couple of seconds – depending on the drift of the internal clock, the merging unit is able to
send samples still fulfilling the synchronization requirements. During this time, "SmpSynch" shall
still be set to TRUE. As long as this is the case, "SmpCnt" shall wrap as if a synchronization pulse
would be present (i.e. at 3999 in the case of 80 samples per period and 50 Hz network
frequency).
Operating without synchronization
If the merging unit does not receive a synchronization signal and has left the hold-over mode as
described above, "SmpSynch" in the SV message shall be set to FALSE. "SmpCnt" shall wrap as if
a synchronization pulse would be present.
NOTE – If a physical device implements more than one logical device merging unit, it is assumed that the samples from
the merging units of the same physical device are synchronized to each other even when "SmpSynch" is set to FALSE.
Clock source
The time master generating the 1PPS signal is typically based on a GPS receiver. The 1PPS pulse
shall have an accuracy of ± 1µsec compared to an absolute time (GPS standard time). In case of
a loss of GPS reception, the internal clock of the time master will drift away from the GPS
standard time. There are two possibilities to handle that situation:
(a) The time master continues to generate the 1PPS signal. The merging unit will continue to
operate with synchronization.
(b) The time master stops to generate the 1PPS signal. The merging unit will operate
without synchronization.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 12 ---
Page 12 / 31
In case (a), functions receiving information from merging units using different time masters (e.g.
line differential protections) will not be aware that the samples are not synchronized anymore
and will not operate correctly. However, functions receiving information from merging units using
the same time master (typically functions dealing with information from the same substation
only) will continue to operate. In case (b), all functions receiving information from more than one
merging unit will not operate anymore. It is an issue of the system integrator, to decide which
option to use.
NOTE – This situation is due to the fact, that the merging unit can only mark the samples as synchronized or not
synchronized. A better approach would be to differentiate at least between global synchronized and local (e.g. station
wide) synchronized. This is an open issue that should be further considered in future revisions of IEC 61850.
7.2.3
Reference arrow orientation system
See Appendix E for more details.
7.2.4
Calculations to be done in the Merging Unit
If neutral current and / or voltage are not measured, the merging unit has to calculate these
values as a sum of the phase values. However, since the receiver needs to know, if the values
are calculated or measured, the merging unit has to indicate that in the respective quality field.
7.3
Configuration of the Merging Unit
This clause lists the configuration parameters of the merging unit according the specification in
this clause that need to be configurable.
Table 10 – Configuration parameters of the merging unit
Parameter
Value Range
Comment / SCL configuration
LDName
xxxxMUnn
xxxx is, according to [6], clause 8.4.2, the
concatenation of substation name, voltage level and
bay
MUnn is, according to [6] the attribute Inst of the
element LDevice. MU is predefined by this standard
while nn needs to be configured and is used to
differentiate several merging units within the same
bay; i.e. nn identifies the measuring point.
MACDestinationAddress for
MSVCB01
01-0C-CD-04-xx-xx
xx-xx needs to be configured if MSVCB01 is enabled
MACDestinationAddress for
MSVCB02
01-0C-CD-04-xx-xx
xx-xx needs to be configured if MSVCB02 is enabled
MSVCB01 Enabled
TRUE/FALSE
Transmission of 80 samples per nominal line cycle
enabled
MSVCB02 Enabled
TRUE/FALSE
Transmission of 256 samples per nominal line cycle
enabled
Nominal frequency
ENUMERATION (50,
60, …)
Values like 16.7 or 25 may be used in the future
NOTE 1 - Further not uniquely defined values like the sensor identification in the LN instance names (Inn / Unn) are not
relevant, since they are not visible in the SV message.
NOTE 2 – The first parameter mentioned above is a real configuration parameter. The other parameters are data of
control blocks or logical nodes instantiated in the merging unit. The parameter nominal frequency is a data of the LNs
TCTR and TVTR. In most cases, it is a read only configuration parameter that is preset by the merging unit.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 13 ---
Page 13 / 31
PHYSICAL DEVICES
No specifications are made with regard to physical devices (IEDs). An IED may consist of more
than one logical device merging unit sharing the same communication interface.
OPEN POINTS
In a future revision of this implementation guideline, the following topics will be considered:
Use of IRIG-B time synchronization as alternate solution to 1PPS
Improved flagging of synchronization status (e.g. local synch / station wide synch / global
synch)
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 14 ---
Page 14 / 31
APPENDIX A: CONTENT OF AN ETHERNET FRAME
Octets
msb
Preamble
lsb
Start of frame
0x01
0x0C
0xCD
0x04
Destination address
Header
MAC
Source address
0x8100
TPID
User priority
CFI
VID
4,0,0
Priority
tagged
VID
0x88BA
Ethertype
0x4000
APPID
Length
0x0000
reserved 1
0x0000
reserved 2
See below
Ether-
type
PDU
APDU
(Pad bytes if necessary)
Frame check sequence
Figure 3 - Content of an Ethernet frame
Remarks to Figure 3
Data fields consisting of one byte have the most significant bit on the left and the least
significant on the right.
Data fields consisting of more than one byte have the most significant bit in the upper left
and the least significant bit in the lower right corner (as shown in the field ‘Preamble’).
The octets are sent over the wire with bit 1 as the first bit.
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 14 / 31
APPENDIX A: CONTENT OF AN ETHERNET FRAME,
Octets sizte{[s5i/4}3}2]i
1 msb_
4 Preamble
7 Ich
9 x01
10 oxoc
"1 . OxcD
12 Destination address oxo4
15 Header
16 mac
" Se ddl
18 jource address
24 0x8100
22 Priority TPID
23 tagged 4,0,0
24 0
25 Ethertype Ox88BA
27 0x4000
8 APPID
31 type dt 0x0000
32 PDU reserve
33 0x0000
. APDU See below
: (Pad bytes if necessary)
: Frame check sequence
Figure 3 - Content of an Ethernet frame
Remarks to Figure 3
e Data fields consisting of one byte have the most significant bit on the left and the least
significant on the right.
e Data fields consisting of more than one byte have the most significant bit in the upper left
and the least significant bit in the lower right corner (as shown in the field Preamble’).
¢ The octets are sent over the wire with bit 1 as the first bit.
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
Aoi fr 61850-9-2
V VV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 15 ---
Page 15 / 31
The APDU resulting from the definitions in clause 7.1 (MSVCB02) is shown below.
savPdu
L (751…943)
noASDU
L (1)
Sequence of ASDU
L (744..936)
Sequence ASDU1
(91..115)
svID
(10..34)
values
smpCnt
L (2)
values
confRev
L (4)
smpSynch
L (1)
values
Sequence of Data
L (64)
values
values
values
values
values
values
values
ASDU 1
Data
Set
values
Sequence ASDU2
ASDU 2
Sequence ASDU3
ASDU 3
Sequence ASDU4
ASDU 4
Sequence ASDU5
ASDU 5
Sequence ASDU6
ASDU 6
Sequence ASDU7
ASDU 7
Sequence ASDU8
ASDU 8
ASN.1 Tag
L = Length
Figure 4 – APDU according the definitions of clause 7.1
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 15 / 31
The APDU resulting from the definitions in clause 7.1 (MSVCB02) is shown below.
savPdu = [_60 | L@si.s43) |
noASDU | so [tw] 8 |
Sequence of ASDU
Sequence ASDU1
91.115)
“ a=! _]
10..34)
smpcCnt | 82 | L@ | values | |
smpSynch | 85 | La) | values | |
Sequence of Data a [82 [top Py |
Data
Set
Sequence ASDU2
Sequence ASDU3
Sequence ASDU4
Sequence ASDU5
Sequence ASDU6
Sequence ASDU7
Sequence ASDU8
Figure 4 — APDU according the definitions of clause 7.1
rn a
}; Implementation Guideline for Digital Interface to Instrument Transformers using IEC
KAN 61850-9-2
NIV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 16 ---
Page 16 / 31
The encoding of the dataset PhsMeas1 will be as follows:
Octet
msb
InnATCTR1.Amp.instMag.i
lsb
InnATCTR1.Amp.q
der
OpB
Test
Source
DetailQual
DetailQual
validity
InnBTCTR2.Amp.instMag.i
InnBTCTR2.Amp.q
InnCTCTR3.Amp.instMag.i
InnCTCTR3.Amp.q
InnNmTCTR4.Amp.instMag.i
InnNmTCTR4.Amp.q
UnnATVTR1.Vol.instMag.i
UnnATVTR1.Vol.q
UnnBTVTR2.Vol.instMag.i
UnnBTVTR2.Vol.q
UnnCTVTR3.Vol.instMag.i
UnnCTVTR3.Vol.q
UnnNmTVTR4.Vol.instMag.i
UnnNmTVTR4.Vol.q
Figure 5 – Encoding of the dataset
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- OCR Supplementary Text ---
Page 16 / 31
The encoding of the dataset PhsMeas1 will be as follows:
Octet a a ee ee
Tsb.
InnATCTR1.Amp.instMag.i
| TT der T ops T Test [Source[ DetailQual |
Figure 5 — Encoding of the dataset
oO Implementation Guideline for Digital Interface to Instrument Transformers using IEC
Aoi fr 61850-9-2
’ VV Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 17 ---
Page 17 / 31
APPENDIX B: XML FILE
The XML file below is a .icd file for a merging unit with 80 samples per period.
<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v5 rel. 4 U (http://www.xmlspy.com) by Christoph Brunner (ABB Switzerland Ltd) -->
<SCL
xmlns="http://www.iec.ch/61850/2003/SCL"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL
SCL.xsd">
<Header id="9-2LE-Spec" nameStructure="FuncName" version="0.7" revision="1"/>
<Substation name="">
<VoltageLevel name="">
<Bay name="">
<ConductingEquipment name="Inn" type="CTR">
<SubEquipment name="A" phase="A">
<LNode lnClass="TCTR" lnInst="1"/>
</SubEquipment>
<SubEquipment name="B" phase="B">
<LNode lnClass="TCTR" lnInst="2"/>
</SubEquipment>
<SubEquipment name="C" phase="C">
<LNode lnClass="TCTR" lnInst="3"/>
</SubEquipment>
<SubEquipment name="N" phase="N">
<LNode lnClass="TCTR" lnInst="4"/>
</SubEquipment>
</ConductingEquipment>
<ConductingEquipment name="Unn" type="VTR">
<SubEquipment name="A" phase="A">
<LNode lnClass="TVTR" lnInst="1"/>
</SubEquipment>
<SubEquipment name="B" phase="B">
<LNode lnClass="TVTR" lnInst="2"/>
</SubEquipment>
<SubEquipment name="C" phase="C">
<LNode lnClass="TVTR" lnInst="3"/>
</SubEquipment>
<SubEquipment name="N" phase="N">
<LNode lnClass="TVTR" lnInst="4"/>
</SubEquipment>
</ConductingEquipment>
</Bay>
</VoltageLevel>
</Substation>
<IED name="TEMPLATE">
<AccessPoint name="">
<Server>
<Authentication/>
<LDevice inst="MUnn">
<LN0 lnType="9-2LELLN0" lnClass="LLN0" inst="">
<DataSet name="PhsMeas1">
<FCDA lnClass="TCTR" ldInst="1" fc="MX" doName="Amp"/>
<FCDA lnClass="TCTR" ldInst="2" fc="MX" doName="Amp"/>
<FCDA lnClass="TCTR" ldInst="3" fc="MX" doName="Amp"/>
<FCDA lnClass="TCTR" ldInst="4" fc="MX" doName="Amp"/>
<FCDA lnClass="TVTR" ldInst="1" fc="MX" doName="Vol"/>
<FCDA lnClass="TVTR" ldInst="2" fc="MX" doName="Vol"/>
<FCDA lnClass="TVTR" ldInst="3" fc="MX" doName="Vol"/>
<FCDA lnClass="TVTR" ldInst="4" fc="MX" doName="Vol"/>
</DataSet>
<SampledValueControl name="MSVCB01" datSet="PhsMeas1" smvID="xxxxMUnn01"
smpRate="80" nofASDU="1" confRev="1">
Implementation Guideline for Digital Interface to Instrument Transformers using IEC
61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
--- Page 18 ---
Page 18 / 31
<SmvOpts
refreshTime="false"
sampleSynchronized="true"
sampleRate="false"
security="false" dataRef="false"/>
</SampledValueControl>
</LN0>
<LN lnType="9-2LETCTR" lnClass="TCTR" inst="1"/>
<LN lnType="9-2LETCTR" lnClass="TCTR" inst="2"/>
<LN lnType="9-2LETCTR" lnClass="TCTR" inst="3"/>
<LN lnType="9-2LETCTR" lnClass="TCTR" inst="4"/>
<LN lnType="9-2LETVTR" lnClass="TVTR" inst="1"/>
<LN lnType="9-2LETVTR" lnClass="TVTR" inst="2"/>
<LN lnType="9-2LETVTR" lnClass="TVTR" inst="3"/>
<LN lnType="9-2

This document appears to be an implementation guideline for digital interfaces to instrument transformers, specifically for the IEC 61850-9 standard. The document provides information on various aspects related to the interface, including:

1. Introduction and scope
2. Definitions and abbreviations
3. System configuration and connections
4. Data formats and quality fields
5. Length of configuration revision (ConfRev) and quality fields
6. Reference arrow orientation system for conventional CTs with CT star-point (neutral) on object side
7. Alternative connection for conventional equipment as used by several vendors

The document also includes a change information section, which lists the modifications made to the document over time.

Some key points from the document include:

* The standard uses a digital interface for instrument transformers, allowing for more precise and reliable measurements.
* The system configuration and connections are described in detail, including the use of CTs (current transformers) and VTs (voltage transformers).
* The data formats and quality fields are specified to ensure compatibility between different devices and systems.
* The reference arrow orientation system is used to define the direction of currents and voltages in the system.
* The document provides guidance on how to connect conventional equipment, such as CTs with CT star-point (neutral) on object side or bus-side.

Overall, this document appears to be a technical guide for implementing digital interfaces to instrument transformers according to the IEC 61850-9 standard.

---

# Images and Diagrams

## Page 1

---

## Page 2

---

## Page 3

---

## Page 4

---

## Page 5

---

## Page 6

---

## Page 7

![Image 1 from page 7](images/DigIF_spec_9-2LE_R2-1_040707-CB_page7_img8_de00c122.png)

**Image Type:** Table and Text

**Description:** The image is a technical document page from a guideline for the implementation of a digital interface to instrument transformers using IEC 61850. It focuses on the specification of the logical device "Merging Unit" and its associated logical node "LLNO." The document provides detailed definitions and attribute values for these logical devices, adhering to the IEC 61850 standard.

**Key Elements:**
- **Logical Device "Merging Unit" (MU):** Defines the attributes and their values for the logical device instance "MU."
- **Logical Node "LLNO":** Defines the attributes and their values for the logical node instance "LLNO."
- **Attribute Names and Values:** Lists the specific attributes and their corresponding values for both the logical device and logical node.
- **Comments:** Provides additional explanations and clarifications for the attributes and their values.
- **Table 4:** Lists the attributes and values for the logical device instance "MU."
- **Table 5:** Lists the attributes and values for the logical node instance "LLNO."

**Extracted Text:**

Page 7 / 31

7 SPECIFICATION OF THE LOGICAL DEVICE "MERGING UNIT"

IEC 61850 does not specify logical devices. Logical devices may be described in SCL and configured through several services of IEC 61850. To reduce the first implementations to a minimum of required services without loosing interoperability, this guideline provides a detailed specification of the logical device merging unit as used within the scope of this guideline.

7.1 Definition of the objects according to IEC 61850-7-2

7.1.1 The logical device instance "MU" ([7-2] 8.1.1)

The attributes of the logical device MU shall have the following values:

Table 4 — Logical device instance "MU"

| Attribute Name | Value | M/O | Comment |
|----------------|-------|-----|---------|
| LDName         | xxxxMUnn | m | xxxx is configurable according to [6], clause 8.4.2 and MUnn is the Attribute Inst of the element LDevice in the IED section of the SCL (nn shall identify the measuring point within the bay) |
| LogicalNode    | LLNO  | m | 1..5 is the attribute Inst of the element LN in the IED section |
| LPHD           |       | m |       |
| InnATCTR1      |       | m | Inn / Inn is the identification of the Sensor; A, B, Cand N are the phase identification. Both values are part of the substation section of the SCL and are used to build the name according to [6], clause 8.4.2 |
| InnBTCTR2      |       | m |       |
| InnCTCTR3      |       | m |       |
| InnNTCTR4      |       | m |       |
| InnATVTR1      |       | m |       |
| InnBTVTR2      |       | m |       |
| InnCTVTR3      |       | m |       |
| InnNTVTR4      |       | m |       |

7.1.2 The logical node instance "LLNO" ([7-2] 9.1.1)

The attributes of the logical node LLNO shall have the following values:

Table 5 — LLNO

| Attribute Name | Value | M/O | Comment |
|----------------|-------|-----|---------|
| LNName         | LLNO  | m | As defined in 61850-7-4 |
| LNRef          | xxxxMUnn/LLNO | m |       |
| Data           | PhsMeas1 | m | As defined in 61850-7-4 |
| DataSet        | PhsMeas1 | m |       |
| MultiCastSampledValueControlBlock | MSVCB01 | c1 | c1 - At least one of the two MulticastSampledValueControlBlock shall be implemented |
| MSVCB02        | c1 |       |

**Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2**

Modification Index: R2-1

Last date of storage: 2004-07-07

---

## Page 8

![Image 1 from page 8](images/DigIF_spec_9-2LE_R2-1_040707-CB_page8_img10_518e9584.png)

**Image Type:** Table and Text

**Description:** The image consists of two tables and accompanying text, which detail the attributes and common data class SAV for a dataset named "PhsMeas1". The text and tables are part of an implementation guideline for a digital interface to instrument transformers, adhering to IEC 61850-9-2 standards.

**Key Elements:**
- **Table 6 – Dataset "PhsMeas1":** Lists the attributes and their corresponding values for the dataset.
- **Table 7 – Common data class SAV:** Details the attributes and their types for the common data class SAV.
- **Text:** Provides additional information about the dataset, the common data class SAV, and the implementation guideline.

**Extracted Text:**

Page 8 / 31

7.1.3 The dataset "PhsMeas1" ({7-2] 11.2.1) The attributes of the dataset shall have the following values:

Table 6 – Dataset "PhsMeas1"

| Attribute Name | Value | Comment |
|----------------|-------|---------|
| DSName         | PhsMeas1 |         |
| DSRef          | xxxxMUnn/LLN0$PhsMeas1 |         |
| DSMemberRef    | InnATCTR1.Amp[MX] InnBTCTR2.Amp[MX] InnCTCTR3.Amp[MX] InnNTCTR4.Amp[MX] UnnATVTR1.Vol[MX] UnnBTVTR2.Vol[MX] UnnCTVTR3.Vol[MX] UnnNTVTR4.Vol[MX] |         |

The common data class SAV used for the data above shall support the following MX attributes:

Table 7 – Common data class SAV

| Attribute Name | Attribute Type | Comment |
|----------------|----------------|---------|
| instMag.i      | INT32          |         |
| q              | Quality        | This includes validity information and test flag and an indication if the value is derived or based on a real sensor |
| sVC.scaleFactor | FLOAT32        | 0.001 for current; 0.01 for voltage |
| sVC.offset      | FLOAT32        | Always 0 |

This implementation guideline defines a fixed scaling. See Appendices C and D for more details.

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2

Modification Index: R2-1
Last date of storage: 2004-07-07

**Analysis:**
The image provides a structured breakdown of the dataset "PhsMeas1" and its attributes, along with the common data class SAV that supports these attributes. The text explains the implementation guidelines and references additional appendices for more detailed information. This is a technical document that is likely used in the context of configuring or understanding the digital interface for instrument transformers in accordance with IEC 61850-9-2 standards.

---

## Page 9

---

## Page 10

---

## Page 11

---

## Page 12

---

## Page 13

![Image 1 from page 13](images/DigIF_spec_9-2LE_R2-1_040707-CB_page13_img16_e91d7b20.png)

**Image Type:** Page from a technical document
**Description:** This image is a page from a technical document, specifically from a section discussing physical devices (IEDs) and open points for future revisions of an implementation guideline. The document appears to be related to the digital interface to instrument transformers using IEC 61850-9-2 standards.

**Key Elements:**
- **Page Number:** Page 13 out of 31
- **Section Title:** PHYSICAL DEVICES
- **Content:** Discusses that no specifications are made with regard to physical devices (IEDs). It mentions that an IED may consist of more than one logical device merging unit sharing the same communication interface.
- **Open Points Section:** Lists topics to be considered in a future revision of the implementation guideline, including:
  - Use of IRIG-B time synchronization as an alternate solution to 1PPS.
  - Improved flagging of synchronization status (e.g., local synch, station-wide synch, global synch).
- **Footer Information:** Includes the title "Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2," the modification index (R2-1), and the last date of storage (2004-07-07).

**Extracted Text:**
```plaintext
Page 13 / 31
8 PHYSICAL DEVICES
No specifications are made with regard to physical devices (IEDs). An IED may consist of more than one logical device merging unit sharing the same communication interface.

9 OPEN POINTS
In a future revision of this implementation guideline, the following topics will be considered:
- Use of IRIG-B time synchronization as alternate solution to 1PPS
- Improved flagging of synchronization status (e.g. local synch / station wide synch / global synch)

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
```

---

## Page 14

---

## Page 15

---

## Page 16

---

## Page 17

---

## Page 18

![Image 1 from page 18](images/DigIF_spec_9-2LE_R2-1_040707-CB_page18_img22_95c0d2d7.png)

**Image Type:** XML Screenshot

**Description:** The image is a screenshot of an XML document, specifically a configuration file for a digital interface to instrument transformers, as per the Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2. The XML document includes definitions for various data types and their attributes, as well as configurations for sampled values and access points.

**Key Elements:**
- **IED (IedType):** Defines the type of IED (Intelligent Electronic Device) and its configuration.
- **AccessPoint:** Specifies the access point for the IED.
- **LDevice:** Lists the logical devices associated with the IED.
- **LNO:** Logical node definitions for the IED.
- **LN:** Logical node instances for the IED.
- **DOType:** Data object types, including their attributes and data attributes (DA).
- **BDA:** Base data attributes for the data objects.
- **DA:** Data attributes for the data objects.
- **SampledValueControl:** Configuration for sampled values.
- **SampledValue:** Sampled value definitions.
- **DataTypeTemplates:** Templates for data types, including their logical nodes and data objects.

**Extracted Text:**
```xml
<SmvOpts refreshTime="false" sampleSynchronized="true" sampleRate="false" security="false" dataRef="false"/>
</SampledValueControl>
</LNO>
<LN InType="9-2LETCTR" InClass="TCTR" inst="1"/>
<LN InType="9-2LETCTR" InClass="TCTR" inst="2"/>
<LN InType="9-2LETCTR" InClass="TCTR" inst="3"/>
<LN InType="9-2LETCTR" InClass="TCTR" inst="4"/>
<LN InType="9-2LETVTR" InClass="TVTR" inst="1"/>
<LN InType="9-2LETVTR" InClass="TVTR" inst="2"/>
<LN InType="9-2LETVTR" InClass="TVTR" inst="3"/>
<LN InType="9-2LETVTR" InClass="TVTR" inst="4"/>
</LDevice>
</Server>
</AccessPoint>
</IED>
<DataTypeTemplates>
<LNodeType id="9-2LELLNO" InClass="LLNO">
<DO name="Mod" type="9-2LEINC"/>
</LNodeType>
<LNodeType id="9-2LETCTR" InClass="TCTR">
<DO name="Amp" type="9-2LESAVAmp"/>
</LNodeType>
<LNodeType id="9-2LETVTR" InClass="TVTR">
<DO name="Vol" type="9-2LESAWVol"/>
</LNodeType>
<DOType id="9-2LESAVAmp" cdc="SAV">
<DA name="instMag" bType="Struct" type="9-2LEAV" fc="MX"/>
<DA name="q" bType="Quality" fc="MX"/>
<DA name="sVC" bType="Struct" type="9-2LEsVCAmp" fc="CF"/>
</DOType>
<DOType id="9-2LESAWVol" cdc="SAV">
<DA name="instMag" bType="Struct" type="9-2LEAV" fc="MX"/>
<DA name="q" bType="Quality" fc="MX"/>
<DA name="sVC" bType="Struct" type="9-2LEsVCVol" fc="CF"/>
</DOType>
<DOType id="9-2LEINC" cdc="INC">
<DA name="ctlVal" fc="CO" bType="INT32"/>
<DA name="stVal" fc="ST" bType="INT32" dchg="true"/>
<DA name="q" fc="ST" bType="Quality" dchg="true"/>
<DA name="t" fc="ST" bType="Timestamp" dchg="true"/>
</DOType>
<DAType id="9-2LEAV">
<BDA name="i" bType="INT32"/>
</DAType>
<DAType id="9-2LEsVCAmp">
<BDA name="scaleFactor" bType="FLOAT32">
<Val>0.001</Val>
</BDA>
<BDA name="offset" bType="FLOAT32">
<Val>0</Val>
</BDA>
</DAType>
<DAType id="9-2LEsVCVol">
<BDA name="scaleFactor" bType="FLOAT3

---

## Page 19

![Image 1 from page 19](images/DigIF_spec_9-2LE_R2-1_040707-CB_page19_img24_56b32ffc.png)

**Image Type:** Page from a technical document
**Description:** This image is a page from a technical document, specifically page 19 out of 31. The document appears to be an implementation guideline for a digital interface to instrument transformers, referencing the International Electrotechnical Commission (IEC) standard 61850-9-2. The document includes a footer with a modification index and a last date of storage.

**Key Elements:**
- Page number: 19 out of 31
- Document title: Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
- Modification Index: R2-1
- Last date of storage: 2004-07-07

**Extracted Text:**
```
Page 19 / 31
</SCL>
Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
```

---

## Page 20

![Image 1 from page 20](images/DigIF_spec_9-2LE_R2-1_040707-CB_page20_img25_46c4b077.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the functions and accuracy classes for measuring and protection systems, specifically related to electrical power systems. The table is structured to provide detailed specifications for various functions, such as metering, reverse power for large generators, and distance protection, across different accuracy classes. It includes numerical values for accuracy limits and other parameters, formatted in a structured grid.

**Key Elements:**
- **Functions:** Metering, Reverse power for large generators, and Distance protection.
- **Accuracy Classes:** 0.1%, 0.2%, 0.25%, 0.5%, 1.0%, 2.0%, 5.0%, 10.0%, and 20.0%.
- **Parameters:** Instantaneous error, Peak limit, and other specific limits for each function and accuracy class.
- **Example Entries:** For example, under the "Metering" function, the accuracy class 0.1% has an instantaneous error limit of 0.1%, and a peak limit of 0.2%.

**Extracted Text:**

```plaintext
Functions using these requirements
Metering
Reverse power for large generators i.e., valve position control before shut down
Wattmetric functions in resonant grounded networks
Disturbance recording
Distance protection without extrapolation at overflow condition

Accuracy classes
0.1%, 0.2%, 0.25%, 0.5%, 1.0%, 2.0%, 5.0%, 10.0%, 20.0%

Example entries:
- Metering: Accuracy class 0.1% has an instantaneous error of 0.1% and a peak limit of 0.2%.
- Reverse power for large generators: Accuracy class 0.1% has an instantaneous error of 0.1% and a peak limit of 0.2%.
- Wattmetric functions in resonant grounded networks: Accuracy class 0.1% has an instantaneous error of 0.1% and a peak limit of 0.2%.
- Disturbance recording: Accuracy class 0.1% has an instantaneous error of 0.1% and a peak limit of 0.2%.
- Distance protection without extrapolation at overflow condition: Accuracy class 0.1% has an instantaneous error of 0.1% and a peak limit of 0.2%.
```

This table is useful for technical documentation and search as it provides a structured format for understanding the accuracy requirements and limits for various functions in electrical power systems.

![Image 2 from page 20](images/DigIF_spec_9-2LE_R2-1_040707-CB_page20_img27_1f9a59b7.png)

**Image Type:** Table

**Description:** The image is a technical table from Appendix C of a document, titled "Tables with Measurement Accuracy Requirements." It provides a summary of technical data from IEC60044-7 and -8, focusing on accuracy and dynamic range data for protection functions. The table includes various functions such as metering, reverse power for large generators, and wattmetric functions in resonant grounded networks. It also details the accuracy requirements for these functions across different classes (Class 0.2S, 0.5S, 0.5P, 1P, 5P, 10P) and at various percentages of the rated current and voltage.

**Key Elements:**
- **Title:** Tables with Measurement Accuracy Requirements
- **Source:** IEC60044-7 and -8
- **Functions:** Metering, Reverse power for large generators, Wattmetric functions in resonant grounded networks, Distance protection without extrapolation at overflow condition, Disturbance recording
- **Accuracy Classes:** 0.2S, 0.5S, 0.5P, 1P, 5P, 10P
- **Accuracy Requirements:** Percentage of rated current and voltage
- **Dynamic Range Data:** Added for protection functions
- **Table Structure:** Columns for accuracy classes and rows for functions and their respective accuracy requirements

**Extracted Text:**

Page 20 / 31

APPENDIX C: TABLES WITH MEASUREMENT ACCURACY REQUIREMENTS

The two tables comprise a summary of technical data out of IEC60044-7 and -8. Accuracy and dynamic range data, which is of interest for protection functions has been added both to the current and the voltage table.

Functions using these requirements:

- Metering
- Reverse power for large generators i.e., valve position control before shut down
- Wattmetric functions in resonant grounded networks
- Distance protection without extrapolation at overflow condition
- Disturbance recording

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2

Modification Index: R2-1
Last date of storage: 2004-07-07

---

## Page 21

![Image 1 from page 21](images/DigIF_spec_9-2LE_R2-1_040707-CB_page21_img28_74e6ceba.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the accuracy classes for voltage transformers, specifically for resonant grounded systems. It details the voltage error percentages for different accuracy classes (Class 1, Class 2, Class 5P, Class 10P) at various percentage levels of the rated voltage. The table also includes functions using these requirements, such as distance protection and phase-to-ground loop protection, and references to standards like IEC 61850-9-2 and IEC 60044-7.

**Key Elements:**

- **Accuracy Classes:** Class 1, Class 2, Class 5P, Class 10P
- **Voltage Error Percentages:** 0.1%, 0.2%, 0.5%, 1.0%, 2.0%, 3.0%, 5.0%, 10.0%, 20.0%, 50.0%, 100.0%
- **Rated Voltage Percentage Levels:** 5%, 10%, 20%, 50%, 100%, 120%, 150%, 190%
- **Functions Using These Requirements:** Distance protection, phase-to-ground loop protection
- **Standards Referenced:** IEC 61850-9-2, IEC 60044-7

**Extracted Text:**

The table is structured to provide voltage error percentages for different accuracy classes at various percentage levels of the rated voltage. It includes the following key points:

- **Accuracy Classes:** Class 1, Class 2, Class 5P, Class 10P
- **Voltage Error Percentages:** 0.1%, 0.2%, 0.5%, 1.0%, 2.0%, 3.0%, 5.0%, 10.0%, 20.0%, 50.0%, 100.0%
- **Rated Voltage Percentage Levels:** 5%, 10%, 20%, 50%, 100%, 120%, 150%, 190%
- **Functions Using These Requirements:** Distance protection, phase-to-ground loop protection
- **Standards Referenced:** IEC 61850-9-2, IEC 60044-7

This table is useful for technical documentation and search as it provides a clear and structured reference for voltage transformer accuracy requirements and their application in protective functions.

![Image 2 from page 21](images/DigIF_spec_9-2LE_R2-1_040707-CB_page21_img30_696a620a.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the functions and requirements for implementing a digital interface to instrument transformers using IEC 61850-9-2. It provides a detailed comparison of accuracy classes and their corresponding requirements for various protection functions, such as distance protection and phase-to-ground loop protection. The table includes numerical values for accuracy classes and their associated percentage errors, as well as specific requirements for different types of transformer connections and grounding configurations.

**Key Elements:**

- **Accuracy Classes:** The table lists accuracy classes ranging from 0.1 to 5.
- **Protection Functions:** The table includes functions like distance protection and phase-to-ground loop protection.
- **Transformer Connections:** It specifies transformer connections such as single-phase, three-phase, and phase-to-ground loop.
- **Grounding Configurations:** The table details grounding configurations like directly grounded networks and resonant grounded networks.
- **IEC 61850-9-2 Standard:** The standard is referenced for the implementation guidelines.
- **IEC 60044-7 Standard:** This standard is referenced for the accuracy classes and their associated requirements.

**Extracted Text:**

Page 21 / 31

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2

Modification Index: R2-1

Last date of storage: 2004-07-07

Functions using these requirements

Distance protection, phase-to-ground loop

IEC 60044-7

Accuracy classes

Transformer connections

Grounding configurations

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

IEC 60044-7

IEC 61850-9-2

---

## Page 22

---

## Page 23

![Image 1 from page 23](images/DigIF_spec_9-2LE_R2-1_040707-CB_page23_img33_e6a9d0d9.png)

**Image Type:** Diagram

**Description:** The image is a nomogram for current, specifically designed for the implementation of digital interfaces to instrument transformers as per the IEC 61850-9-2 standard. It is a graphical tool used to determine the current values for various classes of instrument transformers, such as Class 0.2, Class 0.5, Class 1, and DEF. The nomogram is used to calculate the expected current values for different transformer classes and their corresponding accuracy limits.

**Key Elements:**

- **Current Classes:** The diagram includes different current classes such as Class 0.2, Class 0.5, Class 1, and DEF.
- **Current Values:** The diagram provides a range of current values in milliamps (mA), amperes (A), and kiloamps (kA) for different transformer classes.
- **Accuracy Limits:** The accuracy limits for each current class are indicated, such as Class 0.2 having an accuracy limit factor of 50.
- **Peak and RMS Values:** The diagram includes both peak and RMS values for the currents, with the RMS values being the standard for the IEC 61850-9-2 standard.
- **Transformer Types:** The diagram is applicable to various transformer types, including generator and line current transformers.
- **Nomogram Scale:** The nomogram scale is used to determine the expected current values for the transformer classes based on the accuracy limits and the transformer type.

**Extracted Text:**

Page 23 / 31
1 mA=LSB
1 mA (r.m.s.)
10 mA (r.m.s.)
100 mA (r.m.s.)
1 A (r.m.s.)
10 A (r.m.s.)
100 A (r.m.s.)
1 kA (r.m.s.)
10 kA (r.m.s.)
100 kA (r.m.s.)
1000 kA (r.m.s.)
1'000 kA (r.m.s.)
1'417 kA (peak)
141 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
140 A (peak)
140 A (r.m.s.)
1

---

## Page 24

![Image 1 from page 24](images/DigIF_spec_9-2LE_R2-1_040707-CB_page24_img35_90ecd7b5.png)

**Image Type:** Nomogram

**Description:** The image is a nomogram designed to illustrate the relationship between various voltage levels and their corresponding values in a digital interface for instrument transformers. The nomogram is used to determine the maximum voltage that can be applied to the rated object voltage, ensuring compliance with the IEC 61850-9-2 standard.

**Key Elements:**

- **Voltage Levels:** The nomogram includes various voltage levels such as 10 mV, 100 mV, 1 V, 10 V, 100 V, 1 kV, 10 kV, 100 kV, and 1000 kV (rms).
- **RMS Values:** The values are given in RMS (Root Mean Square) units.
- **Peak Values:** The peak values are also provided for comparison, such as 14.1 kV (peak) and 21475 kV (peak).
- **Class Indicators:** The nomogram includes indicators for different classes (Class 0.2, Class 0.5, Class 1, Class 3P) which define the accuracy and performance of the instrument transformers.
- **Nomogram Lines:** The lines in the nomogram are used to visually determine the required voltage for a given class and accuracy level.
- **Reference Line:** The reference line is marked as "10 kV (r.m.s.) in this example," indicating the standard voltage level used for the example.

**Extracted Text:**

```
Page 24 / 31
10 mV = LSB
10 mV (r.m.s.)
100 mV (r.m.s.)
1 V (r.m.s.)
10 V (r.m.s.)
100 V (r.m.s.)
1 kV (r.m.s.)
10 kV (r.m.s.)
14.1 kV (peak)
100 kV (r.m.s.)
1000 kV (r.m.s.)
10'000 kV (r.m.s.)
21475 kV (peak)

Figure 7 - Nomogram for voltage
Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
```

This nomogram is a useful tool for engineers and technicians to ensure that the voltage applied to instrument transformers meets the specified standards and accuracy levels as defined by the IEC 61850-9-2 standard.

---

## Page 25

![Image 1 from page 25](images/DigIF_spec_9-2LE_R2-1_040707-CB_page25_img37_98f3882e.png)

**Image Type:** Schematic Diagram

**Description:** This image is a schematic diagram from Appendix E of a technical document, specifically focusing on the "Reference Arrow Orientation System." It provides a legend and definitions for various symbols and terms used in electrical diagrams, particularly in the context of instrument transformers and their digital interfaces. The document is part of an implementation guideline for digital interfaces to instrument transformers, adhering to IEC 61850-9-2 standards.

**Key Elements:**
- **Legend:** Defines symbols and their meanings, such as U for Voltage, I for Current, N for Neutral, E for Earth, and indices for phase and sequence.
- **Indices (subscript):** Specifies the phases (A, B, C) and sequence types (1 for positive, 2 for negative, 0 for zero).
- **Terms and Definitions:**
  - DBB: Double Bus Bar arrangement.
  - OHB: One and a Half Breaker arrangement.
  - I_E: Earth (ground) return current.
- **Positive Sense of Arrows (Phasors):** Explains that the positive sense of arrows follows the "load vector system," meaning the voltage-drop across a resistor is in phase with the current.
- **Reference Arrow Orientation System:** Describes the orientation of arrows in electrical diagrams, which is crucial for understanding the phase relationships and sequence components.

**Extracted Text:**
```plaintext
Page 25 / 31
APPENDIX E: REFERENCE ARROW ORIENTATION SYSTEM
Legend
U Voltage
I Current
N Neutral
E Earth (IEC notation) = Ground (ANSI notation)
Indices (subscript)
A Phase A
B Phase B
C Phase C
N Neutral
E Earth = Ground
P Parallel overhead line, if applicable
1 Positive sequence
2 Negative sequence
0 Zero sequence
The positive sense of arrows (phasors) follows the,,load vector system" i.e., the voltage-drop across a resistor is in phase with the current.
DBB Double Bus Bar arrangement
OHB 1½ (One and a Half) Breaker arrangement
I_E Earth (ground) return current
Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
```

This image is a crucial reference for electrical engineers and technicians working with instrument transformers and their digital interfaces, providing a standardized method for interpreting electrical diagrams and ensuring consistency in design and implementation.

---

## Page 26

![Image 1 from page 26](images/DigIF_spec_9-2LE_R2-1_040707-CB_page26_img39_e48b41ad.png)

**Image Type:** Schematic Diagram

**Description:** This image is a schematic diagram illustrating a generic three-phase electrical system, including the representation of voltages and currents. The diagram is labeled as "Figure 8 - Generic three phase system including voltages and currents" and is part of an implementation guideline for digital interfaces to instrument transformers using IEC 61850-9-2.

**Key Elements:**
- **Three-phase system:** The diagram shows three phases (A, B, and C) with their respective voltages (U_A, U_B, U_C) and currents (I_A, I_B, I_C).
- **Neutral point (N):** The neutral point is indicated in the diagram, which is common in three-phase systems.
- **Overhead line (Parallel Line):** The diagram includes an overhead line, which is labeled as "Parallel Line (overhead line)."
- **Feeder under consideration:** The feeder under consideration is shown with arrows indicating the direction of current flow.
- **Zero-sequence voltage (U_0):** The zero-sequence voltage is represented in the diagram, which is used for mutual zero-sequence compensation.
- **Zero-sequence current (I_0):** The zero-sequence current is also shown, which is used for mutual zero-sequence compensation and distance to fault locators.
- **Voltage and current relationships:** The diagram includes equations for the relationships between the voltages and currents, such as \(3U_0 = U_{AE} + U_{BE} + U_{CE}\) and \(3I_0 = I_A + I_B + I_C\).

**Extracted Text:**
Page 26 / 31
Parallel Line (overhead line)
Feeder under consideration
I_A
I_B
I_C
U_A
U_B
U_C
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_0
U_

---

## Page 27

![Image 1 from page 27](images/DigIF_spec_9-2LE_R2-1_040707-CB_page27_img41_59840242.png)

**Image Type:** Schematic Diagram

**Description:** This image is a schematic diagram illustrating the current flow in a double bus bar (DBB) arrangement, commonly used in electrical power systems. The diagram shows the connection points and flow of currents (I_A, I_B, I_C, and I_0) through various components such as bus bars, circuit breakers (QA1), and a tertiary transformer. The HV (high voltage) and LV (low voltage) sections are depicted, with arrows indicating the direction of current flow.

**Key Elements:**
- **Bus 1 and Bus 2:** Represent the two main bus bars in the DBB arrangement.
- **QA1:** Likely a circuit breaker or switch used to disconnect the bus bar.
- **BZ1:** A tertiary transformer, which is part of the DBB system.
- **HV (High Voltage) and LV (Low Voltage):** Indicate the voltage levels at different points in the system.
- **Currents (I_A, I_B, I_C, I_0):** Represent the phase and zero-sequence currents flowing through the system.
- **P1 and P2:** Points of connection or measurement within the system.
- **Tertiary Transformer:** A transformer used to step down the voltage from the high voltage (HV) to the low voltage (LV) section.

**Extracted Text:**
Page 27 / 31
Figure 9 - Currents in DBB (double bus bar) arrangement
Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07

---

## Page 28

![Image 1 from page 28](images/DigIF_spec_9-2LE_R2-1_040707-CB_page28_img43_bcac2649.png)

**Image Type:** Schematic Diagram

**Description:** The image is a detailed electrical schematic diagram illustrating the current flow and protection mechanisms in a one-and-a-half-breaker (OHB) arrangement. It shows the connection of two feeders (Feeder 1 and Feeder 2) to a bus system (Bus 1 and Bus 2) and the associated protection relays (BZ11, BZ21, BZ31) and circuit breakers (QA11, QA21, QA31). The diagram includes annotations for the currents (I_A11, I_B11, I_C11, I_A31, I_B31, I_C31, I_A21, I_B21, I_C21) and their respective phases (A, B, C) and zero-sequence (3L_011, 3L_031, 3L_021).

**Key Elements:**
- **Bus System:** Two buses (Bus 1 and Bus 2) are shown at the top of the diagram.
- **Feeders:** Two feeders (Feeder 1 and Feeder 2) are connected to the buses.
- **Protection Relays:** Three protection relays (BZ11, BZ21, BZ31) are shown, each associated with specific phases and zero-sequence currents.
- **Circuit Breakers:** Three circuit breakers (QA11, QA21, QA31) are shown, each associated with specific phases and zero-sequence currents.
- **Current Phases and Zero-Sequence:** The currents are labeled as I_A11, I_B11, I_C11, I_A31, I_B31, I_C31, I_A21, I_B21, I_C21, and their zero-sequence counterparts (3L_011, 3L_031, 3L_021).
- **Sum and Difference of Currents:** The diagram includes the sum and difference of the currents for each phase and zero-sequence.
- **HV and LV Sections:** The diagram is divided into high voltage (HV) and low voltage (LV) sections, indicating the different voltage levels.

**Extracted Text:**
Page 28 / 31
Bus 1
Dif of two currents
QA11
P1
P2
Feeder 1
QA31
P1
P2
Bus 2
Sum of two currents
Feeder 2
QA21
HV
Tertiary
I_A11, I_B11, I_C11, (3L_011)
I_A31, I_B31, I_C31, (3L_031)
I_A21, I_B21, I_C21, (3L_021)
Figure 10 - Currents in OHB (one and a half breaker) arrangement
Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07

---

## Page 29

![Image 1 from page 29](images/DigIF_spec_9-2LE_R2-1_040707-CB_page29_img45_a5ec0101.png)

**Image Type:** Schematic Diagram

**Description:** The image is a schematic diagram illustrating the connections between conventional equipment and current transformers (CTs). It shows the reference arrow orientation system for conventional CTs with a CT star-point (neutral) on the object side. The diagram includes the primary current (I_A, I_B, I_C) and the current in the protection relay (I_N), which is shown to be in phase with the primary current. The diagram also highlights the relationship between the primary current and the current in the protection relay, indicating that IN, as used with conventional CTs, results in I_N = -3 I_0.

**Key Elements:**
- Conventional equipment and CTs connections
- Neutral of the CTs formed on the object side
- Primary current (I_A, I_B, I_C)
- Current in the protection relay (I_N)
- Reference arrow orientation system
- Phase relationship between primary current and protection relay current
- Mixed-mode configurations (digital data streams from NCITs and inputs from conventional CTs and VTs)
- Diagram illustrating the relationship I_N = -3 I_0

**Extracted Text:**
Page 29 / 31
Figure 11 shows the connections between conventional equipment and CT’s. The neutral of the CT's is formed on object-side. The primary current and the current in the protection relay are "in phase". The arrow orientation is in line with the system used for NCIT’s (as defined in IEC 60044-8, clause 11.1.3). The drawing shall help to co-ordinate mixed mode configurations (mixed-mode uses digital data streams from NCIT’s and inputs from conventional CT’s and VT’s). Furthermore the diagram shall illustrate that IN, as it is used with conventional CT's, results in I_N = - 3 I_0.

Figure 11 — Reference arrow orientation system, conventional CTs with CT star-point (neutral) on object side

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2

Modification Index: R2-1
Last date of storage: 2004-07-07

---

## Page 30

![Image 1 from page 30](images/DigIF_spec_9-2LE_R2-1_040707-CB_page30_img47_d6da5adf.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating an alternative connection for conventional current transformers (CTs) used in electrical systems. It shows the reference arrow orientation system for CTs with a star-point neutral on the bus-side. The diagram includes a representation of the CTs, the bus-side neutral, and the secondary equipment connections, highlighting the inversion of the secondary equipment's arrow orientation.

**Key Elements:**
- **CTs (Current Transformers):** Represented by the circular shapes at the top of the diagram.
- **Bus-side Neutral (N):** Indicated by the vertical line with a neutral symbol.
- **Secondary Equipment Arrow Orientation:** The arrows are shown in counter-phase, indicating that the secondary equipment is inverted.
- **Protection Unit:** Represented by the rectangular shape labeled "Protection."
- **Current Phases (A, B, C):** Indicated by the arrows pointing in different directions, representing the primary and secondary current flows.
- **Neutral Current (I_N):** Represented by the arrow pointing to the left, labeled as "I_N = -3 I_0."

**Extracted Text:**
Page 30 / 31
Figure 12 shows an alternative connection for conventional equipment as used by several vendors i.e., the neutral of the CT’s is formed on bus-side. In this case the arrow orientation for the secondary equipment is inverted (The primary current and the current in the protection unit are “in counter phase”. This must be considered for directional- and differential- functions. To ease adaptation to the two conventions, the IEC61850-9 inputs should preferably be selectable: direct or inverted.

Figure 12 - Reference arrow orientation system, conventional CTs with CT star-point (neutral) on bus-side

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07

This technical image is part of a larger document providing guidelines for the implementation of digital interfaces to instrument transformers, specifically focusing on the IEC 61850-9-2 standard. The diagram and accompanying text are crucial for understanding the correct configuration and orientation of CTs in electrical systems, particularly when dealing with directional and differential protection functions.

---

## Page 31

![Image 1 from page 31](images/DigIF_spec_9-2LE_R2-1_040707-CB_page31_img49_281478cb.png)

**Image Type:** Table

**Description:** The image is a table from a technical document, specifically from page 31 of a larger document. The table is titled "CHANGE INFORMATION" and contains data related to changes in a document or system. The table includes columns for Date, Change Index, Document State, Author, and Remarks.

**Key Elements:**
- **Date:** Indicates the date of the change.
- **Change Index:** Identifies the specific change being documented.
- **Document State:** Indicates whether the change is "Released" or not.
- **Author:** Names the person who made the change.
- **Remarks:** Provides additional information about the change, such as the context or details of the modification.

**Extracted Text:**

```
Page 31 / 31
CHANGE INFORMATION

Date | Change Index | Document State | Author | Remarks
04-03-01 | 2.0a | Released | Ch. Brunner | Version released at UCA usersgroup meeting
04-07-07 | 2.1 | Released | Ch. Brunner | Modifications in Figure 4 (Length of ConfRev) and 5 (Quality fields)

Implementation Guideline for Digital Interface to Instrument Transformers using IEC 61850-9-2
Modification Index: R2-1
Last date of storage: 2004-07-07
```

---

