#!/usr/bin/env python3
"""
Demo script to test OCR text extraction from images in PDFs.
This demonstrates the enhanced functionality with no content duplication.
"""

import sys
import os
from pathlib import Path
import importlib.util

# Import the enhanced processor
try:
    spec = importlib.util.spec_from_file_location("pdf_processor", "pdf-to-markdown-processor.py")
    if spec and spec.loader:
        pdf_processor = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(pdf_processor)
        EnhancedPDFProcessor = pdf_processor.EnhancedPDFProcessor
        ProcessingConfig = pdf_processor.ProcessingConfig
    else:
        raise ImportError("Could not load pdf-to-markdown-processor.py")
except ImportError as e:
    print(f"Error: Could not import EnhancedPDFProcessor: {e}")
    sys.exit(1)

def test_image_ocr_extraction():
    """Test OCR text extraction from images with different configurations."""
    
    print("🧪 Testing Image OCR Text Extraction")
    print("=" * 50)
    
    # Test configurations
    configs = [
        {
            "name": "OCR Enabled (Default)",
            "config": ProcessingConfig(
                extract_text_from_images=True,
                use_gpu_ocr=True,
                ocr_engine="tesseract",
                ocr_confidence_threshold=0.5,
                text_overlap_threshold=0.7
            )
        },
        {
            "name": "OCR Disabled",
            "config": ProcessingConfig(
                extract_text_from_images=False,
                use_gpu_ocr=True,
                ocr_engine="tesseract"
            )
        },
        {
            "name": "High Confidence OCR",
            "config": ProcessingConfig(
                extract_text_from_images=True,
                use_gpu_ocr=True,
                ocr_engine="tesseract",
                ocr_confidence_threshold=0.8,
                text_overlap_threshold=0.9
            )
        }
    ]
    
    # Find a test PDF
    pdf_files = list(Path("pdfs").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in 'pdfs' directory")
        return
    
    test_pdf = pdf_files[0]  # Use first PDF for testing
    print(f"📄 Testing with: {test_pdf.name}")
    print()
    
    for i, test_config in enumerate(configs, 1):
        print(f"🔍 Test {i}: {test_config['name']}")
        print("-" * 30)
        
        try:
            # Create output directory for this test
            output_dir = f"test_image_ocr_{i}"
            
            # Initialize processor
            processor = EnhancedPDFProcessor(
                input_dir="pdfs",
                output_dir=output_dir,
                config=test_config['config']
            )
            
            # Process the PDF
            success = processor.process_pdf(test_pdf)
            
            if success:
                # Check the output
                output_file = Path(output_dir) / f"{test_pdf.stem}.md"
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Count OCR text instances
                    ocr_text_count = content.count("**Text from image:**")
                    total_lines = len(content.splitlines())
                    
                    print(f"✅ Success!")
                    print(f"   📊 Total lines: {total_lines}")
                    print(f"   🖼️  OCR text extractions: {ocr_text_count}")
                    print(f"   📁 Output: {output_file}")
                else:
                    print("❌ Output file not found")
            else:
                print("❌ Processing failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
    
    print("🎯 Key Features Demonstrated:")
    print("• OCR text extraction from images")
    print("• Configurable confidence thresholds")
    print("• Intelligent duplicate content detection")
    print("• Clean markdown output optimized for RAG")
    print("• Preserved image references with extracted text")

def analyze_ocr_quality():
    """Analyze the quality of OCR text extraction."""
    
    print("\n🔍 OCR Quality Analysis")
    print("=" * 30)
    
    # Check if we have test output
    test_dirs = [d for d in Path(".").iterdir() if d.is_dir() and d.name.startswith("test_image_ocr_")]
    
    if not test_dirs:
        print("❌ No test output directories found. Run test_image_ocr_extraction() first.")
        return
    
    for test_dir in test_dirs:
        print(f"\n📁 Analyzing: {test_dir.name}")
        
        md_files = list(test_dir.glob("*.md"))
        if not md_files:
            continue
            
        md_file = md_files[0]
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract OCR text samples
        ocr_sections = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "**Text from image:**" in line:
                ocr_text = line.replace("**Text from image:**", "").strip()
                ocr_sections.append({
                    'line': i + 1,
                    'text': ocr_text[:100] + "..." if len(ocr_text) > 100 else ocr_text,
                    'length': len(ocr_text)
                })
        
        print(f"   🖼️  OCR extractions found: {len(ocr_sections)}")
        
        if ocr_sections:
            avg_length = sum(s['length'] for s in ocr_sections) / len(ocr_sections)
            print(f"   📏 Average OCR text length: {avg_length:.1f} characters")
            
            # Show a few samples
            print("   📝 Sample extractions:")
            for i, sample in enumerate(ocr_sections[:3], 1):
                print(f"      {i}. Line {sample['line']}: {sample['text']}")

if __name__ == "__main__":
    print("🚀 Enhanced PDF OCR Text Extraction Demo")
    print("=" * 50)
    
    # Run the tests
    test_image_ocr_extraction()
    analyze_ocr_quality()
    
    print("\n✅ Demo completed!")
    print("\n💡 The enhanced system now:")
    print("   • Extracts text from images using OCR")
    print("   • Maintains original formatting where possible")
    print("   • Prevents content duplication intelligently")
    print("   • Optimizes output for RAG applications")
    print("   • Provides configurable quality thresholds")
