#!/usr/bin/env python3
"""
Quick test to verify OCR filtering improvements on a single page.
"""

import sys
from pathlib import Path
import fitz  # PyMuPDF

# Import the processor
import importlib.util
spec = importlib.util.spec_from_file_location("pdf_processor", "pdf-to-markdown-processor.py")
pdf_processor = importlib.util.module_from_spec(spec)
spec.loader.exec_module(pdf_processor)

EnhancedPDFProcessor = pdf_processor.EnhancedPDFProcessor
ProcessingConfig = pdf_processor.ProcessingConfig
load_config_from_env = pdf_processor.load_config_from_env
TextOverlapDetector = pdf_processor.TextOverlapDetector

def test_single_page():
    """Test OCR filtering on a single page."""
    print("Testing OCR filtering on a single page from DNP3 PDF...")
    
    # Load configuration
    config = load_config_from_env()
    
    # Set up paths
    input_dir = Path("./pdfs-dnp3")
    pdf_path = input_dir / "1815-2012.pdf"
    
    if not pdf_path.exists():
        print(f"PDF not found: {pdf_path}")
        return
    
    # Initialize processor
    processor = EnhancedPDFProcessor(input_dir, Path("./test_single_page"), config)
    
    try:
        # Open the PDF and extract text from a specific page (page 51 where we saw the issue)
        pdf_document = fitz.open(str(pdf_path))
        
        # Test pages with multi-column layout issues
        test_pages = [18, 19, 20]  # Pages around the DNP3 Overview section

        for page_num in test_pages:
            if page_num >= len(pdf_document):
                continue

            print(f"\n{'='*60}")
            print(f"TESTING PAGE {page_num + 1}")
            print('='*60)
            
        page = pdf_document[page_num]
        
        print(f"Testing page {page_num + 1} of {pdf_path.name}")
        
        # Extract native text using both methods for comparison
        old_native_text = page.get_text()
        new_native_text = processor._extract_text_with_reading_order(page)

        print(f"Old native text length: {len(old_native_text)} characters")
        print(f"New native text length: {len(new_native_text)} characters")

        # Show comparison of first 300 characters
        print(f"\nOLD METHOD (first 300 chars):")
        print("-" * 50)
        print(repr(old_native_text[:300]))
        print("-" * 50)

        print(f"\nNEW METHOD (first 300 chars):")
        print("-" * 50)
        print(repr(new_native_text[:300]))
        print("-" * 50)

        native_text = new_native_text  # Use the improved method
        
        # Extract OCR text
        ocr_text = ""
        if processor.ocr_engine:
            ocr_text = processor._extract_ocr_text_from_page(page, page_num)
        print(f"OCR text length: {len(ocr_text)} characters")
        
        # Test text quality evaluation
        native_quality = TextOverlapDetector._evaluate_text_quality(native_text)
        ocr_quality = TextOverlapDetector._evaluate_text_quality(ocr_text)

        print(f"Native text quality score: {native_quality:.2f}")
        print(f"OCR text quality score: {ocr_quality:.2f}")

        # Test text merging
        primary_text, supplementary_text = TextOverlapDetector.merge_texts(
            native_text,
            ocr_text,
            config.text_overlap_threshold
        )

        print(f"Primary text length: {len(primary_text)} characters")
        print(f"Supplementary text length: {len(supplementary_text)} characters")
        
        # Test our new supplementary text filtering
        if supplementary_text.strip():
            is_valuable = processor._is_ocr_supplementary_valuable(primary_text, supplementary_text)
            print(f"Is OCR supplementary text valuable? {is_valuable}")
            
            if not is_valuable:
                print("✅ OCR supplementary text filtered out!")
                supplementary_text = ""
        
        # Show the results
        print("\n" + "="*60)
        print("FINAL TEXT OUTPUT:")
        print("="*60)
        
        if primary_text.strip():
            print("PRIMARY TEXT (first 500 chars):")
            print("-" * 40)
            print(primary_text[:500])
            print("-" * 40)
        
        if supplementary_text.strip():
            print("\nSUPPLEMENTARY OCR TEXT (first 500 chars):")
            print("-" * 40)
            print(supplementary_text[:500])
            print("-" * 40)
        else:
            print("\n✅ NO SUPPLEMENTARY OCR TEXT - Success!")
        
        pdf_document.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_single_page()
