<?xml version="1.0" ?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1224 1584" width="1224" height="1584">
  <g id="background"/>
  <g id="shapes" stroke="black" stroke-width="1" fill="none">
    <line x1="0" y1="96" x2="999" y2="97"/>
    <line x1="0" y1="1409" x2="999" y2="1410"/>
    <line x1="0" y1="84" x2="999" y2="85"/>
    <line x1="0" y1="1398" x2="999" y2="1399"/>
    <line x1="0" y1="1421" x2="999" y2="1422"/>
    <line x1="0" y1="1429" x2="999" y2="1430"/>
    <line x1="285" y1="1000" x2="285" y2="0"/>
    <line x1="283" y1="1000" x2="283" y2="0"/>
    <line x1="303" y1="1000" x2="303" y2="0"/>
    <line x1="0" y1="119" x2="999" y2="120"/>
    <line x1="316" y1="1011" x2="386" y2="0"/>
    <line x1="384" y1="1000" x2="384" y2="0"/>
    <line x1="307" y1="1011" x2="377" y2="0"/>
    <line x1="322" y1="1005" x2="357" y2="0"/>
    <line x1="298" y1="1036" x2="507" y2="0"/>
    <line x1="309" y1="1017" x2="413" y2="0"/>
    <line x1="299" y1="1030" x2="473" y2="0"/>
    <line x1="318" y1="1011" x2="388" y2="0"/>
    <line x1="290" y1="1022" x2="429" y2="0"/>
    <line x1="307" y1="1017" x2="411" y2="0"/>
    <line x1="309" y1="1011" x2="379" y2="0"/>
    <line x1="305" y1="1017" x2="409" y2="0"/>
    <line x1="0" y1="121" x2="999" y2="122"/>
    <line x1="307" y1="1023" x2="446" y2="0"/>
    <line x1="277" y1="1000" x2="277" y2="0"/>
    <line x1="301" y1="1023" x2="440" y2="0"/>
    <line x1="297" y1="1029" x2="471" y2="0"/>
    <line x1="311" y1="1017" x2="415" y2="0"/>
    <line x1="303" y1="1023" x2="442" y2="0"/>
    <line x1="294" y1="1016" x2="398" y2="0"/>
    <line x1="260" y1="0" x2="295" y2="994"/>
    <line x1="310" y1="1005" x2="345" y2="0"/>
    <line x1="302" y1="1017" x2="406" y2="0"/>
    <line x1="303" y1="1037" x2="512" y2="0"/>
    <line x1="0" y1="1423" x2="999" y2="1424"/>
    <line x1="301" y1="1030" x2="475" y2="0"/>
    <line x1="324" y1="1005" x2="359" y2="0"/>
    <line x1="288" y1="1029" x2="462" y2="0"/>
    <line x1="286" y1="1028" x2="460" y2="0"/>
    <line x1="313" y1="1017" x2="417" y2="0"/>
    <line x1="299" y1="1023" x2="438" y2="0"/>
    <line x1="301" y1="1000" x2="301" y2="0"/>
    <line x1="306" y1="1030" x2="480" y2="0"/>
    <line x1="313" y1="1031" x2="487" y2="0"/>
    <line x1="304" y1="1030" x2="478" y2="0"/>
    <line x1="308" y1="1030" x2="482" y2="0"/>
    <line x1="305" y1="1037" x2="514" y2="0"/>
    <line x1="326" y1="1000" x2="326" y2="0"/>
    <line x1="287" y1="1035" x2="496" y2="0"/>
    <line x1="296" y1="1036" x2="505" y2="0"/>
    <rect x="174" y="1399" width="130" height="127"/>
  </g>
  <g id="text" font-family="Arial, sans-serif" font-size="12" fill="black">
    <text x="557" y="81">Page</text>
    <text x="607" y="77">18</text>
    <text x="633" y="80">/</text>
    <text x="648" y="77">31</text>
    <text x="391" y="134">&lt;SmvOpts</text>
    <text x="493" y="130">_refreshTime=&quot;false”</text>
    <text x="897" y="134">sampleRate=&quot;false”</text>
    <text x="254" y="151">&quot;false&quot;</text>
    <text x="304" y="154">dataRef=&quot;false&quot;/&gt;</text>
    <text x="361" y="175">&lt;/SampledValueControl&gt;</text>
    <text x="331" y="195">&lt;/LNO&gt;</text>
    <text x="331" y="212">&lt;LN</text>
    <text x="368" y="216">InType=&quot;9-2LETCTR&quot;</text>
    <text x="533" y="212">InClass=&quot;TCTR&quot;</text>
    <text x="656" y="215">inst=&quot;1&quot;/&gt;</text>
    <text x="447" y="233">-2LETCTR&quot;</text>
    <text x="533" y="233">InClass=&quot;TCTR&quot;</text>
    <text x="656" y="236">inst=&quot;2&quot;/&gt;</text>
    <text x="447" y="253">-2LETCTR&quot;</text>
    <text x="533" y="253">InClass=&quot;TCTR&quot;</text>
    <text x="656" y="256">inst=&quot;3&quot;/&gt;</text>
    <text x="447" y="274">-2LETCTR&quot;</text>
    <text x="533" y="274">InClass=&quot;TCTR&quot;</text>
    <text x="656" y="277">inst=&quot;4&quot;/&gt;</text>
    <text x="447" y="294">-2LETVTR&quot;</text>
    <text x="533" y="294">InClass=&quot;TVTR&quot;</text>
    <text x="656" y="283">i</text>
    <text x="714" y="297">&quot;I&gt;</text>
    <text x="533" y="315">InClass=&quot;TVTR&quot;</text>
    <text x="714" y="318">&quot;I&gt;</text>
    <text x="533" y="336">InClass=&quot;TVTI</text>
    <text x="714" y="339">&quot;I&gt;</text>
    <text x="533" y="356">InClass=&quot;TVTR&quot;</text>
    <text x="714" y="359">&quot;I&gt;</text>
    <text x="301" y="380">&lt;/LDevice&gt;</text>
    <text x="271" y="400">&lt;/Server&gt;</text>
    <text x="241" y="421">&lt;/AccessPoint&gt;</text>
    <text x="211" y="441">&lt;/IED&gt;</text>
    <text x="211" y="462">&lt;DataTypeTemplates&gt;</text>
    <text x="241" y="482">&lt;LNodeType</text>
    <text x="374" y="479">id=&quot;9-2LELLNO&quot;</text>
    <text x="468" y="479">InClass=&quot;LLNO&quot;&gt;</text>
    <text x="271" y="500">&lt;DO</text>
    <text x="312" y="500">name=&quot;Mod&quot;</text>
    <text x="241" y="523">&lt;/LNodeType&gt;</text>
    <text x="241" y="544">&lt;LNodeType</text>
    <text x="343" y="541">id=&quot;9-2LETCTR&quot;</text>
    <text x="471" y="541">InClass=&quot;TCTR&quot;&gt;</text>
    <text x="271" y="561">&lt;DO</text>
    <text x="312" y="565">name=&quot;Amp&quot;</text>
    <text x="418" y="565">type=&quot;9-2LESAVAmp&quot;/&gt;</text>
    <text x="241" y="585">&lt;/LNodeType&gt;</text>
    <text x="241" y="606">&lt;LNodeType</text>
    <text x="374" y="602">id=&quot;9-2LETVTR&quot;</text>
    <text x="471" y="602">InClass=&quot;TVTR&quot;&gt;</text>
    <text x="271" y="623">&lt;DO</text>
    <text x="312" y="623">nam</text>
    <text x="241" y="647">&lt;/LNodeType&gt;</text>
    <text x="241" y="667">&lt;DOType</text>
    <text x="319" y="664">id=</text>
    <text x="372" y="623">'Vol&quot;</text>
    <text x="407" y="627">type=&quot;9-2LESAVVol&quot;/&gt;</text>
    <text x="360" y="667">-2LESAVAmp&quot;</text>
    <text x="469" y="664">cdc=&quot;SAV&quot;&gt;</text>
    <text x="375" y="688">instMag&quot;</text>
    <text x="441" y="688">bType=&quot;Struct&quot;</text>
    <text x="562" y="688">type=&quot;9-2LEAV&quot;</text>
    <text x="689" y="687">fc=&quot;MX&quot;/&gt;</text>
    <text x="457" y="708">‘Quality&quot;</text>
    <text x="522" y="708">fc=&quot;MX&quot;/&gt;</text>
    <text x="241" y="749">&lt;/DOType&gt;</text>
    <text x="241" y="770">&lt;DOType</text>
    <text x="319" y="766">id=</text>
    <text x="241" y="852">&lt;/DOType&gt;</text>
    <text x="241" y="872">&lt;DOType</text>
    <text x="319" y="869">id=</text>
    <text x="271" y="890">&lt;DA</text>
    <text x="310" y="890">nam</text>
    <text x="271" y="910">&lt;DA</text>
    <text x="310" y="910">name='</text>
    <text x="549" y="893">&quot;INT32&quot;/&gt;</text>
    <text x="555" y="910">INT32&quot;</text>
    <text x="607" y="913">dchg=&quot;true&quot;/&gt;</text>
    <text x="420" y="941">'ST&quot;</text>
    <text x="457" y="934">bType=&quot;Quality&quot;</text>
    <text x="586" y="934">dchg=&quot;true&quot;/&gt;</text>
    <text x="421" y="951">ST&quot;</text>
    <text x="453" y="955">bType=&quot;Timestamp&quot;</text>
    <text x="613" y="955">dchg=&quot;true&quot;/&gt;</text>
    <text x="241" y="975">&lt;/DOType&gt;</text>
    <text x="241" y="995">&lt;DAType</text>
    <text x="317" y="992">id=&quot;9-2LEAV&quot;&gt;</text>
    <text x="271" y="1013">&lt;BDA</text>
    <text x="320" y="1013">name=&quot;i&quot;</text>
    <text x="397" y="1017">bType=&quot;INT32&quot;/&gt;</text>
    <text x="241" y="1036">&lt;/DAType&gt;</text>
    <text x="241" y="1057">&lt;DAType</text>
    <text x="271" y="1074">&lt;BDA</text>
    <text x="320" y="1074">name=&quot;scaleFactor&quot;</text>
    <text x="477" y="1078">bType=&quot;FLOAT32&quot;&gt;</text>
    <text x="301" y="1098">&lt;Val&gt;0.001&lt;/Val&gt;</text>
    <text x="271" y="1118">&lt;/BDA&gt;</text>
    <text x="271" y="1136">&lt;BDA</text>
    <text x="320" y="1136">name=&quot;offset&quot;</text>
    <text x="435" y="1140">bType=&quot;FLOAT32&quot;&gt;</text>
    <text x="301" y="1159">&lt;Val&gt;0&lt;/Val&gt;</text>
    <text x="271" y="1180">&lt;/BDA&gt;</text>
    <text x="241" y="1201">&lt;/DAType&gt;</text>
    <text x="241" y="1221">&lt;DAType</text>
    <text x="317" y="1218">id=&quot;9-2LEsVCVol&quot;&gt;</text>
    <text x="271" y="1238">&lt;BDA</text>
    <text x="320" y="1238">name=&quot;scaleFactor&quot;</text>
    <text x="477" y="1242">bType=&quot;FLOAT32&quot;&gt;</text>
    <text x="301" y="1262">&lt;Val&gt;0.01&lt;/Val&gt;</text>
    <text x="271" y="1282">&lt;/BDA&gt;</text>
    <text x="271" y="1300">&lt;BDA</text>
    <text x="320" y="1300">name=&quot;offset&quot;</text>
    <text x="435" y="1304">bType=&quot;FLOAT32&quot;&gt;</text>
    <text x="301" y="1323">&lt;Val&gt;0&lt;/Val&gt;</text>
    <text x="271" y="1344">&lt;/BDA&gt;</text>
    <text x="241" y="1365">&lt;/DAType&gt;</text>
    <text x="211" y="1386">&lt;/DataTypeTemplates&gt;</text>
    <text x="358" y="1434">Implementation</text>
    <text x="493" y="1431">Guideline</text>
    <text x="573" y="1431">for</text>
    <text x="602" y="1434">Digital</text>
    <text x="659" y="1431">Interface</text>
    <text x="738" y="1431">to</text>
    <text x="760" y="1431">Instrument</text>
    <text x="856" y="1431">Transformers</text>
    <text x="969" y="1434">using</text>
    <text x="1017" y="1431">IEC</text>
    <text x="960" y="1451">61850-9-2</text>
    <text x="872" y="1489">Modification</text>
    <text x="961" y="1489">Index:</text>
    <text x="1022" y="1489">R2-1</text>
    <text x="812" y="1509">Last</text>
    <text x="845" y="1509">date</text>
    <text x="881" y="1509">of</text>
    <text x="900" y="1512">storage:</text>
    <text x="964" y="1509">2004-07-07</text>
  </g>
</svg>
