#!/usr/bin/env python3
"""
Test script to verify improved filtering is working better.
This will reprocess just the first PDF with our improved filtering logic.
"""

import os
import sys
from pathlib import Path

# Import the processor
import importlib.util
spec = importlib.util.spec_from_file_location("pdf_processor", "pdf-to-markdown-processor.py")
pdf_processor = importlib.util.module_from_spec(spec)
spec.loader.exec_module(pdf_processor)

EnhancedPDFProcessor = pdf_processor.EnhancedPDFProcessor
ProcessingConfig = pdf_processor.ProcessingConfig
load_config_from_env = pdf_processor.load_config_from_env

def test_improved_filtering():
    """Test the improved OCR quality filtering on a single PDF."""
    print("Testing improved OCR quality filtering on DNP3 PDF...")

    # Load configuration
    config = load_config_from_env()

    # Set up paths
    input_dir = Path("./pdfs-dnp3")
    output_dir = Path("./test_ocr_quality")
    output_dir.mkdir(exist_ok=True)

    # Initialize processor
    processor = EnhancedPDFProcessor(input_dir, output_dir, config)

    # Process just the DNP3 PDF
    pdf_path = input_dir / "1815-2012.pdf"
    
    if not pdf_path.exists():
        print(f"PDF not found: {pdf_path}")
        return
    
    print(f"Processing: {pdf_path}")
    success = processor.process_pdf(pdf_path)
    
    if success:
        print("✅ Processing completed successfully!")
        
        # Check the output
        output_file = output_dir / f"{pdf_path.stem}.md"
        if output_file.exists():
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📊 Output Statistics:")
            print(f"   - Total characters: {len(content):,}")
            print(f"   - Total lines: {len(content.splitlines()):,}")
            
            # Count sections
            lines = content.splitlines()
            page_sections = [line for line in lines if line.startswith("## Page ")]
            empty_sections = 0
            
            for i, line in enumerate(lines):
                if line.startswith("## Page "):
                    # Check if the next few lines are empty or just "---"
                    next_lines = lines[i+1:i+5]
                    content_lines = [l for l in next_lines if l.strip() and l.strip() != "---"]
                    if not content_lines:
                        empty_sections += 1
            
            print(f"   - Page sections found: {len(page_sections)}")
            print(f"   - Empty page sections: {empty_sections}")
            print(f"   - Sections with content: {len(page_sections) - empty_sections}")
            
            # Show first few lines of actual content
            print(f"\n📄 First 10 lines of content:")
            for i, line in enumerate(content.splitlines()[:10]):
                print(f"   {i+1:2d}: {line}")
                
        else:
            print(f"❌ Output file not found: {output_file}")
    else:
        print("❌ Processing failed!")

if __name__ == "__main__":
    test_improved_filtering()
