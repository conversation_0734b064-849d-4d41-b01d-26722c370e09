#!/usr/bin/env python3
"""
Analyze a specific image for SVG conversion potential.
This script examines the image characteristics to determine SVG suitability.
"""

import sys
from PIL import Image
import numpy as np
from pathlib import Path

def analyze_image_for_svg_conversion(image_path: str):
    """Analyze an image to determine its SVG conversion potential."""
    
    print(f"🔍 Analyzing Image for SVG Conversion")
    print(f"📁 Image: {image_path}")
    print("=" * 60)
    
    try:
        # Load the image
        img = Image.open(image_path)
        
        # Basic image properties
        print(f"📏 Dimensions: {img.size[0]} x {img.size[1]} pixels")
        print(f"🎨 Mode: {img.mode}")
        print(f"📊 Format: {img.format}")
        
        # Convert to RGB if needed for analysis
        if img.mode != 'RGB':
            img_rgb = img.convert('RGB')
        else:
            img_rgb = img
        
        # Convert to numpy array for analysis
        img_array = np.array(img_rgb)
        
        # Color analysis
        unique_colors = len(np.unique(img_array.reshape(-1, img_array.shape[-1]), axis=0))
        print(f"🌈 Unique colors: {unique_colors}")
        
        # Determine if it's mostly black and white
        grayscale = img.convert('L')
        gray_array = np.array(grayscale)
        
        # Count pixels that are close to black or white
        black_pixels = np.sum(gray_array < 50)
        white_pixels = np.sum(gray_array > 200)
        total_pixels = gray_array.size
        bw_ratio = (black_pixels + white_pixels) / total_pixels
        
        print(f"⚫ Black pixels: {black_pixels:,} ({black_pixels/total_pixels*100:.1f}%)")
        print(f"⚪ White pixels: {white_pixels:,} ({white_pixels/total_pixels*100:.1f}%)")
        print(f"🔲 B&W ratio: {bw_ratio:.2f}")
        
        # Edge detection (simple gradient)
        from scipy import ndimage
        edges_x = ndimage.sobel(gray_array, axis=0)
        edges_y = ndimage.sobel(gray_array, axis=1)
        edges = np.hypot(edges_x, edges_y)
        edge_pixels = np.sum(edges > 30)
        edge_ratio = edge_pixels / total_pixels
        
        print(f"📐 Edge pixels: {edge_pixels:,} ({edge_ratio*100:.1f}%)")
        
        # SVG Conversion Assessment
        print("\n🎯 SVG Conversion Assessment")
        print("-" * 40)
        
        svg_score = 0
        reasons = []
        
        # Size check
        if img.size[0] > 200 and img.size[1] > 100:
            svg_score += 20
            reasons.append("✅ Good size for conversion")
        else:
            reasons.append("⚠️ Small image - may not benefit from SVG")
        
        # Color complexity
        if unique_colors < 50:
            svg_score += 30
            reasons.append("✅ Low color complexity - excellent for SVG")
        elif unique_colors < 200:
            svg_score += 20
            reasons.append("✅ Moderate color complexity - good for SVG")
        else:
            reasons.append("⚠️ High color complexity - challenging for SVG")
        
        # Black and white content
        if bw_ratio > 0.8:
            svg_score += 25
            reasons.append("✅ Mostly B&W content - perfect for SVG")
        elif bw_ratio > 0.6:
            svg_score += 15
            reasons.append("✅ Good B&W ratio - suitable for SVG")
        else:
            reasons.append("⚠️ Complex color gradients - difficult for SVG")
        
        # Edge content (indicates line drawings, diagrams)
        if edge_ratio > 0.1:
            svg_score += 25
            reasons.append("✅ High edge content - likely technical diagram")
        elif edge_ratio > 0.05:
            svg_score += 15
            reasons.append("✅ Moderate edge content - some geometric shapes")
        else:
            reasons.append("⚠️ Low edge content - may be photographic")
        
        # Final assessment
        if svg_score >= 80:
            assessment = "🌟 EXCELLENT - Ideal for SVG conversion"
            color = "green"
        elif svg_score >= 60:
            assessment = "✅ GOOD - Well-suited for SVG conversion"
            color = "blue"
        elif svg_score >= 40:
            assessment = "⚠️ MODERATE - Possible but challenging"
            color = "yellow"
        else:
            assessment = "❌ POOR - Not recommended for SVG"
            color = "red"
        
        print(f"📊 SVG Score: {svg_score}/100")
        print(f"🎯 Assessment: {assessment}")
        print()
        
        print("📋 Detailed Analysis:")
        for reason in reasons:
            print(f"   {reason}")
        
        # Specific recommendations
        print(f"\n💡 Recommendations:")
        if svg_score >= 60:
            print("   • This image is well-suited for SVG conversion")
            print("   • Focus on shape detection and text extraction")
            print("   • Use edge detection to identify geometric elements")
            if bw_ratio > 0.7:
                print("   • Simple black/white vectorization should work well")
            if edge_ratio > 0.1:
                print("   • Likely contains technical diagrams or charts")
        else:
            print("   • Consider keeping as raster image")
            print("   • SVG conversion may not provide significant benefits")
            if unique_colors > 200:
                print("   • Too many colors for effective vectorization")
        
        return {
            'svg_score': svg_score,
            'assessment': assessment,
            'dimensions': img.size,
            'unique_colors': unique_colors,
            'bw_ratio': bw_ratio,
            'edge_ratio': edge_ratio,
            'suitable_for_svg': svg_score >= 60
        }
        
    except Exception as e:
        print(f"❌ Error analyzing image: {e}")
        return None

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze_image_for_svg.py <image_path>")
        sys.exit(1)
    
    image_path = sys.argv[1]
    if not Path(image_path).exists():
        print(f"❌ Image file not found: {image_path}")
        sys.exit(1)
    
    result = analyze_image_for_svg_conversion(image_path)
    
    if result and result['suitable_for_svg']:
        print(f"\n🚀 This image is a good candidate for SVG conversion!")
        print(f"   Expected benefits: Scalability, searchable text, smaller file size")
    else:
        print(f"\n💭 This image may be better kept as a raster format.")

if __name__ == "__main__":
    main()
