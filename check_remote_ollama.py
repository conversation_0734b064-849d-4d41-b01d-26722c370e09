#!/usr/bin/env python3
"""
Check available models at remote Ollama server
"""

import requests
import json

def check_ollama_models(url="http://192.168.0.77:11434"):
    """Check available models at remote Ollama server."""
    
    print(f"🔍 Checking Ollama models at {url}")
    print("=" * 60)
    
    try:
        # Test connection
        response = requests.get(f"{url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"✅ Connected successfully!")
            print(f"📊 Available models: {len(models)}")
            print()
            
            # Sort models by size (largest first)
            models_sorted = sorted(models, key=lambda x: x.get('size', 0), reverse=True)
            
            print("📋 Model Details:")
            print("-" * 60)
            
            for model in models_sorted:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                size_gb = size / (1024 * 1024 * 1024) if size > 0 else 0
                modified = model.get('modified_at', 'Unknown')
                
                # Estimate context window based on model name
                context_window = estimate_context_window(name)
                
                print(f"🤖 {name}")
                print(f"   Size: {size_gb:.1f} GB")
                print(f"   Modified: {modified[:19] if modified != 'Unknown' else 'Unknown'}")
                print(f"   Estimated Context: {context_window:,} tokens")
                print()
            
            # Recommend best model for large documents
            recommend_best_model(models_sorted)
            
            return models_sorted
            
        else:
            print(f"❌ Server returned status {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to Ollama server at {url}")
        print(f"Please ensure:")
        print(f"1. Ollama is running on the remote server")
        print(f"2. Port 11434 is accessible")
        print(f"3. Network connectivity is working")
        return None
        
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return None

def estimate_context_window(model_name):
    """Estimate context window based on model name."""
    
    name_lower = model_name.lower()
    
    # Known context windows for popular models
    if 'llama3.2' in name_lower:
        if '90b' in name_lower or '70b' in name_lower:
            return 128000  # 128k context
        else:
            return 128000  # 128k context for smaller variants too
    elif 'llama3.1' in name_lower:
        if '405b' in name_lower:
            return 128000  # 128k context
        elif '70b' in name_lower or '8b' in name_lower:
            return 128000  # 128k context
        else:
            return 128000
    elif 'llama3' in name_lower:
        return 8192   # 8k context for older Llama 3
    elif 'qwen2.5' in name_lower:
        if '72b' in name_lower or '32b' in name_lower:
            return 32768  # 32k context
        else:
            return 32768  # 32k context
    elif 'mistral' in name_lower:
        if 'nemo' in name_lower:
            return 128000  # 128k context
        else:
            return 32768   # 32k context
    elif 'gemma' in name_lower:
        return 8192    # 8k context
    elif 'phi' in name_lower:
        return 131072  # 128k context for Phi models
    elif 'deepseek' in name_lower:
        if 'r1' in name_lower:
            return 64000   # 64k context
        else:
            return 16384   # 16k context
    else:
        return 4096    # Default assumption

def recommend_best_model(models):
    """Recommend the best model for large document processing."""
    
    print("🎯 **RECOMMENDATIONS FOR LARGE DOCUMENT PROCESSING**")
    print("=" * 60)
    
    # Find models with large context windows
    large_context_models = []
    
    for model in models:
        name = model.get('name', '')
        context = estimate_context_window(name)
        size_gb = model.get('size', 0) / (1024 * 1024 * 1024)
        
        if context >= 32000:  # 32k+ context
            large_context_models.append({
                'name': name,
                'context': context,
                'size_gb': size_gb,
                'model': model
            })
    
    if large_context_models:
        # Sort by context window, then by size
        large_context_models.sort(key=lambda x: (x['context'], x['size_gb']), reverse=True)
        
        print("🌟 **BEST OPTIONS FOR PDF PROCESSING:**")
        print()
        
        for i, model in enumerate(large_context_models[:3], 1):
            print(f"{i}. **{model['name']}**")
            print(f"   ✅ Context Window: {model['context']:,} tokens")
            print(f"   ✅ Size: {model['size_gb']:.1f} GB")
            
            # Calculate rough document capacity
            chars_per_token = 4  # Rough estimate
            max_chars = model['context'] * chars_per_token
            max_pages = max_chars // 2000  # Rough estimate of chars per page
            
            print(f"   📄 Can handle: ~{max_pages:,} pages per chunk")
            print()
        
        # Recommend the best one
        best_model = large_context_models[0]
        print(f"🏆 **RECOMMENDED:** `{best_model['name']}`")
        print(f"   - Largest context window: {best_model['context']:,} tokens")
        print(f"   - Can process large documents in fewer chunks")
        print(f"   - Better coherence for copyright removal and filtering")
        
    else:
        print("⚠️ No models with large context windows found")
        print("Consider installing a model with larger context:")
        print("- ollama pull llama3.2:90b")
        print("- ollama pull qwen2.5:72b") 
        print("- ollama pull mistral-nemo")

def test_model_performance(url, model_name):
    """Test a specific model's performance."""
    
    print(f"\n🧪 Testing model: {model_name}")
    print("-" * 40)
    
    test_text = """Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

Section 4.2 Technical Requirements

The system shall comply with the following specifications:
- Voltage range: 100-240V AC
- Frequency: 50/60 Hz"""

    prompt = """Remove all copyright boilerplate, legal notices, and customer information from this text. Keep only the technical content in English:

""" + test_text

    try:
        payload = {
            "model": model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 500
            }
        }
        
        import time
        start_time = time.time()
        
        response = requests.post(
            f"{url}/api/generate",
            json=payload,
            timeout=60
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            filtered_text = result.get('response', '').strip()
            
            print(f"✅ Response time: {end_time - start_time:.1f} seconds")
            print(f"📊 Original: {len(test_text)} chars")
            print(f"📊 Filtered: {len(filtered_text)} chars")
            print(f"📊 Reduction: {((len(test_text) - len(filtered_text)) / len(test_text) * 100):.1f}%")
            print(f"🔍 Result preview:")
            print(f"   '{filtered_text[:150]}...'")
            
            # Check if copyright was removed
            copyright_terms = ['phil young', 'trianglemicroworks', '<EMAIL>', 'all rights reserved']
            remaining = [term for term in copyright_terms if term.lower() in filtered_text.lower()]
            
            if remaining:
                print(f"⚠️ Still contains: {remaining}")
            else:
                print(f"✅ Copyright successfully removed")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Main function."""

    url = "http://192.168.0.78:11434"  # Updated to check the second server

    models = check_ollama_models(url)

    # Check specifically for vision models
    vision_models = [m for m in models if 'vision' in m['name'].lower() or 'vl' in m['name'].lower() or 'qwen2.5vl' in m['name'].lower()]
    if vision_models:
        print(f"\n🔍 **VISION MODELS FOUND:**")
        for model in vision_models:
            size_gb = model.get('size_gb', model.get('size', 0) / (1024**3))
            print(f"   📷 {model['name']} ({size_gb:.1f}GB)")
    else:
        print(f"\n⚠️ **NO VISION MODELS FOUND** - Consider pulling qwen2.5vl:7b")
    
    if models:
        # Test the recommended model if available
        large_context_models = [m for m in models if estimate_context_window(m.get('name', '')) >= 32000]
        
        if large_context_models:
            best_model = max(large_context_models, key=lambda x: estimate_context_window(x.get('name', '')))
            test_model_performance(url, best_model.get('name', ''))

if __name__ == "__main__":
    main()
