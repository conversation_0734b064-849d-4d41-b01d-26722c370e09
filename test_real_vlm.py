#!/usr/bin/env python3
"""
Test real VLM with local Ollama server
"""

import requests
import base64
import json
import time
from pathlib import Path

def test_ollama_vision(image_path: str, model_name: str = "llava:7b", url: str = "http://127.0.0.1:11434"):
    """Test Ollama vision model with an image."""
    
    print(f"🧠 Testing {model_name} with real image analysis")
    print(f"📁 Image: {image_path}")
    print(f"🔗 Server: {url}")
    
    try:
        # Encode image to base64
        with open(image_path, "rb") as image_file:
            image_b64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        print(f"✅ Image encoded ({len(image_b64)} characters)")
        
        # Simple vision test prompt
        prompt = """Look at this image and describe what you see. Focus on:
1. What type of image is this?
2. What are the main visual elements?
3. Is there any text visible?
4. What technical content do you notice?

Keep your response concise but detailed."""
        
        payload = {
            "model": model_name,
            "prompt": prompt,
            "images": [image_b64],
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 500
            }
        }
        
        print(f"🚀 Sending request to VLM...")
        start_time = time.time()
        
        response = requests.post(
            f"{url}/api/generate",
            json=payload,
            timeout=300  # 5 minutes
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            analysis_text = result.get('response', '')
            
            print(f"✅ VLM Analysis Complete!")
            print(f"⏱️ Processing time: {processing_time:.1f} seconds")
            print(f"📊 Response length: {len(analysis_text)} characters")
            print(f"\n🔍 VLM Analysis:")
            print("=" * 50)
            print(analysis_text)
            print("=" * 50)
            
            return {
                "success": True,
                "analysis": analysis_text,
                "processing_time": processing_time,
                "model": model_name
            }
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out after 5 minutes")
        return {"success": False, "error": "Timeout"}
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"success": False, "error": str(e)}

def test_simple_vision_prompt(model_name: str = "llava:7b", url: str = "http://127.0.0.1:11434"):
    """Test with a simple text-only prompt first."""
    
    print(f"\n🧪 Testing {model_name} with text-only prompt...")
    
    try:
        payload = {
            "model": model_name,
            "prompt": "Hello! Are you a vision model that can analyze images?",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 100
            }
        }
        
        response = requests.post(
            f"{url}/api/generate",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"✅ Text-only test successful!")
            print(f"Response: {response_text}")
            return True
        else:
            print(f"❌ Text-only test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Text-only test error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔬 Real VLM Testing with Local Ollama")
    print("=" * 50)
    
    # Test images
    test_images = [
        "test_image_ocr_1/images/DigIF_spec_9-2LE_R2-1_040707-CB_page23_img33_e6a9d0d9.png",  # Nomogram
        "test_image_ocr_1/images/DigIF_spec_9-2LE_R2-1_040707-CB_page18_img22_95c0d2d7.png"   # Technical diagram
    ]
    
    model_name = "llava:7b"
    
    # First test text-only
    if not test_simple_vision_prompt(model_name):
        print("❌ Basic model test failed. Check Ollama server.")
        return
    
    # Test with images
    for i, image_path in enumerate(test_images, 1):
        if not Path(image_path).exists():
            print(f"⚠️ Image {i} not found: {image_path}")
            continue
            
        print(f"\n📊 Test {i}: {Path(image_path).name}")
        print("-" * 40)
        
        result = test_ollama_vision(image_path, model_name)
        
        if result["success"]:
            print(f"🎉 Test {i} successful!")
            
            # Save analysis to file
            output_file = f"vlm_analysis_{i}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"VLM Analysis - {Path(image_path).name}\n")
                f.write("=" * 50 + "\n")
                f.write(f"Model: {result['model']}\n")
                f.write(f"Processing Time: {result['processing_time']:.1f} seconds\n")
                f.write(f"Analysis:\n{result['analysis']}\n")
            
            print(f"📄 Analysis saved to: {output_file}")
        else:
            print(f"❌ Test {i} failed: {result.get('error', 'Unknown error')}")
        
        print()

if __name__ == "__main__":
    main()
