# Enhanced PDF to Markdown Processor with GPU OCR

A powerful PDF processing tool with GPU-accelerated OCR capabilities, designed specifically for RAG (Retrieval Augmented Generation) applications. This enhanced version intelligently combines native PDF text extraction with OCR to handle both digital and scanned documents while avoiding text duplication.

## 🚀 Key Features

### GPU-Accelerated OCR
- **PaddleOCR**: Best performance with GPU acceleration
- **EasyOCR**: Excellent accuracy with multi-language support
- **Tesseract**: Fallback option with CPU optimization

### Intelligent Text Processing
- **Overlap Detection**: Automatically detects and prevents duplicate text from native PDF and OCR sources
- **Quality Thresholding**: Configurable confidence levels for OCR text acceptance
- **Smart Fallback**: Graceful degradation from GPU to CPU when needed

### RAG-Optimized Output
- **Clean Markdown**: Structured output perfect for vector databases
- **Image Preservation**: Extracts and embeds diagrams, figures, and tables
- **Contextual Chunking**: Maintains document structure for better retrieval
- **No Duplication**: Ensures clean text without overlapping content

## 📦 Installation

### 1. Basic Installation
```bash
pip install -r requirements.txt
```

### 2. GPU OCR Dependencies

#### Option A: PaddleOCR (Recommended for GPU)
```bash
# For CUDA GPU
pip install paddlepaddle-gpu paddleocr

# For CPU only
pip install paddlepaddle paddleocr
```

#### Option B: EasyOCR
```bash
pip install easyocr
```

#### Option C: Tesseract
```bash
# Install Tesseract system dependency first
# Ubuntu/Debian: sudo apt-get install tesseract-ocr
# Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# macOS: brew install tesseract

pip install pytesseract
```

### 3. Optional: Ollama for LLM Processing
```bash
# Install Ollama: https://ollama.ai/
ollama pull llama2
ollama serve
```

## 🔧 Configuration

Create a `.env` file in your project directory:

```env
# Input/Output Directories
INPUT_DIR=./pdfs
OUTPUT_DIR=./markdown_output

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
MODEL_NAME=llama2

# OCR Configuration
USE_GPU_OCR=true
OCR_ENGINE=paddleocr
OCR_LANGUAGES=en
OCR_CONFIDENCE_THRESHOLD=0.6
OCR_FALLBACK_TO_CPU=true
TEXT_OVERLAP_THRESHOLD=0.8
PREFER_NATIVE_TEXT=true

# Processing Configuration
CHUNK_SIZE=3000
TEMPERATURE=0.1
MAX_RETRIES=3
TIMEOUT=120

# Image Processing
MIN_IMAGE_SIZE=100
IMAGE_QUALITY=90
EXTRACT_TABLES=true

# Logging Level
LOG_LEVEL=INFO
```

## 🎯 Usage

### Command Line Interface
```bash
# Basic usage
python extract_text.py document.pdf

# Specify output directory
python extract_text.py document.pdf --output ./output

# Use different OCR engine
python extract_text.py document.pdf --ocr-engine easyocr

# Disable GPU acceleration
python extract_text.py document.pdf --no-gpu
```

### Python API
```python
from pdf_to_markdown_processor import EnhancedPDFProcessor, ProcessingConfig

# Configure for RAG applications
config = ProcessingConfig(
    use_gpu_ocr=True,
    ocr_engine="paddleocr",
    ocr_confidence_threshold=0.6,
    text_overlap_threshold=0.8,
    prefer_native_text=True,
    min_image_size=100,
    image_quality=90
)

# Process PDFs
processor = EnhancedPDFProcessor("./pdfs", "./output", config)
processor.process_directory()
```

### Batch Processing
```bash
# Process all PDFs in a directory
python pdf-to-markdown-processor.py
```

## 🧠 How It Works

### 1. Dual Text Extraction
- **Native Text**: Extracts text directly from PDF structure (fast, accurate for digital PDFs)
- **OCR Text**: Uses GPU-accelerated OCR for scanned content or when native text is poor quality

### 2. Intelligent Overlap Detection
- Compares native and OCR text using similarity algorithms
- Configurable threshold (default 80%) to detect duplicates
- Prefers native text when overlap is detected
- Keeps both sources when they contain different information

### 3. RAG Optimization
- Maintains document structure with clear page breaks
- Preserves images with proper markdown references
- Creates clean, searchable text chunks
- Removes OCR artifacts and noise

## 📊 Performance Comparison

| Method | Speed | Accuracy | GPU Support | Best For |
|--------|-------|----------|-------------|----------|
| PaddleOCR | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | Production RAG systems |
| EasyOCR | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | Multi-language documents |
| Tesseract | ⭐⭐⭐ | ⭐⭐⭐⭐ | ❌ | CPU-only environments |

## 🔍 Output Structure

```
markdown_output/
├── document1.md          # Main markdown file
├── document2.md
├── images/               # Extracted images
│   ├── doc1_page1_img1.png
│   ├── doc1_page2_diagram.png
│   └── ...
└── pdf_processor.log     # Processing logs
```

### Sample Markdown Output
```markdown
# Document Title

## Section 1

Native PDF text content here...

![Diagram 1](images/doc_page1_img1.png)

--- OCR Supplementary Text ---
Additional text found only through OCR...

## Section 2

More content with preserved structure...
```

## 🚨 Troubleshooting

### GPU Issues
```bash
# Check GPU availability
python -c "import torch; print(torch.cuda.is_available())"

# Test PaddleOCR GPU
python -c "import paddleocr; ocr = paddleocr.PaddleOCR(use_gpu=True)"
```

### Memory Issues
- Reduce `chunk_size` in configuration
- Lower `image_quality` setting
- Process files individually instead of batch

### OCR Quality Issues
- Increase `ocr_confidence_threshold`
- Try different OCR engines
- Preprocess images (contrast, resolution)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- PaddleOCR team for excellent GPU OCR implementation
- EasyOCR for multi-language support
- PyMuPDF for robust PDF processing
- Ollama for local LLM integration
