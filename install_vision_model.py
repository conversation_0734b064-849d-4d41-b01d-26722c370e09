#!/usr/bin/env python3
"""
Install vision model via Ollama API
"""

import requests
import json
import time
import sys

def install_model_via_api(model_name="llava:7b", url="http://127.0.0.1:11434"):
    """Install model via Ollama API."""
    
    print(f"📥 Installing {model_name} via Ollama API...")
    print(f"⚠️ This may take several minutes (model is ~4GB)")
    
    try:
        # Use the pull API endpoint
        payload = {
            "name": model_name,
            "stream": True
        }
        
        response = requests.post(
            f"{url}/api/pull",
            json=payload,
            stream=True,
            timeout=600  # 10 minutes timeout
        )
        
        if response.status_code == 200:
            print(f"✅ Starting download of {model_name}...")
            
            # Process streaming response
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        status = data.get('status', '')
                        
                        if 'pulling' in status.lower():
                            # Show download progress
                            completed = data.get('completed', 0)
                            total = data.get('total', 0)
                            if total > 0:
                                progress = (completed / total) * 100
                                print(f"\r📊 Progress: {progress:.1f}% ({completed:,}/{total:,} bytes)", end='', flush=True)
                        elif 'success' in status.lower() or 'complete' in status.lower():
                            print(f"\n✅ {model_name} installed successfully!")
                            return True
                        elif status:
                            print(f"\n📋 Status: {status}")
                            
                    except json.JSONDecodeError:
                        continue
            
            print(f"\n✅ {model_name} installation completed!")
            return True
            
        else:
            print(f"❌ Failed to install model. Status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing model: {e}")
        return False

def verify_installation(model_name="llava:7b", url="http://127.0.0.1:11434"):
    """Verify the model was installed correctly."""
    
    print(f"\n🔍 Verifying {model_name} installation...")
    
    try:
        # Check if model appears in list
        response = requests.get(f"{url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            # Look for the installed model
            for model in models:
                if model_name in model.get('name', ''):
                    print(f"✅ {model_name} found in model list!")
                    
                    # Test the model with a simple prompt
                    print(f"🧪 Testing model with simple prompt...")
                    
                    test_payload = {
                        "model": model_name,
                        "prompt": "Hello! Are you a vision model?",
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "num_predict": 30
                        }
                    }
                    
                    test_response = requests.post(
                        f"{url}/api/generate",
                        json=test_payload,
                        timeout=60
                    )
                    
                    if test_response.status_code == 200:
                        result = test_response.json()
                        response_text = result.get('response', '')
                        print(f"✅ Model test successful!")
                        print(f"Response: {response_text[:100]}...")
                        return True
                    else:
                        print(f"⚠️ Model installed but test failed")
                        return False
            
            print(f"❌ {model_name} not found in model list")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying installation: {e}")
        return False

def main():
    """Main installation function."""
    
    print("📥 Ollama Vision Model Installer")
    print("=" * 40)
    
    model_name = "llava:7b"
    
    print(f"Installing {model_name}...")
    print(f"This will download approximately 4GB of data.")
    
    # Install the model
    if install_model_via_api(model_name):
        # Verify installation
        if verify_installation(model_name):
            print(f"\n🎉 Success! {model_name} is ready for use!")
            print(f"\nNext steps:")
            print(f"   python vlm_image_analyzer.py image.png --backend ollama --model {model_name}")
        else:
            print(f"\n⚠️ Installation may have succeeded but verification failed")
    else:
        print(f"\n❌ Installation failed")
        print(f"\nAlternative: Try installing manually if Ollama CLI is available:")
        print(f"   ollama pull {model_name}")

if __name__ == "__main__":
    main()
