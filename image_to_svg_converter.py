#!/usr/bin/env python3
"""
Image to SVG Converter
Converts suitable images (technical diagrams, charts, etc.) to SVG format.
"""

import sys
import os
from pathlib import Path
from PIL import Image, ImageFilter
import numpy as np
from scipy import ndimage
import cv2
import xml.etree.ElementTree as ET
from xml.dom import minidom
import argparse
import json

class ImageToSVGConverter:
    def __init__(self, confidence_threshold=0.6):
        self.confidence_threshold = confidence_threshold
        self.ocr_engine = None
        self._init_ocr()
    
    def _init_ocr(self):
        """Initialize OCR engine for text extraction."""
        try:
            import pytesseract
            self.ocr_engine = pytesseract
            print("✅ Tesseract OCR initialized")
        except ImportError:
            print("⚠️ OCR not available - text extraction disabled")
    
    def analyze_image_suitability(self, image_path):
        """Analyze if image is suitable for SVG conversion."""
        try:
            img = Image.open(image_path)
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img_rgb = img.convert('RGB')
            else:
                img_rgb = img
            
            img_array = np.array(img_rgb)
            unique_colors = len(np.unique(img_array.reshape(-1, img_array.shape[-1]), axis=0))
            
            # Grayscale analysis
            grayscale = img.convert('L')
            gray_array = np.array(grayscale)
            
            black_pixels = np.sum(gray_array < 50)
            white_pixels = np.sum(gray_array > 200)
            total_pixels = gray_array.size
            bw_ratio = (black_pixels + white_pixels) / total_pixels
            
            # Edge detection
            edges_x = ndimage.sobel(gray_array, axis=0)
            edges_y = ndimage.sobel(gray_array, axis=1)
            edges = np.hypot(edges_x, edges_y)
            edge_pixels = np.sum(edges > 30)
            edge_ratio = edge_pixels / total_pixels
            
            # Calculate SVG score
            svg_score = 0
            if img.size[0] > 200 and img.size[1] > 100:
                svg_score += 20
            if unique_colors < 50:
                svg_score += 30
            elif unique_colors < 200:
                svg_score += 20
            if bw_ratio > 0.8:
                svg_score += 25
            elif bw_ratio > 0.6:
                svg_score += 15
            if edge_ratio > 0.1:
                svg_score += 25
            elif edge_ratio > 0.05:
                svg_score += 15
            
            return {
                'suitable': svg_score >= 60,
                'score': svg_score,
                'dimensions': img.size,
                'unique_colors': unique_colors,
                'bw_ratio': bw_ratio,
                'edge_ratio': edge_ratio
            }
        except Exception as e:
            print(f"❌ Error analyzing {image_path}: {e}")
            return {'suitable': False, 'score': 0}
    
    def extract_text_regions(self, image):
        """Extract text regions and their positions using OCR."""
        if not self.ocr_engine:
            return []
        
        try:
            # Get detailed OCR data with bounding boxes
            data = self.ocr_engine.image_to_data(image, output_type=self.ocr_engine.Output.DICT)
            
            text_regions = []
            for i in range(len(data['text'])):
                if int(data['conf'][i]) > 30:  # Confidence threshold
                    text = data['text'][i].strip()
                    if text:
                        text_regions.append({
                            'text': text,
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i],
                            'confidence': data['conf'][i]
                        })
            
            return text_regions
        except Exception as e:
            print(f"⚠️ OCR extraction failed: {e}")
            return []
    
    def detect_shapes(self, image):
        """Detect basic shapes (lines, rectangles, circles) in the image."""
        # Convert PIL to OpenCV format
        img_array = np.array(image.convert('RGB'))
        img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        shapes = []
        
        # Detect lines using HoughLines
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None:
            for line in lines[:50]:  # Limit to first 50 lines
                rho, theta = line[0]
                a = np.cos(theta)
                b = np.sin(theta)
                x0 = a * rho
                y0 = b * rho
                x1 = int(x0 + 1000 * (-b))
                y1 = int(y0 + 1000 * (a))
                x2 = int(x0 - 1000 * (-b))
                y2 = int(y0 - 1000 * (a))
                
                shapes.append({
                    'type': 'line',
                    'x1': max(0, min(x1, image.width)),
                    'y1': max(0, min(y1, image.height)),
                    'x2': max(0, min(x2, image.width)),
                    'y2': max(0, min(y2, image.height))
                })
        
        # Detect rectangles using contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            # Approximate contour to polygon
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) == 4:  # Rectangle
                x, y, w, h = cv2.boundingRect(approx)
                if w > 10 and h > 10:  # Filter small rectangles
                    shapes.append({
                        'type': 'rect',
                        'x': x,
                        'y': y,
                        'width': w,
                        'height': h
                    })
        
        return shapes
    
    def create_svg(self, image, text_regions, shapes, output_path):
        """Create SVG from detected elements."""
        width, height = image.size
        
        # Create SVG root element
        svg = ET.Element('svg')
        svg.set('xmlns', 'http://www.w3.org/2000/svg')
        svg.set('viewBox', f'0 0 {width} {height}')
        svg.set('width', str(width))
        svg.set('height', str(height))
        
        # Add background (simplified version of original image)
        # Convert image to high contrast B&W for background
        bg_img = image.convert('L')
        bg_array = np.array(bg_img)
        
        # Create simplified background using thresholding
        threshold = 128
        bg_binary = (bg_array > threshold).astype(np.uint8) * 255
        
        # Add background as embedded image (simplified)
        bg_group = ET.SubElement(svg, 'g')
        bg_group.set('id', 'background')
        
        # Add detected shapes
        shapes_group = ET.SubElement(svg, 'g')
        shapes_group.set('id', 'shapes')
        shapes_group.set('stroke', 'black')
        shapes_group.set('stroke-width', '1')
        shapes_group.set('fill', 'none')
        
        for shape in shapes:
            if shape['type'] == 'line':
                line = ET.SubElement(shapes_group, 'line')
                line.set('x1', str(shape['x1']))
                line.set('y1', str(shape['y1']))
                line.set('x2', str(shape['x2']))
                line.set('y2', str(shape['y2']))
            elif shape['type'] == 'rect':
                rect = ET.SubElement(shapes_group, 'rect')
                rect.set('x', str(shape['x']))
                rect.set('y', str(shape['y']))
                rect.set('width', str(shape['width']))
                rect.set('height', str(shape['height']))
        
        # Add text elements
        text_group = ET.SubElement(svg, 'g')
        text_group.set('id', 'text')
        text_group.set('font-family', 'Arial, sans-serif')
        text_group.set('font-size', '12')
        text_group.set('fill', 'black')
        
        for text_region in text_regions:
            text_elem = ET.SubElement(text_group, 'text')
            text_elem.set('x', str(text_region['x']))
            text_elem.set('y', str(text_region['y'] + text_region['height']))
            text_elem.text = text_region['text']
        
        # Write SVG to file
        rough_string = ET.tostring(svg, 'unicode')
        reparsed = minidom.parseString(rough_string)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(reparsed.toprettyxml(indent="  "))
        
        return True
    
    def convert_image(self, input_path, output_path=None):
        """Convert a single image to SVG."""
        if output_path is None:
            output_path = Path(input_path).with_suffix('.svg')
        
        print(f"🔄 Converting: {input_path}")
        
        # Check suitability
        analysis = self.analyze_image_suitability(input_path)
        if not analysis['suitable']:
            print(f"⚠️ Image not suitable for SVG conversion (score: {analysis['score']}/100)")
            return False
        
        print(f"✅ Image suitable for conversion (score: {analysis['score']}/100)")
        
        try:
            # Load image
            image = Image.open(input_path)
            
            # Extract text regions
            print("📝 Extracting text regions...")
            text_regions = self.extract_text_regions(image)
            print(f"   Found {len(text_regions)} text regions")
            
            # Detect shapes
            print("🔍 Detecting shapes...")
            shapes = self.detect_shapes(image)
            print(f"   Found {len(shapes)} shapes")
            
            # Create SVG
            print("🎨 Creating SVG...")
            success = self.create_svg(image, text_regions, shapes, output_path)
            
            if success:
                # Compare file sizes
                original_size = os.path.getsize(input_path)
                svg_size = os.path.getsize(output_path)
                reduction = (1 - svg_size / original_size) * 100
                
                print(f"✅ SVG created: {output_path}")
                print(f"📊 File size: {original_size:,} → {svg_size:,} bytes ({reduction:+.1f}%)")
                return True
            else:
                print("❌ Failed to create SVG")
                return False
                
        except Exception as e:
            print(f"❌ Conversion failed: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Convert images to SVG format')
    parser.add_argument('input', help='Input image file or directory')
    parser.add_argument('-o', '--output', help='Output file or directory')
    parser.add_argument('--batch', action='store_true', help='Process all images in directory')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze suitability, don\'t convert')
    
    args = parser.parse_args()
    
    converter = ImageToSVGConverter()
    
    if args.batch or Path(args.input).is_dir():
        # Batch processing
        input_dir = Path(args.input)
        output_dir = Path(args.output) if args.output else input_dir / 'svg_output'
        output_dir.mkdir(exist_ok=True)
        
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        image_files = [f for f in input_dir.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        print(f"🔍 Found {len(image_files)} images in {input_dir}")
        
        results = []
        for img_file in image_files:
            if args.analyze_only:
                analysis = converter.analyze_image_suitability(img_file)
                results.append({
                    'file': str(img_file),
                    'suitable': analysis['suitable'],
                    'score': analysis['score']
                })
                print(f"{img_file.name}: {'✅' if analysis['suitable'] else '❌'} ({analysis['score']}/100)")
            else:
                output_file = output_dir / f"{img_file.stem}.svg"
                success = converter.convert_image(img_file, output_file)
                results.append({
                    'file': str(img_file),
                    'success': success
                })
        
        if args.analyze_only:
            suitable_count = sum(1 for r in results if r['suitable'])
            print(f"\n📊 Summary: {suitable_count}/{len(results)} images suitable for SVG conversion")
    else:
        # Single file processing
        if args.analyze_only:
            analysis = converter.analyze_image_suitability(args.input)
            print(f"Suitability: {'✅' if analysis['suitable'] else '❌'} ({analysis['score']}/100)")
        else:
            converter.convert_image(args.input, args.output)

if __name__ == "__main__":
    main()
