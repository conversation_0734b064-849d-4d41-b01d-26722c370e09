#!/usr/bin/env python3
"""
Test script to verify copyright removal and English filtering
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path to import the processor
sys.path.append(str(Path(__file__).parent))

# Import the classes from the main script
exec(open('pdf-to-markdown-processor.py').read())

def test_copyright_removal():
    """Test the copyright removal functionality."""
    
    print("🧪 Testing Copyright Removal and English Filtering")
    print("=" * 60)
    
    # Create a test processor
    config = ProcessingConfig()
    processor = PDFToMarkdownProcessor(config)
    
    # Test cases with various copyright boilerplate
    test_cases = [
        {
            "name": "IEC Specific Copyright",
            "text": """Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

This is the actual technical content that should be preserved.
It contains important information about electrical standards."""
        },
        {
            "name": "Generic Copyright",
            "text": """© 2024 International Electrotechnical Commission. All rights reserved.
Copyright 2024 IEC Geneva Switzerland. All rights reserved.

Section 4.2 Technical Requirements

The system shall comply with the following specifications:
- Voltage range: 100-240V AC
- Frequency: 50/60 Hz"""
        },
        {
            "name": "Mixed Language Content",
            "text": """This is English content that should be kept.

Ceci est du contenu français qui devrait être supprimé.

Dies ist deutscher Inhalt, der entfernt werden sollte.

More English technical content about electrical systems.
Figure 1 shows the circuit diagram."""
        },
        {
            "name": "Page Headers/Footers",
            "text": """– 12 –    IEC 61850-4 – 14 –

4.3 Communication protocols

The communication shall use the following protocols:
- HTTP/HTTPS for web services
- TCP/IP for network communication

IEC 61850-4 – 16 –    – 17 –"""
        },
        {
            "name": "Complex Boilerplate",
            "text": """Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821
IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

5. TESTING PROCEDURES

5.1 General Requirements

All equipment shall be tested according to the procedures outlined in this section.

International Electrotechnical Commission
Geneva, Switzerland
All rights reserved. No part of this publication may be reproduced."""
        }
    ]
    
    print(f"\n📋 Running {len(test_cases)} test cases...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print("-" * 40)
        
        original_text = test_case['text']
        print(f"📄 Original length: {len(original_text)} characters")
        
        # Test copyright removal
        cleaned_text = processor._remove_copyright_boilerplate(original_text)
        print(f"🧹 After copyright removal: {len(cleaned_text)} characters")
        
        # Test English verification
        verification = processor._verify_english_content(cleaned_text)
        print(f"🔍 English verification:")
        print(f"   - Is English: {verification['is_english']}")
        print(f"   - Confidence: {verification['confidence']:.2f}")
        print(f"   - Issues: {verification['issues_count']}")
        
        if verification['issues']:
            print(f"   - Issues found: {verification['issues']}")
        
        # Show cleaned result
        print(f"✅ Cleaned text preview:")
        preview = cleaned_text[:200].replace('\n', ' ').strip()
        if len(cleaned_text) > 200:
            preview += "..."
        print(f"   '{preview}'")
        
        print()
    
    print("🎯 Testing LLM English Filtering (if available)...")
    
    # Test with LLM if available
    test_text = """Customer: Phil Young - Company: TriangleMicroworks
IMPORTANT: This file is copyright of IEC, Geneva, Switzerland.

Section 3.1 Technical Specifications

The device shall operate within the following parameters:
- Temperature range: -40°C to +85°C
- Humidity: 5% to 95% RH

Ceci est du texte français qui devrait être supprimé.

Figure 2 shows the connection diagram."""
    
    try:
        if hasattr(processor, 'llm') and processor.llm:
            print("🤖 Testing with LLM filtering...")
            result = processor.llm_filter_english_content(test_text)
            
            print(f"📊 LLM Results:")
            print(f"   - Original: {len(test_text)} characters")
            print(f"   - Filtered: {len(result['english_text'])} characters")
            print(f"   - Confidence: {result['confidence']:.2f}")
            
            # Verify LLM result
            llm_verification = processor._verify_english_content(result['english_text'])
            print(f"   - Final verification: {llm_verification['is_english']}")
            print(f"   - Final issues: {llm_verification['issues_count']}")
            
            if llm_verification['issues']:
                print(f"   - Remaining issues: {llm_verification['issues']}")
            
            print(f"✅ LLM filtered text:")
            print(f"   '{result['english_text'][:300]}...'")
        else:
            print("⚠️ LLM not available - using basic filtering only")
            
    except Exception as e:
        print(f"❌ LLM testing failed: {e}")
    
    print(f"\n🎉 Copyright removal and English filtering tests completed!")
    print(f"📋 Summary:")
    print(f"   - Copyright patterns: Comprehensive removal implemented")
    print(f"   - English verification: Multi-language detection active")
    print(f"   - Boilerplate detection: Pattern-based filtering active")
    print(f"   - LLM integration: {'Available' if hasattr(processor, 'llm') and processor.llm else 'Not configured'}")

def test_with_sample_files():
    """Test with actual markdown files if they exist."""
    
    print(f"\n📁 Testing with existing markdown files...")
    
    markdown_dir = Path("markdown_output")
    if not markdown_dir.exists():
        print(f"⚠️ No markdown_output directory found")
        return
    
    md_files = list(markdown_dir.glob("*.md"))
    if not md_files:
        print(f"⚠️ No markdown files found in {markdown_dir}")
        return
    
    print(f"📊 Found {len(md_files)} markdown files to analyze")
    
    config = ProcessingConfig()
    processor = PDFToMarkdownProcessor(config)
    
    for md_file in md_files[:3]:  # Test first 3 files
        print(f"\n📄 Analyzing: {md_file.name}")
        
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for copyright issues
            verification = processor._verify_english_content(content)
            
            print(f"   - File size: {len(content)} characters")
            print(f"   - English confidence: {verification['confidence']:.2f}")
            print(f"   - Issues found: {verification['issues_count']}")
            
            if verification['issues']:
                print(f"   - Issues: {verification['issues'][:3]}...")  # Show first 3 issues
            else:
                print(f"   ✅ No copyright or language issues detected")
                
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")

def main():
    """Main test function."""
    
    test_copyright_removal()
    test_with_sample_files()
    
    print(f"\n🔧 **Next Steps:**")
    print(f"1. Run the updated PDF processor on your files")
    print(f"2. Check the logs for filtering quality reports")
    print(f"3. Verify that copyright boilerplate is removed from output")
    print(f"4. Confirm that only English content remains")

if __name__ == "__main__":
    main()
