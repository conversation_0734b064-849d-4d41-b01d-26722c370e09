# Iec61850 9 2{Ed2.0}B

## Carrier Sense Multiple Access with Collision Detection (CSMA/CD)
ISO/IEC 8802-3:2000
Physical
Fibre optic transmission system
100Base-FX
ISO/IEC 8802-3:2000
Basic optical fibre connector
NOTE This is the specification for the ST connector.
IEC 60874-10-1, IEC 60874-10-2 and IEC 60874-10-3

### Physical Layer: Specifications for the Medium Attachment Unit (MAU)
The optical fibre transmission system 100Base-FX according to ISO/IEC 8802-3 is recommended as indicated above because of requirements relating to the electromagnetic environment.

### Link Layer: Ethernet Addresses
The destination ISO/IEC 8802-3 multicast/unicast address has to be configured for the transmission of sampled values. A unique ISO/IEC 8802-3 source address shall be used.
Recommendations of multicast address range assignments are given in Annex B.

## A-Profile
Table 5 shows services and protocols of the A-Profile SV.
### Table 5 — Service and protocols for SV communication A-Profile

| layer | Presentation layer: see additional definitions in 8.5. | Application layer: see additional definitions in 8.5. |

## T-Profile
The T-Profile for SV services is shown in Table 6.
### Table 6 — SV T-Profile

| OSI model | layer | Service specification | Protocol |
| --- | --- | --- | --- |
| Link | Parallel redundancy protocol and IEC 62439-3, Amendment 1 | Redundancy | high availability seamless ring |
| DataLink | Priority tagging/VLAN IEEE 802.10 | om | Carrier sense multiple access ISO/IEC 8802-3:2000 with collision detection (CSMA/CD) |
| Physical | Fibre optic transmission system | ISO/IEC 8802-3:2000 ct | 100Base-FX |
| Basic optical fibre connector | IEC 60874-10-1, IEC 60874-10-2 and ct | NOTE This is the specification for the ST connector. |

### Physical Layer: Specifications for the Medium Attachment Unit (MAU)
The optical fibre transmission system 100Base-FX according to ISO/IEC 8802-3 is recommended as indicated above because of requirements relating to the electromagnetic environment.

### Link Layer: Ethernet Addresses
The destination ISO/IEC 8802-3 multicast/unicast address has to be configured for the transmission of sampled values. A unique ISO/IEC 8802-3 source address shall be used.
Recommendations of multicast address range assignments are given in Annex B.

## Link Layer: Priority Tagging/Virtual LAN
Priority tagging according to IEEE 802.1Q is used to separate time critical and high priority bus traffic for protection-relevant applications from low priority busload.
See Figure 2 for the structure of the tag header.

### Figure 2 – Structure of the tag header

Octets | TPID (tag protocol identifier) field: Indicates the Ethernet type assigned for 802.1Q Ethernet encoded frames. This value shall be 0x8100. |
| TCI (tag control information) fields: User priority: BS3; User priority value shall be set by configuration to separate sampled values from low priority busload. If the priority is not configured, then the default values of Table 7 shall be used. |
| CFI (canonical format indicator): BS1 [0]; A single bit flag value. For this standard, the CFI bit value shall be reset (value = 0). |
| NOTE 1 If set (value = 1), an embedded resource identification field (E-RIF) follows the Length/Type field in the ISO/IEC 8802-3 tagged frame. |
| VID: Virtual LAN support is optional. If this mechanism will be used, the VLAN identifier (VID) shall be set by configuration, if it is not used, it shall be set to zero (0). |
| NOTE 2 As IEEE 802.1Q allows implementation with a restricted set of priorities, the higher priority frames should have a priority of 4 to 7 and the lower priority should have a priority of 1 to 3. The value 1 is the priority of untagged frames thus 0 should be avoided as it may cause unpredictable delay due to normal traffic.

## Additional Content

Additionally, since sampled values need to have potentially its own bandwidth allocation, their configured VID will
be different from GOOSE and GSSE.
The default values for priority and VID shall be as defined in Table 7.
Table 7 – Default Virtual LAN IDs and priorities
Service
Default VID
Default priority
Sampled Values
The general ISO/IEC 8802-3 frame structure for sampled values can be found in Annex A.
*******
Link layer: Ethertype and other header information
*******.1
Ethertype
Ethertypes based on ISO/IEC 8802-3 MAC-sublayer are registered by the IEEE authority
registration. GSE management, GOOSE and samples values shall be directly mapped to the
reserved Ethertype(s) and the Ethertype PDU. The assigned values are found in Table 8.
IEC   1787/11
--- Page 18 ---
61850-9-2 © IEC:2011
Table 8 – Assigned Ethertype values
Use
Ethertype value
(hexadecimal)
APPID type
IEC 61850-8-1 GOOSE
88-B8
0 0
IEC 61850-8-1 GSE Management
88-B9
0 0
IEC 61850-9-2 Sampled Values
88-BA
0 1
The Ethertype PDU and APDU octets shall be as defined in Annex A.
*******.2
APPID
Application identifier. The APPID is used to select ISO/IEC 8802-3 frames containing sampled
value messages and to distinguish the application association.
The value of APPID is the combination of the APPID type, defined as the two most significant
bits of the value (as defined in Table 8), and the actual ID.
The reserved value range for sampled values is 0x4000 to 0x7FFF. If no APPID is configured,
the default value shall be 0x4000. The default value is reserved to indicate lack of
configuration. It is strongly recommended to have unique, source orientated SV APPID within a
system, in order to enable a filter on link layer. The configuration of APPID should be enforced
by the configuration system.
*******.3
Length
Number of octets including the Ethertype PDU header starting at APPID, and the length of the
APDU (Application Protocol Data Unit). Therefore, the value of Length shall be 8 + m, where m
is the length of the APDU and m is less than 1493. Frames with inconsistent or invalid length
field shall be discarded.
*******.4
Reserved 1
The structure of the Reserved 1 is defined in Figure 3.
Octets
Reserved Security
Reserved Security
Figure 3 – Reserved 1
S: Simulate. When this flag is set, the SampledValue telegram has been issued by a publisher
located in a test device and not by the publisher as specified in the configuration file of the
device.
R: Reserved. The three bits are reserved for future standardized application and shall be set to
0 as default.
Reserved security: See reserved 2 below.
IEC   1788/11
--- OCR Supplementary Text ---
-16- 61850-9-2 © IEC:2011
Table 8 - Assigned Ethertype values
Ethertype value
| Reiman | arrose
fieceiemarcooe SSCs fo
[iectesa. ose wenagenen [sess ito =
The Ethertype PDU and APDU octets shall be as defined in Annex A.
*******.2 APPID
Application identifier. The APPID is used to select ISO/IEC 8802-3 frames containing sampled
value messages and to distinguish the application association.
The value of APPID is the combination of the APPID type, defined as the two most significant
bits of the value (as defined in Table 8), and the actual ID.
The reserved value range for sampled values is 0x4000 to 0x7FFF. If no APPID is configured,
the default value shall be 0x4000. The default value is reserved to indicate lack of
configuration. It is strongly recommended to have unique, source orientated SV APPID within a
system, in order to enable a filter on link layer. The configuration of APPID should be enforced
by the configuration system.
*******.3 Length
Number of octets including the Ethertype PDU header starting at APPID, and the length of the
APDU (Application Protocol Data Unit). Therefore, the value of Length shall be 8 + m, where m
is the length of the APDU and m is less than 1493. Frames with inconsistent or invalid length
field shall be discarded.
*******.4 Reserved 1
The structure of the Reserved 1 is defined in Figure 3.
octets} 8 | 7]}/6]5]/4]3] 244
o [Ss] | Reserves Secu |
IEC 1788/11
Figure 3 — Reserved 1
S: Simulate. When this flag is set, the SampledValue telegram has been issued by a publisher
located in a test device and not by the publisher as specified in the configuration file of the
device.
R: Reserved. The three bits are reserved for future standardized application and shall be set to
0 as default.
Reserved security: See reserved 2 below.
--- Page 19 ---
61850-9-2 © IEC:2011
*******.5
Reserved 2
The Reserved 2 field and the “reserved security” of Reserved 1 field form a 28 bits word
defined by the security standard IEC/TS 62351-6. It shall be used as defined when
SampledValue telegram with security is transmitted, otherwise it shall be set to 0.
5.4
Restrictions
This mapping is restricted to the mapping of the ACSI model for the transmission of sampled
values. The model applies to data sets. To get full benefit of IEC 61850, additional ACSI
models need to be supported in accordance to IEC 61850-8-1. As an example, to enable the
transmission of sampled value buffer, the associated control block attribute “SvEna” shall be
written. However, if the client will read a list of available data sets or the contents of the data
set, further models (for example logical device, logical node or data set) need to be supported.
Data sets for sampled values will be specified by using the XML language on engineering level
in accordance with IEC 61850-6 to ensure interoperability.
For the transmission of sampled value data sets, the ASN.1 basic encoding rules (BER) will
be used in combination with tags notation harmonised with the MMS grammar used in
IEC 61850-8-1.
Mapping of IEC 61850-7-2 and IEC 61850-7-3 data attributes
The mapping of attributes and common data attributes to MMS are specified in IEC 61850-8-1.
For the transmission of sampled values the ASN.1, the basic encoding rules (BER) and the
common data classes defined in IEC 61850-7-3 apply.
Mapping of IEC 61850-7-2 classes and services
7.1
Classes of SV data sets
If a client/server association based on MMS is used in addition to the transmission of SV data
sets, the definitions of IEC 61850-8-1 apply for the following classes:
server class model;
association model;
logical device model;
logical node model;
data class model;
data set class model.
7.2
Definition of SV data sets
For the transmission of sampled values, the data sets are defined in logical node "LLN0". All
sampled value data sets specification are part of the IED configuration description (ICD).
NOTE It is assumed that the data sets used for the transmission of sampled values may include data objects from
more than one logical node and are therefore allocated in LLN0.
--- Page 20 ---
61850-9-2 © IEC:2011
Mapping of the model for the transmission of sampled values
8.1
Overview
To ensure interoperability, the data sets for sampled values are specified in XML according to
the definition in IEC 61850-6.
The sampled value class model provides reporting of sampled value data sets in an organised
and time controlled way, so that transfer is very fast and time of transfer is kept constant.
Sampled value control block for unicast and multicast defines the transmission characteristics
of the data set they refer to. A detailed description is given in IEC 61850-7-2.
8.2
Mapping of the multicast sampled value control block class and services
8.2.1
Multicast sampled value control block definition
The sampled value control block, as defined in IEC 61850-7-2, shall be pre-defined by
configuration or shall be mapped to an MMS Multicast sampled value control block (MSVCB) as
defined in Table 9. All MSVCB components shall be of the functional constraint “MS”.
Table 9 – MMS TypeDescription definition for MSVCB MMS structure
MMS component
name
MMS
TypeDescription
r/w
m/o
Condi-
tion
Comments
MsvCBNam
Identifier
MMS Identifier of the structure of the
MsvCBName within the MMS object named:
LLN0$MV e.g. LLN0$MS$<MsvCBNam>
MsvCBRef
Visible-string
The value of this component shall contain
the IEC Reference of the MsvCB.
e.g.
<MMSDomain>/LLN0$MS$<MsvCBNam>
SvEna
Boolean
r/w
TRUE = transmission of sampled value
buffer is activated.
FALSE = transmission of sampled value
buffer is deactivated.
MsvID
Visible-string
System-wide unique identification.
DatSet
Visible-string
The value of this component shall contain
the IEC reference of the DataSet conveyed
by the MsvCB. This ObjectReference shall be
limited to VMD or Domain scoped
NamedVariableLists.
ConfRev
Integer
Count of configuration changes regard to
MSVCB.
SmpRate
Integer
Amount of samples (default per nominal
period, see SmpMod).
OptFlds
BitString
refresh-time
Boolean
TRUE = SV buffer contains the attribute
“RefrTm”.
FALSE = attribute “RefrTm” is not available
in the SV buffer.
sample-
synchronised
Boolean
Value will be ignored. Kept to ensure
backward compatibility to IEC 61850-9-2
edition 1.0
sample-rate
Boolean
TRUE = SV buffer contains the attribute
“SmpRate”.
FALSE = attribute “SmpRate” is not available
in the SV buffer.
--- OCR Supplementary Text ---
-18- 61850-9-2 © IEC:2011
8 Mapping of the model for the transmission of sampled values
8.1 Overview
To ensure interoperability, the data sets for sampled values are specified in XML according to
the definition in IEC 61850-6.
The sampled value class model provides reporting of sampled value data sets in an organised
and time controlled way, so that transfer is very fast and time of transfer is kept constant.
Sampled value control block for unicast and multicast defines the transmission characteristics
of the data set they refer to. A detailed description is given in IEC 61850-7-2.
8.2 Mapping of the multicast sampled value control block class and services
8.2.1 Multicast sampled value control block definition
The sampled value control block, as defined in IEC 61850-7-2, shall be pre-defined by
configuration or shall be mapped to an MMS Multicast sampled value control block (MSVCB) as
defined in Table 9. All MSVCB components shall be of the functional constraint “MS”.
Table 9 - MMS TypeDescription definition for MSVCB MMS structure
MMS component MMS — tlw mio | Condi- Comments
name TypeDescription tion
MsvCBNam Identifier r MMS Identifier of the structure of the
MsvCBName within the MMS object named:
LLNO$MV e.g. LLNO$MS$<MsvCBNam>
MsvCBRef Visible-string r The value of this component shall contain
the IEC Reference of the MsvCB.
e.g.
<MMSDomain>/LLNO$MS$<MsvCBNam>
SvEna Boolean rlw TRUE = transmission of sampled value
buffer is activated.
FALSE = transmission of sampled value
buffer is deactivated.
Visibie-string[F[m |__| System-wide unique identification,
DatSet Visible-string r The value of this component shall contain
the IEC reference of the DataSet conveyed
by the MsvCB. This ObjectReference shall be
limited to VMD or Domain scoped
NamedVariableLists.
ConfRev Integer r Count of configuration changes regard to
MsvcB.
SmpRate Integer r Amount of samples (default per nominal
period, see SmpMod).
refresh-time Boolean r TRUE = SV buffer contains the attribute
“RefrTm"
FALSE = attribute “RefrTm’ is not available
in the SV buffer.
sample- Boolean r Value will be ignored. Kept to ensure
synchronised backward compatibility to IEC 61850-9-2
edition 1.0
sample-rate Boolean r TRUE = SV buffer contains the attribute
“SmpRate”.
FALSE = attribute “SmpRate” is not available
in the SV buffer.
--- Page 21 ---
61850-9-2 © IEC:2011
MMS component
name
MMS
TypeDescription
r/w
m/o
Condi-
tion
Comments
data-set
Boolean
TRUE = SV buffer contains the attribute
“DatSet”.
FALSE = attribute “DatSet” is not available in
the SV buffer.
security
Boolean
Mapping specific attribute.
TRUE = SV buffer contains the attribute
“Security”.
FALSE = attribute “Security” is not available
in the SV buffer.
SmpMod
Enumerated
smpMod specifies
0 = samples per nominal period (DEFAULT)
1 = samples per second
2 = seconds per sample
If not available (backward compatibility) the
default value is 0.
DstAddress
See Table 10
Mapping specific attribute.
noASDU
Integer
Mapping specific attribute.
Number of ASDU concatenated into one
APDU.
Table 10 – DstAddress structure
MMS component
name
MMS
TypeDescription
r/w
m/o
Condi-
tion
Comments
Addr
OCTET-STRING
Length is 6 octets and contains the value of
the destination media access control (MAC)
address to which the SV message is to be
sent.
If DstAddress is member of a MSVCB, the
address shall be an Ethernet address that
has the multicast bit set to TRUE. In order to
facilitate the network traffic filtering, it is
recommended to use different Ethernet
addresses for each DstAddress.
If DstAddress is member of a USVCB, the
address shall be the Ethernet address of the
SV subscriber.
See Annex B for multicast addressing
recommendations
PRIORITY
Unsigned8
Range of values shall be limited from 0 to 7.
VID
Unsigned16
r/w
Range of values shall be limited from 0 to
4095.
APPID
Unsigned16
As defined in 5.3.3.
8.2.2
MSV Services
See Table 11.
Table 11 – Mapping of multicast sampled value services
Services of MSVCB Class
Service
SendMSVMessage
Transmission of MSV messages is mapped directly on data
link layer as defined in 8.4 and 8.5
GetMSVCBValue
Mapped to MMS read service
SetMSVCBValue
Mapped to MMS write service
--- OCR Supplementary Text ---
61850-9-2 © IEC:2011 -19-
MMS component MMS — tlw mio | Condi- Comments
name TypeDescription tion
data-set Boolean r TRUE = SV buffer contains the attribute
“DatSet”.
FALSE = attribute “DatSet” is not available in
the SV buffer.
security Boolean r Mapping specific attribute
TRUE = SV buffer contains the attribute
“Security”.
FALSE = attribute “Security” is not available
in the SV buffer.
‘SmpMod Enumerated r smpMod specifies,
0 = samples per nominal period (DEFAULT)
1 = samples per second
2 = seconds per sample
If not available (backward compatibility) the
default value is 0.
DstAddress See Table 10 [ [Mm |__| Mapping specific attribute
noASDU Integer r Mapping specific attribute
Number of ASDU concatenated into one
APDU.
Table 10 — DstAddress structure
MMS component MMS rw mio | condi- Comments
name TypeDescription tion
‘Addr OCTET-STRING Length is 6 octets and contains the value of
the destination media access control (MAC)
address to which the SV message is to be
sent
If DstAddress is member of a MSVCB, the
address shall be an Ethernet address that
has the multicast bit set to TRUE. In order to
facilitate the network traffic filtering, it is
recommended to use different Ethernet
addresses for each DstAddress.
If DstAddress is member of a USVCB, the
address shall be the Ethernet address of the
SV subscriber.
See Annex B for multicast addressing
recommendations
PRIORITY Unsigned8 |r [m__ [|_| Range of values shall be limited from 0 to 7.
Unsigned16 a a Range of values shall be limited from 0 to
4095.
APPID Unsigned16 [rs [M_——|_—__s As defined in 5.3.3.
8.2.2 MSV Services
See Table 11.
Table 11 — Mapping of multicast sampled value services
SendMSVMessage Transmission of MSV messages is mapped directly on data
link layer as defined in 8.4 and 8.5
GetMSVCBValue Mapped to MMS read service
SetMSVCBValue Mapped to MMS write service
--- Page 22 ---
61850-9-2 © IEC:2011
8.3
Mapping of the unicast sampled value control block class and services
8.3.1
Unicast sampled value control block definition
The sampled value control block, as defined in IEC 61850-7-2, shall be pre-defined by
configuration or shall be mapped to an MMS unicast sampled value control block (USVCB) as
defined in Table 12. All USVCB components shall be of the functional constraint “US”.
Table 12 – MMS TypeDescription definition for USVCB MMS structure
MMS component
name
MMS type
description
r/w
m/o
Condi-
tion
Comments
UsvCBNam
Identifier
MMS Identifier of the structure of the
UsvCBName within the MMS object named:
LLN0$MV e.g. LLN0$US$<UsvCBNam>
UsvCBRef
Visible-string
The value of this component shall contain
the IEC Reference of the UsvCB.
e.g.
“<MMSDomain>/LLN0$US$<UsvCBNam>”
SvEna
Boolean
r/w
TRUE = transmission of sampled value
buffer is activated.
FALSE = transmission of sampled value
buffer is deactivated.
Resv
Boolean
r/w
TRUE = USVCB is exclusively reserved for
the client that has set this value to TRUE.
UsvID
Visible-string
System-wide unique identification.
DatSet
Visible-string
The value of this component shall contain
the IEC Reference of the DataSet conveyed
by the UsvCB. This ObjectReference shall be
limited to VMD or Domain scoped
NamedVariableLists.
ConfRev
Integer
Count of configuration changes regard to
USVCB.
SmpRate
Integer
Amount of samples (default per nominal
period see SmpMod).
OptFlds
BitString
refresh-time
Boolean
TRUE = SV buffer contains the attribute
“RefrTm”.
FALSE = attribute “RefrTm” is not available
in the SV buffer.
sample-
synchronised
Boolean
Value will be ignored. Kept to ensure
backward compatibility to IEC 61850-9-2
edition 1.0
sample-rate
Boolean
TRUE = SV buffer contains the attribute
“SmpRate”.
FALSE = attribute “SmpRate” is not available
in the SV buffer.
data-set
Boolean
TRUE = SV buffer contains the attribute
“DatSet”.
FALSE = attribute “DatSet” is not available in
the SV buffer.
security
Boolean
Mapping specific attribute.
TRUE = SV buffer contains the attribute
“Security”.
FALSE = attribute “Security” is not available
in the SV buffer.
--- OCR Supplementary Text ---
—20- 61850-9-2 © IEC:2011
8.3. Mapping of the unicast sampled value control block class and services
8.3.1 Unicast sampled value control block definition
The sampled value control block, as defined in IEC 61850-7-2, shall be pre-defined by
configuration or shall be mapped to an MMS unicast sampled value control block (USVCB) as
defined in Table 12. All USVCB components shall be of the functional constraint “US”.
Table 12 - MMS TypeDescription definition for USVCB MMS structure
MMS component MMS type tlw mio | condi- Comments
name description tion
UsvCBNam Identifier r MMS Identifier of the structure of the
UsvCBName within the MMS object named:
LLNO$MV e.g. LLNOSUS$<UsvCBNam>
UsvCBRef Visible-string r The value of this component shall contain
the IEC Reference of the UsvCB
eg.
“<MMSDomain>/LLNOSUS$<UsvCBNam>”
SvEna Boolean rlw TRUE = transmission of sampled value
buffer is activated.
FALSE = transmission of sampled value
buffer is deactivated.
Resv Boolean rlw TRUE = USVCB is exclusively reserved for
the client that has set this value to TRUE.
Visbiestring |e [M| | System-wide unique identification
DatSet Visible-string r The value of this component shall contain
the IEC Reference of the DataSet conveyed
by the UsvCB. This ObjectReference shall be
limited to VMD or Domain scoped
NamedVariableLists.
ConfRev Integer r Count of configuration changes regard to
usvcB.
SmpRate Integer r Amount of samples (default per nominal
period see SmpMod).
refresh-time Boolean r TRUE = SV buffer contains the attribute
“RefrTm".
FALSE = attribute “RefrTm’ is not available
in the SV buffer.
sample- Boolean r Value will be ignored. Kept to ensure
synchronised backward compatibility to IEC 61850-9-2
edition 1.0
sample-rate Boolean r TRUE = SV buffer contains the attribute
“SmpRate”,
FALSE = attribute “SmpRate” is not available
in the SV buffer.
data-set Boolean r TRUE = SV buffer contains the attribute
“DatSet’.
FALSE = attribute “DatSet’ is not available in
the SV buffer.
security Boolean r Mapping specific attribute.
TRUE = SV buffer contains the attribute
“Security”.
FALSE = attribute “Security” is not available
in the SV buffer.
--- Page 23 ---
61850-9-2 © IEC:2011
MMS component
name
MMS type
description
r/w
m/o
Condi-
tion
Comments
SmpMod
Enumerated
smpMod specifies
0 = samples per nominal period (DEFAULT)
1 = samples per second
2 = seconds per sample
If not available (backward compatibility) the
default value is 0.
DstAddress
See Table 10
Mapping specific attribute.
noASDU
Integer
Mapping specific attribute.
Number of ASDU concatenated into one
APDU.
8.3.2
USV Services
See Table 13.
Table 13 – Mapping of unicast sampled value services
Services of USVCB class
Service
SendUSVMessage
Transmission of USV messages is mapped directly on data
link layer as defined in 8.4 and 8.5
GetUSVCBValue
Mapped to MMS read service
SetUSVCBValue
Mapped to MMS write service
8.4
Mapping of the update of the sampled value buffer
As specified in IEC 61850-7-2, the communication system is responsible to update the buffer of
the subscriber.
The update is directly mapped to an ethertype reserved for IEC 61850 applications based on
ISO/IEC 8802-3 MAC – Sublayer.
The communication stack used does not provide the following functionality.
Initiating and checking the update of the sampled value buffer over the communication link.
Optionally concatenating the update of more than one buffer into the same link layer frame.
This is application layer functionality.
Encoding the abstract data types. This is presentation layer functionality.
Concatenating the update of more than one transmission buffer into the same link layer
frame as transport layer functionality is not supported. The opposite, to segment the update
of one buffer to several link layer frames is not considered, since the maximum frame
length of the link layer protocols is sufficient.
Translating the logical address of the subscriber in a physical MAC address.
Therefore, the additional definitions of 8.5 apply.
8.5
Additional definitions for the transmission of sampled values
8.5.1
Application layer functionality
The mapping provides the capability to concatenate more than one ASDU into one APDU before
the APDU is posted into the transmission buffer. The numbers of ASDUs which will be
concatenated into one APDU are configurable and related to the sample rate. The
--- Page 24 ---
61850-9-2 © IEC:2011
concatenation of ASDUs is not dynamically changeable in order to reduce the implementation
complexity. When concatenating several ASDUs into one frame, the ASDU with the oldest
samples is the first one in the frame.
Details are shown in Figure 4.
No. of ASDUs (UI16)
APDU (Application – Protocol Data Unit)
ASDU's (Application – Service Data Unit)
ASDU 1
APCI
(Application – Protocol Control Information)
ASDU 2
ASDU n
Length
Tag
Figure 4 – Concatenation of several ASDU's into one frame
ASN.1 grammar in relation with the basic encoding rules (BER) is used to encode the sampled
value messages for transmission on ISO/IEC 8802-3.
8.5.2
Presentation layer functionality
For the transmission, the sampled value buffer is encoded as specified in the Table 14.
Table 14 – Encoding for the transmission of the sampled value buffer
IEC61850 DEFINITIONS ::= BEGIN
IMPORTS Data FROM ISO-IEC-9506-2
IEC 61850-9-2 Specific Protocol ::= CHOICE {
savPdu      [APPLICATION 0] IMPLICIT SavPdu,
Abstract buffer format according to
IEC 61850-7-2
Coding in IEC 61850-9-2
Comments
Attribute name
Attribute type
ASN.1 basic encoding rules (BER)
SavPdu ::=
SEQUENCE {
noASDU [0] IMPLICIT INTEGER
(1..65535),
Mapping specific attribute.
Number of ASDUs, which will be
concatenated into one APDU.
security [1] ANY OPTIONAL,
Mapping specific attribute.
Reserved for future definition (e.g.
digital signature).
asdu [2] IMPLICIT SEQUENCE OF
ASDU
1 to n number of ASDUs as
specified before.
ASDU ::=
SEQUENCE {
MsvID or UsvID
VISIBLE STRING
svID [0] IMPLICIT VisibleString,
Should be a system-wide unique
identification.
DatSet
ObjectReference
datset [1] IMPLICIT VisibleString
OPTIONAL,
Value from the MSVCB or USVCB
IEC   1789/11
--- OCR Supplementary Text ---
-—22- 61850-9-2 © IEC:2011
concatenation of ASDUs is not dynamically changeable in order to reduce the implementation
complexity. When concatenating several ASDUs into one frame, the ASDU with the oldest
samples is the first one in the frame.
Details are shown in Figure 4.
APCI . | .
(Application — Protocol Control Information) ASDU's (Application — Service Data Unit)
No. of ASDUs (UI16) | ASDU 1 | ASDU 2 [eee] ASDU n
APDU (Application — Protocol Data Unit) lec 1789/11
Figure 4 - Concatenation of several ASDU's into one frame
ASN.1 grammar in relation with the basic encoding rules (BER) is used to encode the sampled
value messages for transmission on ISO/IEC 8802-3.
8.5.2 Presentation layer functionality
For the transmission, the sampled value buffer is encoded as specified in the Table 14.
Table 14 — Encoding for the transmission of the sampled value buffer
IEC61850 DEFINITIONS ::= BEGIN
IMPORTS Data FROM ISO-IEC-9506-2
IEC 61850-9-2 Specific Protocol ::= CHOICE {
savPdu [APPLICATION 0] IMPLICIT SavPdu,
Abstract buffer format according to Coding in IEC 61850-9-2 Comments
IEC 61850-7-2
Attribute name | Attribute type | ASN.1 basic encoding rules (BER)
SavPdu ::=
SEQUENCE {
NoASDU [0] IMPLICIT INTEGER Mapping specific attribute.
(1.65535), Number of ASDUs, which will be
concatenated into one APDU.
security [1] ANY OPTIONAL, Mapping specific attribute.
Reserved for future definition (e.g.
digital signature)
asdu [2] IMPLICIT SEQUENCE OF _|1 to n number of ASDUs as
ASDU specified before.
ASDU ::=
SEQUENCE {
MsvID or UsvID —_|VISIBLE STRING |svID [0] IMPLICIT VisibleString, Should be a system-wide unique
identification.
DatSet ObjectReference _|datset [1] IMPLICIT VisibleString \Value from the MSVCB or USVCB
OPTIONAL,
--- Page 25 ---
61850-9-2 © IEC:2011
Abstract buffer format according to
IEC 61850-7-2
Coding in IEC 61850-9-2
Comments
Attribute name
Attribute type
ASN.1 basic encoding rules (BER)
SavPdu ::=
SEQUENCE {
SmpCnt
INT16U
smpCnt [2] IMPLICIT OCTET STRING
(SIZE(2)),
Will be incremented each time a
new sampling value is taken. The
counter shall be set to zero if the
sampling is synchronised by clock
signal and the synchronising signal
occurs.
When sync pulses are used to
synchronise merging units, the counter
shall be set to zero with every sync
pulse. The value 0 shall be given to the
data set where the sampling of the
primary current coincides with the sync
pulse.
The OCTET STRING is interpreted
as INT16U as defined in Table 15.
ConfRev
INT32U
confRev [3] IMPLICIT OCTET STRING
(SIZE(4)),
Value from the MSVCB or USVCB.
The OCTET STRING is interpreted
as INT32U as defined in Table 15.
RefrTm
TimeStamp
refrTm [4] IMPLICIT UtcTime
OPTIONAL,
RefrTm contains the refresh time of
the SV buffer.
SmpSynch
INT8U
smpSynch [5] IMPLICIT OCTET
STRING (SIZE(1)),
0= SV are not synchronised by an
external clock signal.
1= SV are synchronised by a clock
signal from an unspecified local area
clock.
2= SV are synchronised by a global
area clock signal (time traceable).
5 to 254= SV are synchronised by a
clock signal from a local area clock
identified by this value.
3;4;255= Reserved values – Do not
use.
SmpRate
INT16U
smpRate [6] IMPLICIT OCTET
STRING (SIZE(2)) OPTIONAL,
Value from the MSVCB or USVCB.
The OCTET STRING is interpreted
as INT16U as defined in Table 15.
Sample [1..n]
Type depends on
the CDC defined in
IEC 61850-7-3.
sample [7] IMPLICIT OCTET STRING
(SIZE(n))
List of data values related to the
data set definition.
For the encoding of the Data, the
rules for the encoding of the basic
data types shall apply as defined in
Table 15.
The SIZE (n) is the cumulated size
of all the data conveyed as defined
in the DataSet.
SmpMod
INT16U
smpMod [8] IMPLICIT OCTET STRING
(SIZE(2)) OPTIONAL
Value from the MSVCB or USVCB.
The OCTET STRING is interpreted
as INT16U as defined in Table 15.
NOTE The usage of the OptFlds attribute according to IEC 61850-7-2 is not necessary, because the relating
attributes RefrTm, security, SmpRate and DatSet will be signed as optional via the ASN.1 attribute directly.
... }
END
--- OCR Supplementary Text ---
61850-9-2 © IEC:2011 -23-
Abstract buffer format according to Coding in IEC 61850-9-2 Comments
IEC 61850-7-2
Attribute name | Attribute type | ASN.1 basic encoding rules (BER)
SavPdu ::=
SEQUENCE {
SmpCnt INT16U ismpCnt [2] IMPLICIT OCTET STRING |Will be incremented each time a
(SIZE(2)), new sampling value is taken. The
counter shall be set to zero if the
sampling is synchronised by clock
signal and the synchronising signal
occurs.
When sync pulses are used to
synchronise merging units, the counter
shall be set to zero with every sync
pulse. The value O shall be given to the
data set where the sampling of the
primary current coincides with the sync
pulse.
The OCTET STRING is interpreted
jas INT16U as defined in Table 15.
ConfRev INT32U confRev [3] IMPLICIT OCTET STRING |Value from the MSVCB or USVCB.
(SIZE(4)), The OCTET STRING is interpreted
jas INT32U as defined in Table 15.
Refrtm TimeStamp refrTm [4] IMPLICIT UtcTime RefrTm contains the refresh time of
OPTIONAL, the SV buffer.
SmpSynch INTBU ismpSynch [5] IMPLICIT OCTET O= SV are not synchronised by an
STRING (SIZE(1)), external clock signal
1= SV are synchronised by a clock
signal from an unspecified local area
clock.
2= SV are synchronised by a global
area clock signal (time traceable)
5 to 254= SV are synchronised by a
clock signal from a local area clock
identified by this value.
3:4;255= Reserved values ~ Do not
use.
SmpRate INT16U ismpRate [6] IMPLICIT OCTET \Value from the MSVCB or USVCB.
STRING (SIZE(2)) OPTIONAL, The OCTET STRING is interpreted
jas INT16U as defined in Table 15.
Sample [1..n] Type depends on_|sample [7] IMPLICIT OCTET STRING |List of data values related to the
the CDC defined in |(SIZE(n)) data set definition.
IEC 61850-7-3.
For the encoding of the Data, the
rules for the encoding of the basic
data types shall apply as defined in
Table 15.
The SIZE (n) is the cumulated size
of all the data conveyed as defined
in the DataSet.
SmpMod INT16U smpMod [8] IMPLICIT OCTET STRING |Value from the MSVCB or USVCB.
(SIZE(2)) OPTIONAL The OCTET STRING is interpreted
} jas INT16U as defined in Table 15.
NOTE The usage of the OptF lds attribute according to IEC 61850-7-2 is not necessary, because the relating
attributes RefrTm, security, SmpRate and DatSet will be signed as optional via the ASN.1 attribute directly.
END
--- Page 26 ---
61850-9-2 © IEC:2011
For the tag definition of basic data types, see 8.6.
8.6
Definitions for basic data types – Presentation layer functionality
Table 15 shows the encoding for the basic data types used for the Data values referenced by
the data set members.
Table 15 – Encoding for the basic data types
Data types according to
IEC 61850-7-2
Encoding in data set
Comments
BOOLEAN
8 Bit set to 0 FALSE; anything else = TRUE
INT8
8 Bit Big Endian
signed
INT16
16 Bit Big Endian
signed
INT32
32 Bit Big Endian
signed
INT64
64 Bit Big Endian
signed
INT8U
8 Bit Big Endian
unsigned
INT16U
16 Bit Big Endian
unsigned
INT24U
24 Bit Big Endian
unsigned
INT32U
32 Bit Big Endian
unsigned
FLOAT32
32 Bit IEEE Floating Point (IEEE 754)
FLOAT64
64 Bit IEEE Floating Point (IEEE 754)
ENUMERATED
32 Bit Big Endian
CODED ENUM
32 Bit Big Endian
OCTET STRING
20 Bytes ASCII Text, Null terminated
VISIBLE STRING
35 Bytes ASCII Text, Null terminated
UNICODE STRING
20 Bytes ASCII Text, Null terminated
ObjectName
20 Bytes ASCII Text, Null terminated
ObjectReference
20 Bytes ASCII Text, Null terminated
TimeStamp
64 Bit Timestamp as defined in IEC 61850-8-1
EntryTime
48 Bit Timestamp as defined in IEC 61850-8-1
Data types according to
IEC 61850-8-1
Encoding in data set
Comments
BITSTRING
32 Bit Big Endian
Conformance
9.1
Notation
For Subclause 9.2 to Clause 11, see the abbreviations given in Clause 4.
9.2
PICS
9.2.1
Profile conformance
Table 16 and Table 17 define the basic conformance statement.
--- OCR Supplementary Text ---
—24- 61850-9-2 © IEC:2011
For the tag definition of basic data types, see 8.6.
8.6 Definitions for basic data types — Presentation layer functionality
Table 15 shows the encoding for the basic data types used for the Data values referenced by
the data set members.
Table 15 — Encoding for the basic data types
Data types according to Encoding in data set Comments
IEC 61850-7-2
BOOLEAN 8 Bit set to 0 FALSE; anything else = TRUE jf rti(i‘sSCSsés*”
FLOAT32 32 Bit IEEE Floating Point (IEEE 754) fi (isi—‘—s—sSrY
FLOAT64 64 Bit IEEE Floating Point (IEEE 754) f ti(‘itsSSSC—SY
OCTET STRING 20 Bytes ASCII Text, Null terminated ; t—‘“SOC*S
VISIBLE STRING 35 Bytes ASCII Text, Null terminated ; rti(‘s—s™Y
UNICODE STRING 20 Bytes ASCII Text, Null terminated fT twtiCSY
ObjectReference 20 Bytes ASCII Text, Null terminated f ti‘tSSOSCSY
Data types according to Encoding in data set Comments
IEC 61850-8-1
9 Conformance
9.1 Notation
For Subclause 9.2 to Clause 11, see the abbreviations given in Clause 4.
9.2 PICS
9.2.1 Profile conformance
Table 16 and Table 17 define the basic conformance statement.
--- Page 27 ---
61850-9-2 © IEC:2011
Table 16 – PICS for A-Profile support
Client
Server
Value/comment
F/S
F/S
Client/Server A-Profile
Refer to 5.2
SV A-Profile
Refer to 5.3
c1 – Shall be ‘m’ if support for any service specified in Table 1 is declared within the ACSI basic conformance
statement.
c2 – Shall be ‘m’ if support for any service specified in Table 4 is declared within the ACSI basic conformance
statement.
Table 17 – PICS for T-Profile support
Client
Server
Value/comment
F/S
F/S
TCP/IP T-Profile
SV T-Profile
c1 – Shall be ‘m’ if support for A1 is declared. Otherwise, shall be 'i'
c2 – Shall be 'm' if support for A2 is declared. Otherwise, shall be 'i'.
9.2.2
SV Services
This subclause describes the protocol implementation conformance statement for sampled
values services based on the IEC 61850-7-2 basic conformance statement. See Table 18.
Table 18 – SV conformance statement
Services
Client/
subscriber
Server/
publisher
Value/comment
Multicast
SendMSVMessage
GetMSVCBValues
SetMSVCBValues
Unicast
SendUSVMessage
GetUSVCBValues
SetUSVCBValues
c1 – Shall declare ‘m’ for at least one (MSV or USV) as declared within ACSI basic conformance statement.
c2 – Shall be ‘o’ as declared within ACSI basic conformance statement. See IEC 61850-8-1, Table 117 “Read
Conformance Statement”.
c3 – Shall be ‘o’ as declared within ACSI basic conformance statement. See IEC 61850-8-1, Table 118 “Write
Conformance Statement”.
10 Substation configuration language (SCL)
Conforming implementations shall support the substation configuration language as defined in
IEC 61850-6 for exchange between engineering tools.
--- OCR Supplementary Text ---
61850-9-2 © IEC:2011 —25-
Table 16 — PICS for A-Profile support
Po etn server | Vatucicomment |
poof es Ps PP
1 — Shall be ‘m’ if support for any service specified in Table 1 is declared within the ACSI basic conformance
statement
2 - Shall be ‘m’ if support for any service specified in Table 4 is declared within the ACSI basic conformance
statement
Table 17 — PICS for T-Profile support
Po etn server | Vatuctcomment |
poof es Ps
1 — Shall be ‘m’ if support for A1 is declared. Otherwise, shall be ‘i’
2 — Shall be 'm' if support for A2 is declared. Otherwise, shall be ‘i.
9.2.2 SV Services
This subclause describes the protocol implementation conformance statement for sampled
values services based on the IEC 61850-7-2 basic conformance statement. See Table 18.
Table 18 — SV conformance statement
Client/ Server!
publisher
1 — Shall declare ‘m’ for at least one (MSV or USV) as declared within ACSI basic conformance statement.
2 ~ Shall be ‘o' as declared within ACSI basic conformance statement. See IEC 61850-8-1, Table 117 “Read
Conformance Statement”.
3 — Shall be ‘o' as declared within ACSI basic conformance statement. See IEC 61850-8-1, Table 118 “Write
Conformance Statement”.
10 Substation configuration language (SCL)
Conforming implementations shall support the substation configuration language as defined in
IEC 61850-6 for exchange between engineering tools.
--- Page 28 ---
61850-9-2 © IEC:2011
11 SCSM specific address element definitions
This clause defines the xs:string types that are allowed for the SV addressing as type
parameters of the P element of the Address element. The values and character restrictions are
defined in Table 19.
Table 19 – Definitions for SV SCL
P-type designation
Description
m/o
Restrictions/comments
MAC-Address
Media Access Address
value
Shall be 6 groups of 2 visible characters
separated by hyphens (-). Characters shall be
limited to 0 to 9 and A to F.
APPID
Application Identifier
Shall be 4 characters.
Characters shall be limited to 0 to 9 and A to F.
VLAN-PRIORITY
VLAN User Priority
Shall be a single character.
Characters shall be limited to 0 to 7.
VLAN-ID
VLAN ID
Shall be 3 characters. Characters shall be
limited to 0 to 9 and A to F.
c1 – Shall only be present if VLAN is also present.
--- OCR Supplementary Text ---
—26- 61850-9-2 © IEC:2011
11 SCSM specific address element definitions
This clause defines the xs:string types that are allowed for the SV addressing as type
parameters of the P element of the Address element. The values and character restrictions are
defined in Table 19.
Table 19 — Definitions for SV SCL
P-type designation Description Restrictions/comments
MAC-Address Media Access Address Shall be 6 groups of 2 visible characters
value separated by hyphens (-). Characters shall be
limited to 0 to 9 and A to F.
APPID Application Identifier Shall be 4 characters.
Characters shall be limited to 0 to 9 and A to F.
VLAN-PRIORITY VLAN User Priority c1 Shall be a single character.
Characters shall be limited to 0 to 7.
VLAN-ID VLAN ID Shall be 3 characters. Characters shall be
limited to 0 to 9 and A to F.
c1 — Shall only be present if VLAN is also present.
--- Page 29 ---
61850-9-2 © IEC:2011
Annex A
(informative)
ISO/IEC 8802-3 frame format and ASN.1 basic encoding rules
A.1
ISO/IEC 8802-3 frame format
See Figures A.1, A.2 and A.3.
Octets
Notes
Preamble

It appears that you have provided an excerpt from a technical document of the International Electrotechnical Commission (IEC), titled "Networks and Communication Systems for Electrical Power System Automation – Part 9-2: Specific Communication Service Mapping (SCSM) – Sampled Values over ISO/IEC 8802-3".

Here is an analysis of the content:

**Scope**

Part 9-2 of IEC 61850 defines the specific communication service mapping (SCSM) for sampled value transmission, in accordance with the abstract specification of IEC 61850-7-2.

**Normative References**

The document references several standards and technical specifications, including:

* IEC/TR 61850-1: Networks and Communication Systems in Substations – Part 1: Introduction and Overview
* IEC/TS 61850-2: Glossary
* IEC 61850-6: Configuration Description Language for Communication in Electrical Substations-related to IEDs
* IEC 61850-7-1: Basic Communication Structure – Principles and Models
* IEC 61850-7-2: Abstract Communication Service Interface (ACSI)
* IEC 61850-8-1: Specific Communication Service Mapping (SCSM) – Mappings for MMS (ISO 9506-1 and ISO 9506-2) and for ISO/IEC 8802-3

**Content**

The document describes the specific communication service mapping (SCSM) for sampled value transmission, in accordance with the abstract specification of IEC 61850-7-2. It defines three parts:

1. Specification of the communication stack used
2. Mapping of the abstract specifications of the IEC 61850-7 series to the real elements of the stack used
3. Implementation specification of the functionality, not covered by the stack used

In summary, this document defines the specific communication service mapping (SCSM) for sampled value transmission, in accordance with the abstract specification of IEC 61850-7-2. It references several standards and technical specifications and defines three parts for the specific communication service mapping (SCSM).

---

# Images and Diagrams

## Page 1

![Image 1 from page 1](images/iec61850-9-2{ed2.0}b_page1_img1_28019bb6.jpeg)

**Image Type:** Diagram
**Description:** The image appears to be a technical diagram, possibly related to a system or process flow. It features a combination of curved and straight lines, circles, and dots, which suggest a structured layout. The lines and circles are arranged in a way that could represent connections, pathways, or stages in a process.

**Key Elements:**
- Curved and straight lines: These likely represent different paths or stages in a process.
- Circles: These could indicate nodes, points of interest, or stages in the process.
- Dots: These might represent specific points or data points within the system.
- Symmetry: The arrangement of lines and circles suggests a symmetrical or balanced design, which is common in technical diagrams to indicate a structured or organized system.

**Extracted Text:** No text detected

This diagram could be used in various technical contexts, such as process flow diagrams, network diagrams, or system architecture diagrams. The lack of text makes it challenging to determine the exact purpose or context, but the visual elements suggest a structured and organized system.

![Image 2 from page 1](images/iec61850-9-2{ed2.0}b_page1_img2_41ec8811.png)

**Image Type:** Logo

**Description:** The image is a logo consisting of the letters "IEC" in white, bold, sans-serif font. Below the letters, there are three horizontal lines and a small circle, also in white. The background is a solid blue color.

**Key Elements:**
- The letters "IEC" are prominently displayed in the center.
- The three horizontal lines are evenly spaced below the letters.
- The small circle is located at the end of the third line.

**Extracted Text:** IEC

This logo is likely associated with the International Electrotechnical Commission (IEC), an organization that develops international standards for electrical, electronic, and related technologies.

![Image 3 from page 1](images/iec61850-9-2{ed2.0}b_page1_img3_250cab8f.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document. It is designed to provide an overview of the document's title, edition, and key technical details. The document is related to communication networks and systems for power utility automation, specifically focusing on Part 9-2 of the standard.

**Key Elements:**
- **Title:** "INTERNATIONAL STANDARD"
- **Edition:** "Edition 2.0"
- **Date:** "2011-09"
- **Standard Number:** "IEC 61850-9-2"
- **Document Type:** "Communication networks and systems for power utility automation"
- **Specific Part:** "Part 9-2: Specific communication service mapping (SCSM) - Sampled values over ISO/IEC 8802-3"
- **Language:** The document is available in both English and French.
- **Contact Information:** Email and phone number for customer service inquiries.

**Extracted Text:**
```
IEC 61850-9-2
®
Edition 2.0 2011-09

INTERNATIONAL STANDARD

Communication networks and systems for power utility automation —
Part 9-2: Specific communication service mapping (SCSM) - Sampled values
over ISO/IEC 8802-3

Réseaux et systèmes de communication pour l'automatisation des systèmes
électriques —
Partie 9-2: Mise en correspondance des services de communication spécifiques
(SCSM) — Valeurs échantillonnées sur ISO/CEI 8802-3

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2012-000331 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

---

## Page 2

![Image 1 from page 2](images/iec61850-9-2{ed2.0}b_page2_img4_edd3df81.png)

**Image Type:** Warning Symbol

**Description:** The image depicts a standard warning symbol, commonly used in technical documentation and safety notices. It consists of a yellow triangle with a black border, and inside the triangle is a black exclamation mark, indicating a warning or caution.

**Key Elements:**
- **Shape:** Triangle
- **Color:** Yellow (background) and Black (border and exclamation mark)
- **Symbol:** Exclamation mark

**Extracted Text:** No text detected

This image is typically used to draw attention to a potential hazard or important caution in technical documents, ensuring that the reader is aware of the need for caution or safety measures.

![Image 2 from page 2](images/iec61850-9-2{ed2.0}b_page2_img5_aea0a6d0.png)

**Image Type:** Warning Symbol

**Description:** The image depicts a standard warning symbol, commonly used in technical documentation and safety notices. It consists of a yellow triangle with a black border, and inside the triangle is a black exclamation mark, which is universally recognized as a symbol for caution or warning.

**Key Elements:**
- **Shape:** Triangle
- **Color:** Yellow with a black border
- **Symbol:** Black exclamation mark

**Extracted Text:** No text detected

This image is typically used to indicate a potential hazard or cautionary message in technical documents, ensuring that the reader is aware of the importance of the information that follows.

---

## Page 3

![Image 1 from page 3](images/iec61850-9-2{ed2.0}b_page3_img6_41ec8811.png)

**Image Type:** Logo

**Description:** The image features a logo with a blue background and white text and design elements. The text "IEC" is prominently displayed in a bold, sans-serif font. Below the text, there are three horizontal lines and a small circle, which appear to be part of the logo's design.

**Key Elements:**
- Blue background
- White text "IEC"
- Three horizontal lines
- Small circle

**Extracted Text:** IEC

This logo is likely associated with the International Electrotechnical Commission (IEC), an organization that develops international standards for electrical and electronic equipment. The design elements suggest a modern and professional appearance, which is typical for organizations involved in technical standards.

![Image 2 from page 3](images/iec61850-9-2{ed2.0}b_page3_img7_b5030df9.png)

**Image Type:** Technical Document Cover Page

**Description:** The image is a cover page of a technical standard document published by the International Electrotechnical Commission (IEC). It is titled "IEC 61850-9-2" and is part of a series of standards related to communication networks and systems for power utility automation. The document is in its second edition, published in September 2011.

**Key Elements:**
- Title: "IEC 61850-9-2"
- Edition: 2.0
- Publication Date: 2011-09
- Standard Description: Communication networks and systems for power utility automation – Part 9-2: Specific communication service mapping (SCSM) – Sampled values over ISO/IEC 8802-3
- Language: The document is available in both English and French.
- Publisher: International Electrotechnical Commission (IEC)
- ISBN: 978-2-88912-631-6
- Price Code: V
- Additional Information: The document is subject to a license agreement and copyright by IEC, Geneva, Switzerland.

**Extracted Text:**

Edition 2.0 2011-09

INTERNATIONAL ELECTROTECHNICAL COMMISSION

Communication networks and systems for power utility automation – Part 9-2: Specific communication service mapping (SCSM) – Sampled values over ISO/IEC 8802-3

Réseaux et systèmes de communication pour l'automatisation des systèmes électriques – Partie 9-2: Mise en correspondance des services de communication spécifiques (SCSM) - Valeurs échantillonnées sur ISO/CEI 8802-3

INTERNATIONAL ELECTROTECHNICAL COMMISSION

COMMISSION ELECTROTECHNIQUE INTERNATIONALE

PRICE CODE V

CODE PRIX

ICS 33.200

ISBN 978-2-88912-631-6

Custernegisiladl Naceinarechtoe nfebhams(a))EléctrdteonnjpaopmbriangleMicroworks.. Order MerauNi@p86#2OO O98rissitd POA ANTUT hnlerfatoislecopyright of IEC, Geneva, Switzerland. All rights reserved.

Tel.: +41 22 919 0211

---

## Page 7

![Image 1 from page 7](images/iec61850-9-2{ed2.0}b_page7_img8_c8a1c267.png)

**Image Type:** OCR Text Image
**Description:** The image is a page from a technical document, specifically the second edition of IEC 61850-9-2, published in 2011. It provides an overview of the changes made in this edition compared to the first edition published in 2004. The document outlines the main technical revisions, including the addition of an optional Link redundancy layer, redefinition of "reserved" fields in the link layer, evolution of USVCB and MSVCB components, and the evolution of encoding for the transmission of the sampled value buffer.

**Key Elements:**
- **Edition Information:** Second edition, published in 2011, replaces the first edition from 2004.
- **Main Changes:**
  - Addition of an optional Link redundancy layer (Tables 3 to 6).
  - Redefinition of "reserved" fields in the link layer (*******).
  - Evolution of USVCB and MSVCB components (Tables 9, 10, 12).
  - Evolution of encoding for the transmission of the sampled value buffer (Table 14).
- **Drafting Compliance:** Drafted in accordance with ISO/IEC Directives, Part 2.
- **Publication Status:** Contents will remain unchanged until the stability date indicated on the IEC website.

**Extracted Text:**
61850-9-2 © IEC:2011 -5- This second edition cancels and replaces the first edition published in 2004 and constitutes a technical revision. Main changes with respect to the first edition are:
- addition of an optional Link redundancy layer (Tables 3 to 6);
- redefinition of "reserved" fields in link layer (*******);
- evolution of USVCB and MSVCB components (Tables 9, 10, 12);
- evolution of encoding for the transmission of the sampled value buffer (Table 14).
This publication has been drafted in accordance with the ISO/IEC Directives, Part 2.
A list of all parts of the IEC 61850 series, under the general title: Communication networks and systems for power utility automation, can be found on the IEC website.
The committee has decided that the contents of this publication will remain unchanged until the stability date indicated on the IEC web site under "http://webstore.iec.ch" in the data related to the specific publication. At this date, the publication will be
- reconfirmed,
- withdrawn,
- replaced by a revised edition, or
- amended.

---

## Page 8

![Image 1 from page 8](images/iec61850-9-2{ed2.0}b_page8_img9_299e18a4.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically from IEC 61850, which is a standard for communication in power systems. The page is the introduction to a part of the standard that defines the Sampled Value Communication Service Mapping (SCSM) for sampled values over ISO/IEC 8802-3. The document explains the purpose of the SCSM definition, its application to various electronic devices, and the necessary services for interoperability between devices from different manufacturers.

**Key Elements:**
- **SCSM Definition:** The document defines the SCSM for sampled values over ISO/IEC 8802-3.
- **Application:** The SCSM applies to electronic current and voltage transformers (ECT and EVT), merging units, and intelligent electronic devices.
- **Interoperability:** The SCSM allows interoperability between devices from different manufacturers.
- **Services:** References to IEC 61850-8-1 services are provided for accessing the SV control block.
- **Mapping:** The document defines the mapping of the sampled value class model (IEC 61850-7-2) to ISO/IEC 8802-3.
- **Reading Guide:** The document provides a reading guide for understanding the content, which includes references to other parts of the standard (IEC 61850-7-1, IEC 61850-7-2, IEC 61850-7-3, and IEC 61850-7-4).

**Extracted Text:**
```
-6- 61850-9-2 © IEC:2011
INTRODUCTION
This part of IEC 61850 defines the SCSM for sampled values over ISO/IEC 8802-3. The intent of this SCSM definition is to include the complete mapping of the sampled value model.
This part of IEC 61850 applies to electronic current and voltage transformers (ECT and EVT having a digital output), merging units, and intelligent electronic devices, for example protection units, bay controllers and meters, or sensors.
Process bus communication structures can be arranged in different ways as described in IEC/TR 61850-1. In addition to the transmission of sampled value data sets, which are directly connected to ISO/IEC 8802-3, a selection of IEC 61850-8-1 services is necessary to support the access to the SV control block. References to the relevant IEC 61850-8-1 services are provided in this SCSM. For less complex devices (for example merging units), the sampled value control block can be pre-configured, in which case there is no need to implement IEC 61850-8-1 services based on the MMS-Stack.
This document defines the mapping of sampled value class model (IEC 61850-7-2) to ISO/IEC 8802-3. This SCSM, in combination with IEC 61850-7 and IEC 61850-6, allows interoperability between devices from different manufacturers.
This standard does not specify individual implementations or products, nor does it constrain the implementation of entities and interfaces within a computer system. This standard specifies the externally visible functionality of implementations together with conformance requirements for such functionalities.
Reading guide:
- This document is an extended mapping specification of IEC 61850-8-1 to cover sampled value transmission over ISO/IEC 8802-3.
- This document can best be understood if the reader is thoroughly familiar with IEC 61850-7-1, IEC 61850-7-2, IEC 61850-7-3 and IEC 61850-7-4.
- The ACSI services defined in IEC 61850-7-2 are not explained in this part of IEC 61850.
```

This analysis provides a clear understanding of the content and structure of the technical document, which is essential for technical documentation and search purposes.

---

## Page 12

![Image 1 from page 12](images/iec61850-9-2{ed2.0}b_page12_img10_a959de15.png)

**Image Type:** Table

**Description:** The image is a technical table that provides definitions and explanations for various terms and acronyms related to communication protocols and standards, particularly in the context of the International Electrotechnical Commission (IEC) standards. The table is structured to provide a comprehensive overview of the terms used in the IEC 61850-9-2 standard, which is related to the communication aspects of intelligent electronic devices (IEDs) in power systems.

**Key Elements:**
- **Terms and Acronyms:** The table lists a variety of terms and their definitions, such as CSMA/CD, DF, DO, ECT, EVT, F/S, GOOSE, GSSE, ICD, IED, LSDU, MAC, MAU, MMS, MSVCB, MU, PDU, PICS, SCSM, TCI, TPID, USVCB, VID, VLAN, VMD, and XML.
- **Definitions:** Each term is accompanied by a detailed definition, explaining its role in the context of communication protocols and standards.
- **Scope and Implementation:** The table includes notes indicating which items are within or out of scope, and whether they are mandatory, optional, or excluded for implementation.

**Extracted Text:**

```
-10- 61850-9-2 © IEC:2011
CSMA/CD Carrier sense multiple access/collision detection
DF Data frame
DO Data object
ECT Electronic current transformer
EVT Electronic voltage transformer
F/S Functional standard
GOOSE Generic object oriented substation event
GSSE Generic substation status event
i Out-of-scope: The implementation of the item is not within the scope of this standard
ICD IED configuration description
IED Intelligent electronic device
LSDU Link layer service data unit
m Mandatory support. The item shall be implemented
MAC Media access control
MAU Medium attachment unit
MMS Manufacturing message specification (ISO 9506)
MSVCB Multicast sampled value control block
MU Merging unit
o Optional support. The implementor may decide to implement the item
PDU Protocol data unit
PICS Protocol implementation conformance statement
SCSM Specific communication services mapping
r readable
SV Sampled value
TCI Tag control information
TPID Tag protocol identifier
USVCB Unicast sampled value control block
VID VLAN identifier
VLAN Virtual local area network
VMD Virtual manufacturing device
w Writeable
x Excluded: The implementor shall not implement this item
XML Extensible markup language

5 Communication stack
5.1 Overview of the protocol usage
The OSI reference model (ISO/IEC 7498-1) defines a model based upon the concept of layering of communication functions. The model includes 7 layers and specifies the functional requirements for each layer to achieve a robust communication system. The model does not
```

This table is a crucial reference for anyone working with IEC 61850-9-2 standards, providing a clear and concise overview of the terminology and concepts used in the standard.

---

## Page 13

![Image 1 from page 13](images/iec61850-9-2{ed2.0}b_page13_img11_f6badaec.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram illustrating the OSI (Open Systems Interconnection) reference model and profiles. It includes a hierarchical representation of the OSI model with labeled layers and profiles. The diagram is accompanied by textual explanations that describe the use of A-Profiles and T-Profiles in the context of the OSI model.

**Key Elements:**
- **OSI Reference Model Layers:** Application, Presentation, Session, Transport, Network, DataLink, Physical.
- **A-Profile and T-Profile Definitions:**
  - A-Profile: Set of specifications and agreements relating to the upper three layers (Application, Presentation, and Session).
  - T-Profile: Set of specifications and agreements relating to the lower four layers (Transport, Network, DataLink, and Physical).
- **Figure 1 - OSI Reference Model and Profiles:**
  - Describes the various stack profiles and their relationship to the OSI model.
- **Client/Server Services and Communication Profiles:**
  - Client/server services based on MMS in accordance to IEC 61850-8-1.
  - SV services based on datalink layer.
- **Client/Server Services Section:**
  - Details the use of a client/server communication profile in addition to the SV communication profile.
  - Specifies the conditions under which this profile should be used, including access to the sampled value control block via client.
  - Indicates that this profile should be used for any implementation claiming conformance to the standard and declaring support for one of the IEC 61850-7-2 services.

**Extracted Text:**

```
61850-9-2 © IEC:2011 -11- specify the protocols to be used to achieve the functionality, nor does it restrict the solution to a single set of protocols.

Application
Presentation
Session
Transport
Network
DataLink
Physical

A-Profile
T-Profile

Figure 1 – OSI reference model and profiles

The use of ISO application (A-Profile) and transport (T-Profile) profiles (see Figure 1) describes the various stack profiles. An ISO A-Profile is the set of specifications and agreements relating to the upper three (3) layers of the ISO OSI reference model (for example the application, presentation, and session layers). An ISO T-Profile is the set of specifications and agreements relating to the lower four (4) layers of the ISO OSI reference model (for example the transport, network, datalink and physical layers).

Two combinations of A-Profiles and T-Profiles are defined in order to support the transmission of sampled values including the access to the associated SV control block, as specified in IEC 61850-7-2. The two different combinations are used for:
- client/server services based on MMS in accordance to IEC 61850-8-1;
- SV services based on datalink layer.

5.2 Client/server services and communication profiles

5.2.1 Client/server services

This client/server communication profile shall be used in addition to the SV communication profile according to 5.3 if an access to the sampled value control block via client is required. This profile shall be used for any implementation claiming conformance to this standard and declaring support for one of the following IEC 61850-7-2 services in Table 1.
```

This technical analysis provides a clear understanding of the OSI reference model and the specific profiles (A-Profile and T-Profile) used in the context of the IEC 61850 standard, which is crucial for implementing and conforming to the standard in the field of smart grid communication.

---

## Page 14

![Image 1 from page 14](images/iec61850-9-2{ed2.0}b_page14_img12_713cc820.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the services and protocols required for client/server communication in the context of the IEC 61850-2 model. It is divided into two main sections: Table 1, which lists services and protocols for a specific profile, and Table 2, which provides a more detailed breakdown of services and protocols for the A-Profile client/server communication.

**Key Elements:**

- **Table 1: Service requiring client/server communication profile**
  - Lists various services and protocols related to the IEC 61850-2 model, including operations such as GetServerDirectory, Associate, Abort, Release, GetLogicalDeviceDirectory, GetLogicalNodeDirectory, GetLogicalNodeDirectory, GetAllDataValues, GetDataValues, SetDataValues, CreateDataSet, DeleteDataSet, GetDataSetValues, SetDataSetValues, GetMSVCBValues, SetMSVCBValues, GetUSVCBValues, SetUSVCBValues, GetSVCBValues, and SetSVCBValues.

- **Table 2: Service and protocols for client/server communication A-Profile**
  - Breaks down the services and protocols into different OSI model layers: Application, Presentation, and Session.
  - Lists specific services and protocols for each layer, such as Manufacturing message specification (ISO 9506-1:2000), Association control service element (ISO/IEC 8649:1996), Connection oriented presentation (ISO/IEC 8822:1996), and Connection oriented session (ISO/IEC 8326:1994).

- **A-Profile**
  - Specifies that there is only one T-Profile (TCP/IP) that may be used by the client/server A-Profile.

**Extracted Text:**

```
-12- 61850-9-2 © IEC:2011
Table 1 – Service requiring client/server communication profile
Table 2 shows services and protocols of the A-Profile client/server.
Table 2 – Service and protocols for client/server communication A-Profile
OSI model layer
Name
Specification
Protocol
m/o
Application
Manufacturing message specification
ISO 9506-1:2000
ISO 9506-2:2000
m
Presentation
Connection oriented presentation
ISO/IEC 8822:1996
ISO/IEC 8825-1
m
Session
Connection oriented session
ISO/IEC 8326:1994
ISO/IEC 8822-1:1994
m
There is only one T-Profile (TCP/IP) that may be used by the client/server A-Profile.
```

This table provides a structured overview of the services and protocols necessary for client/server communication in the IEC 61850-2 model, with a specific focus on the A-Profile.

---

## Page 15

![Image 1 from page 15](images/iec61850-9-2{ed2.0}b_page15_img13_c3d968d6.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the service and protocol specifications for the TCP/IP T-Profile client/server. It is structured to show the OSI model layers and the corresponding services and protocols for each layer, along with their respective specifications and status (m/o, m, c1). The table also includes a note about the ST connector specification and a reference to a service and communication profile for the SV (Sampled Value) communication.

**Key Elements:**

- **OSI Model Layers:** Transport, Network, DataLink, Physical.
- **Services and Protocols:** ISO transport on top of TCP, Internet control message protocol (ICMP), Transmission control protocol (TCP), Converting network protocol address, Broadcasting internet datagrams, Host extensions for IP multicasting, Parallel redundancy protocol, Carrier sense multiple access with collision detection (CSMA/CD), Fibre optic transmission system 100Base-FX, Basic optical fibre connector.
- **Specifications:** RFC 1006, RFC 792, RFC 793, RFC 826, RFC 919, RFC 1112, IEC 62439-3, Amendment 1, RFC 894, ISO/IEC 8802-3:2000, ISO/IEC 8802-3:2000, IEC 60874-10-1, IEC 60874-10-2, IEC 60874-10-3.
- **Status Indicators:** m/o, m, c1.
- **SV Communication Profile:** This profile is used for any implementation claiming conformance to the standard and declaring support for the IEC 61850-7-2 services.

**Extracted Text:**

```
61850-9-2 © IEC:2011 = 13 - 5.2.3 TCP/IP T-Profile
Table 3 shows services and protocols of the TCP/IP T-Profile client/server.
Table 3 – Service and protocols for peer TCP/IP T-Profile
OSI model / layer
Name
Specification
Service
Protocol
m/o
Transport
ISO transport on top of TCP
RFC 1006
[ om
[ om
Network
Internet protocol
RFC 791
[ om
[ om
Converting network protocol address
RFC 826 (Address resolution protocol: ARP)
[ om
[ om
Broadcasting internet datagrams
RFC 919
[ om
[ om
Host extensions for IP multicasting
RFC 1112
[ om
[ om
Link
Parallel redundancy protocol and high
IEC 62439-3, Amendment 1
Redundancy
availability seamless ring
DataLink
Standard for the transmission of IP datagrams
RFC 894
over Ethernet networks
Carrier sense multiple access with collision
ISO/IEC 8802-3:2000
detection (CSMA/CD)
Physical
Fibre optic transmission system 100Base-FX
ISO/IEC 8802-3:2000
Basic optical fibre connector
IEC 60874-10-1, IEC 60874-10-2
ct
NOTE This is the specification for the ST
and IEC 60874-10-3
connector.
c1 — Recommended, but future technology could be used.
5.3. SV service and communication profile
5.3.1 SV mapping overview
This SV communication profile shall be used for any implementation claiming conformance to
this standard and declaring support for one of the following IEC 61850-7-2 services in Table 4.
Table 4 – Service requiring SV communication profile
Model
IEC 61850-7-2 service
Multicast sampled value class model
Multicast SV message
Unicast sampled value class model
Unicast SV message
```

This table provides a comprehensive overview of the TCP/IP T-Profile services and protocols, along with their specifications and status, which is crucial for understanding the technical requirements and compliance with the IEC 61850 standard.

---

## Page 16

![Image 1 from page 16](images/iec61850-9-2{ed2.0}b_page16_img14_767ee290.png)

**Image Type:** Table

**Description:** The image is a technical table that outlines the service and protocol specifications for the A-Profile and T-Profile of SV communication, as defined in the OSI model. The table is structured to show the specifications for each layer of the OSI model, including Application, Presentation, Session, Link, Network, DataLink, and Physical layers. It provides details on the service specifications, protocol specifications, and the status of each specification (m/o, m, o, c1).

**Key Elements:**
- **Table Title:** "Table 5 – Service and protocols for SV communication A-Profile"
- **Table Title:** "Table 6 – SV T-Profile"
- **OSI Model Layers:** Application, Presentation, Session, Link, Network, DataLink, Physical
- **Service Specifications:** SV service, Abstract syntax, Parallel redundancy protocol and high availability seamless ring, Priority tagging/VLAN, Carrier sense multiple access with collision detection (CSMA/CD), Fibre optic transmission system 100Base-FX, Basic optical fibre connector
- **Protocol Specifications:** IEC 62439-3, Amendment 1, IEEE 802.1Q, ISO/IEC 8802-3:2000, ISO/IEC 60874-10-1, ISO/IEC 60874-10-2, ISO/IEC 60874-10-3
- **Status Indicators:** m/o, m, o, c1 (m/o: mandatory and optional, m: mandatory, o: optional, c1: recommended, but future technology could be used)

**Extracted Text:**

```
-14- 61850-9-2 © IEC:2011
5.3.2 A-Profile
Table 5 shows services and protocols of the A-Profile SV.
Table 5 – Service and protocols for SV communication A-Profile
OSI model layer
Name
Specification
Service specification
Protocol specification
m/o
Application
SV service
m
Presentation
Abstract syntax
ISO/IEC 8824-1:2008
ISO/IEC 8825-1
m
Session
Abstract syntax
ISO/IEC 8824-1:2008
ISO/IEC 8825-1
m
Presentation layer: see additional definitions in 8.5.
Application layer: see additional definitions in 8.5.
5.3.3 T-Profile
The T-Profile for SV services is shown in Table 6.
Table 6 – SV T-Profile
OSI model layer
Name
Specification
Service specification
Protocol specification
m/o
Link
Parallel redundancy protocol and high availability seamless ring
IEC 62439-3, Amendment 1
o
Redundancy
DataLink
Priority tagging/VLAN
IEEE 802.1Q, Amendment 1
m
Carrier sense multiple access with collision detection (CSMA/CD)
ISO/IEC 8802-3:2000
m
Physical
Fibre optic transmission system 100Base-FX
ISO/IEC 8802-3:2000
c1
Basic optical fibre connector
IEC 60874-10-1, IEC 60874-10-2 and IEC 60874-10-3
c1
c1 – Recommended, but future technology could be used.
******* Physical layer: Specifications for the medium attachment unit (MAU)
The optical fibre transmission system 100Base-FX according to ISO/IEC 8802-3 is recommended as indicated above because of requirements relating to the electromagnetic environment.
******* Link layer: Ethernet addresses
The destination ISO/IEC 8802-3 multicast/unicast address has to be configured for the transmission of sampled values. A unique ISO/IEC 8802-3 source address shall be used. Recommendations of multicast address range assignments are given in Annex B.
```

This technical document provides a detailed breakdown of the service and protocol specifications for the A-Profile and T-Profile of SV communication, adhering to the OSI model and IEC standards.

---

## Page 18

![Image 1 from page 18](images/iec61850-9-2{ed2.0}b_page18_img15_85ac5bad.png)

**Image Type:** Table and Text

**Description:** The image contains a table titled "Table 8 – Assigned Ethertype values" and accompanying text that describes the structure and usage of the Ethertype PDU and APDU octets in the context of IEC 61850-9-2. The text explains the APPID, Length, and Reserved 1 fields, their values, and their significance in the protocol.

**Key Elements:**
- **Table 8 – Assigned Ethertype values:**
  - Lists Ethertype values and their corresponding APPID types.
  - Ethertype values are in hexadecimal format.
  - APPID types are defined as 00 for GOOSE and 01 for Sampled Values.

- **APPID:**
  - Used to select ISO/IEC 8802-3 frames containing sampled value messages.
  - Value is the combination of APPID type and actual ID.

- **Length:**
  - Number of octets including the Ethertype PDU header starting at APPID.
  - Value is 8 + m, where m is the length of the APDU and must be less than 1493.

- **Reserved 1:**
  - Structure defined in Figure 3.
  - S: Simulate flag.
  - R: Reserved flag.
  - Reserved security: Referenced in Reserved 2.

**Extracted Text:**

```
-16- 61850-9-2 © IEC:2011
Table 8 – Assigned Ethertype values

Use     Ethertype value (hexadecimal)    APPID type
IEC 61850-8-1 GOOSE                      88-B8          0 0
IEC 61850-8-1 GSE Management             88-B9          0 0
IEC 61850-9-2 Sampled Values              88-BA          0 1

The Ethertype PDU and APDU octets shall be as defined in Annex A.

*******.2 APPID
Application identifier. The APPID is used to select ISO/IEC 8802-3 frames containing sampled value messages and to distinguish the application association. The value of APPID is the combination of the APPID type, defined as the two most significant bits of the value (as defined in Table 8), and the actual ID.

The reserved value range for sampled values is 0x4000 to 0x7FFF. If no APPID is configured, the default value shall be 0x4000. The default value is reserved to indicate lack of configuration. It is strongly recommended to have unique, source orientated SV APPID within a system, in order to enable a filter on link layer. The configuration of APPID should be enforced by the configuration system.

*******.3 Length
Number of octets including the Ethertype PDU header starting at APPID, and the length of the APDU (Application Protocol Protocol Data Unit). Therefore, the value of Length shall be 8 + m, where m is the length of the APDU and m is less than 1493. Frames with inconsistent or invalid length field shall be discarded.

*******.4 Reserved 1
The structure of the Reserved 1 is defined in Figure 3.

Octets | 8 | 7 | 6 | 5 | 4 | 3 | 2 | 1
0      | S | R | Reserved Security

Figure 3 – Reserved 1

S: Simulate. When this flag is set, the SampledValue telegram has been issued by a publisher located in a test device and not by the publisher as specified in the configuration file of the device.

R: Reserved. The three bits are reserved for future standardized application and shall be set to 0 as default.

Reserved security: See reserved 2 below.
```

This technical analysis provides a clear understanding of the content and structure of the image, which is crucial for technical documentation and search purposes.

---

## Page 21

![Image 1 from page 21](images/iec61850-9-2{ed2.0}b_page21_img16_efb0bc31.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically from IEC 61850-9-2, published in 2011. It details the mapping of Multicast Sampled Value (MSV) services and their attributes within the context of the Message Specification (MMS) protocol. The table outlines the components of the MMS structure, their types, read/write (r/w) permissions, mandatory/optional (m/o) status, conditions, and comments.

**Key Elements:**
- **MMS component name**: Lists the names of the MMS components.
- **MMS TypeDescription**: Describes the type of each component.
- **r/w**: Indicates whether the component can be read (r), written (w), or both (r/w).
- **m/o**: Indicates whether the component is mandatory (M) or optional (O).
- **Condition**: Provides conditions under which the component is applicable.
- **Comments**: Offers additional information and explanations.

**Extracted Text:**

```
61850-9-2 © IEC:2011 -19-

MMS component MMS — tlw mio I Condi- Comments
name TypeDescription tion
data-set Boolean r TRUE = SV buffer contains the attribute “DatSet”. FALSE = attribute “DatSet” is not available in the SV buffer.
security Boolean r Mapping specific attribute TRUE = SV buffer contains the attribute “Security”. FALSE = attribute “Security” is not available in the SV buffer.
SmpMod Enumerated r smpMod specifies, O = samples per nominal period (DEFAULT) 1 = samples per second 2 = seconds per sample If not available (backward compatibility) the default value is O.
DstAddress See Table 10 [ [Mm I__I Mapping specific attribute
noASDU Integer r Mapping specific attribute Number of ASDU concatenated into one APDU.

Table 10 — DstAddress structure
MMS component MMS rw mio I condi- Comments
name TypeDescription tion
Addr OCTET-STRING Length is 6 octets and contains the value of the destination media access control (MAC) address to which the SV message is to be sent If DstAddress is member of a MSVCB, the address shall be an Ethernet address that has the multicast bit set to TRUE. In order to facilitate the network traffic filtering, it is recommended to use different Ethernet addresses for each DstAddress. If DstAddress is member of a USVCB, the address shall be the Ethernet address of the SV subscriber. See Annex B for multicast addressing recommendations
PRIORITY Unsigned8 Ir [m__ [I_I Range of values shall be limited from O to 7.
VID Unsigned16 a a Range of values shall be limited from O to 4095.
APPID Unsigned16 [rs [M_——I_—__s As defined in 5.3.3.

8.2.2 MSV Services
See Table 11.

Table 11 — Mapping of multicast sampled value services
Services of MSVCB Class Service
SendMSVMessage Transmission of MSV messages is mapped directly on data link layer as defined in 8.4 and 8.5
GetMSVCBValue Mapped to MMS read service
SetMSVCBValue Mapped to MMS write service
```

**Technical Analysis:**
The table provides a structured overview of the MMS components related to MSV services, their attributes, and their roles within the MMS framework. It is designed to help in the implementation and understanding of how MSV messages are structured and managed in a networked environment. The comments section offers additional context and recommendations, such as the use of different Ethernet addresses for multicast addresses to facilitate network traffic filtering. The table also references other sections (e.g., Table 10 and Table 11) for more detailed information on specific attributes and services.

---

## Page 24

![Image 1 from page 24](images/iec61850-9-2{ed2.0}b_page24_img17_0a05a280.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page that includes a diagram and a table. The diagram illustrates the structure of an Application Protocol Data Unit (APDU) and the concatenation of several Application Service Data Units (ASDUs) into one frame. The table provides detailed encoding definitions for the transmission of sampled value buffer data according to IEC 61850-9-2.

**Key Elements:**
- **Diagram:**
  - **APCI (Application Protocol Control Information):** This is the header of the APDU.
  - **ASDU's (Application Service Data Unit):** These are the data units that are concatenated into one frame.
  - **No. of ASDUs (UI16):** This field indicates the number of ASDUs in the frame.
  - **ASDU 1, ASDU 2, ..., ASDU n:** These represent the individual ASDUs within the frame.
  - **APDU (Application Protocol Data Unit):** This is the complete unit that includes the APCI and the concatenated ASDUs.

- **Table:**
  - **Abstract buffer format according to IEC 61850-7-2:** This table defines the structure of the sampled value buffer according to IEC 61850-7-2.
  - **Coding in IEC 61850-9-2:** This column specifies the encoding rules for the sampled value buffer.
  - **Comments:** This column provides additional information about the encoding and its purpose.

**Extracted Text:**

```plaintext
—22— 61850-9-2 © IEC:2011
concatenation of ASDUs is not dynamically changeable in order to reduce the implementation complexity. When concatenating several ASDUs into one frame, the ASDU with the oldest samples is the first one in the frame. Details are shown in Figure 4.
APCI (Application – Protocol Control Information)
ASDU's (Application – Service Data Unit)
No. of ASDUs (UI16)
ASDU 1
ASDU 2
ASDU n
APDU (Application – Protocol Data Unit)
Figure 4 – Concatenation of several ASDU's into one frame
ASN.1 grammar in relation with the basic encoding rules (BER) is used to encode the sampled value messages for transmission on ISO/IEC 8802-3.
8.5.2 Presentation layer functionality
For the transmission, the sampled value buffer is encoded as specified in the Table 14.
Table 14 – Encoding for the transmission of the sampled value buffer
IEC61850 DEFINITIONS ::= BEGIN
IMPORTS Data FROM ISO-IEC-9506-2
IEC 61850-9-2 Specific Protocol ::= CHOICE {
savPdu [APPLICATION 0] IMPLICIT SavPdu,
}
Abstract buffer format according to IEC 61850-7-2
Coding in IEC 61850-9-2
Comments
Attribute name Attribute type ASN.1 basic encoding rules (BER)
SavPdu ::= SEQUENCE {
NoASDU [0] IMPLICIT INTEGER (1..65535), Mapping specific attribute.
security [1] ANY OPTIONAL, Mapping specific attribute.
asdu [2] IMPLICIT SEQUENCE OF ASDU 1 to n number of ASDUs as specified before.
ASDU ::= SEQUENCE {
MsvID or UsvID VISIBLE STRING svID [0] IMPLICIT VisibleString, Should be a system-wide unique identification.
DataSet ObjectReference dataset [1] IMPLICIT VisibleString \Value from the MSVCB or USVCB OPTIONAL,
}
```

This technical document page is part of a larger standard document related to the IEC 61850 protocol, specifically addressing the encoding and transmission of sampled value data.

---

## Page 25

![Image 1 from page 25](images/iec61850-9-2{ed2.0}b_page25_img18_809fe94b.png)

**Image Type:** Table

**Description:** The image is a technical table detailing the abstract buffer format according to IEC 61850-9-2. It outlines the coding and comments for various attributes within the buffer format, adhering to the ASN.1 basic encoding rules (BER). The table includes columns for Attribute name, Attribute type, Coding in IEC 61850-9-2, and Comments.

**Key Elements:**
- **Attribute name:** Lists the names of the attributes, such as SmpCnt, ConfRev, RefrTm, SmpSynch, SmpRate, Sample, and SmpMod.
- **Attribute type:** Specifies the data type of each attribute, such as INT16U, INT32U, TimeStamp, and OCTET STRING.
- **Coding in IEC 61850-9-2:** Provides the encoding rules for each attribute, including the sequence and implicit OCTET STRING.
- **Comments:** Offers additional information about the usage and interpretation of each attribute, such as the meaning of the values and the conditions under which certain values are used.

**Extracted Text:**

```
61850-9-2 © IEC:2011 -23- Abstract buffer format according to Coding in IEC 61850-9-2 Comments IEC 61850-7-2 Attribute name I Attribute type I ASN.1 basic encoding rules (BER) SavPdu ::= SEQUENCE { SmpCnt INT16U ismpCnt [2] IMPLICIT OCTET STRING (SIZE(2)), Will be incremented each time a new sampling value is taken. The counter shall be set to zero if the sampling is synchronised by clock signal and the synchronising signal occurs. When sync pulses are used to synchronise merging units, the counter shall be set to zero with every sync pulse. The value 0 shall be given to the data set where the sampling of the primary current coincides with the sync pulse. The OCTET STRING is interpreted as INT16U as defined in Table 15. ConfRev INT32U confRev [3] IMPLICIT OCTET STRING (SIZE(4)), Value from the MSVCB or USVCB. The OCTET STRING is interpreted as INT32U as defined in Table 15. RefrTm TimeStamp refrTm [4] IMPLICIT UtcTime RefrTm contains the refresh time of OPTIONAL, the SV buffer. SmpSynch INT8U ismpSynch [5] IMPLICIT OCTET STRING (SIZE(1)), 0= SV are not synchronised by an external clock signal. 1= SV are synchronised by a clock signal from an unspecified local area clock. 2= SV are synchronised by a global area clock signal (time traceable). 5 to 254= SV are synchronised by a clock signal from a local area clock identified by this value. 3:4;255= Reserved values – Do not use. SmpRate INT16U ismpRate [6] IMPLICIT OCTET STRING (SIZE(2)) OPTIONAL, Value from the MSVCB or USVCB. The OCTET STRING is interpreted as INT16U as defined in Table 15. Sample [1..n] Type depends on the CDC defined in IEC 61850-7-3. sample [7] IMPLICIT OCTET STRING (SIZE(n)) IList of data values related to the data set definition. For the encoding of the Data, the rules for the encoding of the basic data types shall apply as defined in Table 15. The SIZE (n) is the cumulated size of all the data conveyed as defined in the DataSet. SmpMod INT16U smpMod [8] IMPLICIT OCTET STRING (SIZE(2)) OPTIONAL Value from the MSVCB or USVCB. The OCTET STRING is interpreted as INT16U as defined in Table 15. NOTE The usage of the OptF lds attribute according to IEC 61850-7-2 is not necessary, because the relating attributes RefrTm, security, SmpRate and DatSet will be signed as optional via the ASN.1 attribute directly. END
```

This table is crucial for understanding the structure and encoding of data in IEC 61850-9-2, which is used in smart grid communication standards.

---

## Page 26

![Image 1 from page 26](images/iec61850-9-2{ed2.0}b_page26_img19_66e1e297.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard document, specifically IEC 61850-9-2, published in 2011. It provides a detailed encoding for basic data types used in the IEC 61850 standard, which is commonly used in the automation of electric power systems. The table lists various data types along with their encoding in a data set and includes comments for each type.

**Key Elements:**
- **Data Types:** The table lists various data types such as BOOLEAN, INT8, INT16, INT32, INT64, FLOAT32, FLOAT64, ENUMERATED, OCTET STRING, VISIBLE STRING, UNICODE STRING, ObjectName, ObjectReference, and EntryTime.
- **Encoding in Data Set:** Each data type is associated with a specific encoding format, such as 8-bit, 16-bit, 32-bit, or 64-bit, depending on the data type.
- **Comments:** Additional information is provided for each data type, such as the interpretation of the encoding (e.g., 8-bit set to 0 for FALSE, anything else is TRUE for BOOLEAN).

**Extracted Text:**

```
—24— 61850-9-2 © IEC:2011
For the tag definition of basic data types, see 8.6.
8.6 Definitions for basic data types – Presentation layer functionality
Table 15 shows the encoding for the basic data types used for the Data values referenced by the data set members.
Table 15 – Encoding for the basic data types
Data types according to Encoding in data set Comments
IEC 61850-7-2 BOOLEAN 8 Bit set to 0 FALSE; anything else = TRUE
INT8 8 Bit Big Endian signed
INT16 16 Bit Big Endian signed
INT32 32 Bit Big Endian signed
INT64 64 Bit Big Endian signed
FLOAT32 32 Bit IEEE Floating Point (IEEE 754)
FLOAT64 64 Bit IEEE Floating Point (IEEE 754)
OCTET STRING 20 Bytes ASCII Text, Null terminated
VISIBLE STRING 35 Bytes ASCII Text, Null terminated
UNICODE STRING 20 Bytes ASCII Text, Null terminated
ObjectName 20 Bytes ASCII Text, Null terminated
ObjectReference 20 Bytes ASCII Text, Null terminated
EntryTime 48 Bit Timestamp as defined in IEC 61850-8-1
Data types according to Encoding in data set Comments
IEC 61850-8-1
9 Conformance
9.1 Notation
For Subclause 9.2 to Clause 11, see the abbreviations given in Clause 4.
9.2 PICS
9.2.1 Profile conformance
Table 16 and Table 17 define the basic conformance statement.
```

This table is crucial for understanding the encoding of basic data types in the IEC 61850 standard, which is essential for implementing and interpreting data in smart grid and automation systems.

---

## Page 27

![Image 1 from page 27](images/iec61850-9-2{ed2.0}b_page27_img20_dac94033.png)

**Image Type:** Table

**Description:** The image consists of two tables (Table 16 and Table 17) and a section describing SV Services and Substation Configuration Language (SCL). These tables provide a protocol implementation conformance statement (PICS) for A-Profile and T-Profile support, as well as a conformance statement for sampled values (SV) services. The text also includes a reference to the IEC 61850-7-2 and IEC 61850-8-1 standards.

**Key Elements:**

- **Table 16 – PICS for A-Profile support:**
  - Columns: Client, Server, Value/comment
  - Rows: A1, A2, with sub-rows c1 and c2
  - Comments: c1 and c2 refer to specific conditions for declaring support for A1 and A2 profiles within the ACSI basic conformance statement.

- **Table 17 – PICS for T-Profile support:**
  - Columns: Client, Server, Value/comment
  - Rows: T1, T2, with sub-rows c1 and c2
  - Comments: c1 and c2 refer to specific conditions for declaring support for T1 and T2 profiles within the ACSI basic conformance statement.

- **SV Services:**
  - This subclause describes the protocol implementation conformance statement for sampled values services based on the IEC 61850-7-2 basic conformance statement.
  - It includes a reference to Table 18 for SV conformance statement details.

- **Table 18 – SV conformance statement:**
  - Columns: Services, Client/ subscriber, Server/ publisher, Value/comment
  - Services: Multicast, SendMSVMessage, GetMSVCBValues, SetMSVCBValues, Unicast, SendUSVMessage, SendUSVCBValues, GetUSVCBValues, SetUSVCBValues
  - Comments: c1, c2, and c3 refer to specific conditions for declaring support for MSV and USV services within the ACSI basic conformance statement.

- **Substation Configuration Language (SCL):**
  - Conforming implementations shall support the substation configuration language as defined in IEC 61850-6 for exchange between engineering tools.

**Extracted Text:**

61850-9-2 © IEC:2011 —25— Table 16 – PICS for A-Profile support

| Client | Server | Value/comment |
|--------|--------|---------------|
| F/S    | F/S    |               |
| A1     | Client/Server A-Profile | c1            | c1            | Refer to 5.2 |
| A2     | SV A-Profile | c2            | c2            | Refer to 5.3 |

c1 – Shall be ‘m’ if support for any service specified in Table 1 is declared within the ACSI basic conformance statement.
c2 – Shall be ‘m’ if support for any service specified in Table 4 is declared within the ACSI basic conformance statement.

Table 17 – PICS for T-Profile support

| Client | Server | Value/comment |
|--------|--------|---------------|
| F/S    | F/S    |               |
| T1     | TCP/IP T-Profile | c1            | c1            | Refer to 5.2 |
| T2     | SV T-Profile | c2            | c2            | Refer to 5.3 |

c1 – Shall be ‘m’ if support for A1 is declared. Otherwise, shall be ‘i’
c2 – Shall be ‘m’ if support for A2 is declared. Otherwise, shall be ‘i’.

9.2.2 SV Services

This subclause describes the protocol implementation conformance statement for sampled values services based on the IEC 61850-7-2 basic conformance statement. See Table 18.

Table 18 – SV conformance statement

| Services | Client/ subscriber | Server/ publisher | Value/comment |
|----------|--------------------|-------------------|---------------|
| Multicast| c1                 | c1                |               |
| SendMSVMessage| c1                 | c1                |               |
| GetMSVCBValues| c2                 | c2                |               |
| SetMSVCBValues| c3                 | c3                |               |
| Unicast  | c1                 | c1                |               |
| SendUSVMessage| c1                 | c1                |               |
| SendUSVCBValues| c1                 | c1

---

## Page 28

![Image 1 from page 28](images/iec61850-9-2{ed2.0}b_page28_img21_7858f7ed.png)

**Image Type:** Table
**Description:** This image is a technical table from a standard document, specifically from IEC:2011, detailing the definitions for SV SCL (Substation Description Language) addressing elements. The table outlines the permissible string types for the SV (Substation Value) addressing as type parameters of the P element within the Address element. It specifies the permissible values and character restrictions for different types of addresses and identifiers.

**Key Elements:**
- **P-type designation:** Lists the types of addresses and identifiers.
- **Description:** Provides a brief description of each type.
- **m/o:** Indicates whether the type is mandatory (m) or optional (o).
- **Restrictions/comments:** Specifies the character restrictions and additional comments for each type.

**Extracted Text:**
```
—26- 61850-9-2 © IEC:2011
11 SCSM specific address element definitions
This clause defines the xs:string types that are allowed for the SV addressing as type parameters of the P element of the Address element. The values and character restrictions are defined in Table 19.
Table 19 — Definitions for SV SCL
P-type designation Description Restrictions/comments
MAC-Address Media Access Address m Shall be 6 groups of 2 visible characters separated by hyphens (-). Characters shall be limited to 0 to 9 and A to F.
APPID Application Identifier o Shall be 4 characters. Characters shall be limited to 0 to 9 and A to F.
VLAN-PRIORITY VLAN User Priority c1 Shall be a single character. Characters shall be limited to 0 to 7.
VLAN-ID VLAN ID o Shall be 3 characters. Characters shall be limited to 0 to 9 and A to F.
c1 — Shall only be present if VLAN is also present.
```

This table is crucial for understanding the permissible formats for various types of addresses and identifiers used in SV SCL, which is essential for the configuration and interoperability of substation automation systems.

---

## Page 29

![Image 1 from page 29](images/iec61850-9-2{ed2.0}b_page29_img22_5b26b507.png)

**Image Type:** Diagram

**Description:** The image is a detailed diagram illustrating the frame format for ISO/IEC 8802-3, which is part of the IEEE 802.3 standard for Ethernet. The diagram is structured to show the various fields and their positions within the frame, along with their respective octet positions and descriptions. It also references ASN.1 encoding rules and includes a note about the absence of link redundancy.

**Key Elements:**
- **Octet Positions:** The diagram is divided into octets (8-bit segments) from 1 to 26.
- **Field Descriptions:** Each octet is labeled with its corresponding field, such as Preamble, Start of frame, Destination address, Source address, Priority, TPID, TCI, Length Start, APPID, Ethertype, PDU, Length, Reserved fields, and Frame check sequence.
- **Notes:** References to other sections (e.g., *******, "Priority Tagging/VirtualLAN" section, "Ethertype and Other Header Information" section) are provided for additional details.
- **Figure References:** The diagram references Figures A.1, A.2, and A.3 for further details.

**Extracted Text:**
```
61850-9-2 © IEC:2011 -27- Annex A (informative) ISO/IEC 8802-3 frame format and ASN.1 basic encoding rules A.1 ISO/IEC 8802-3 frame format See Figures A.1, A.2 and A.3. Octets 8 7 6 5 4 3 2 1 Notes 2 3 Destination address 8 Header Refer to *******. MAG 9 Source address 12 13 Priority TPID (see Figure 2) Refer to "Priority 14 tagged Tagging/VirtualLAN" 16 TCI (see Figure 2) section. 8 Length Start APPID Ethertype PDU 2 Length (m + 8) ine Refer to “Ethertype 2 Reserved 1 (see and Other Header oa gu Information” section. APDU (of length m) m +26 “1517 (Pad bytes if necessary) Frame check sequence $1521 lec 1790/11 Figure A.1 — ISO/IEC 8802-3 frame format — No link redundancy
```

This diagram is useful for understanding the structure and encoding of Ethernet frames as defined by the ISO/IEC 8802-3 standard, and it provides a reference for implementing or interpreting Ethernet frames in accordance with the standard.

---

## Page 30

![Image 1 from page 30](images/iec61850-9-2{ed2.0}b_page30_img23_15c2038e.png)

**Image Type:** Diagram/Table

**Description:** The image is a detailed diagram illustrating the structure of an ISO/IEC 8802-3 frame format, specifically for Link Redundancy using HSR (High-Speed Ring). It is a technical schematic that breaks down the frame structure into octets and their corresponding fields, along with notes and references to other sections of the standard.

**Key Elements:**

- **Octets:** The diagram is divided into 32 octets, numbered from 1 to 32.
- **Fields:** Each octet is further divided into smaller fields, such as Preamble, Start of Frame, Destination Address, Source Address, Header, Priority Tagged, Link Redundancy, HSR Tag, Path Size H, Sequence Number H, Sequence Number L, Ethertype PDU, Length Start, APPID, and APDU.
- **Notes:** Notes are provided for each field, directing the reader to other sections of the standard for further details.
- **References:** The diagram includes references to other figures and sections, such as "Priority Tagging/VirtualLAN" and "Ethertype and Other Header Information."

**Extracted Text:**

```
-28- 61850-9-2 © IEC:2011
Octets
8 | 7 | 6 | 5 | 4 | 3 | 2 | 1
Notes
Preamble
Start of frame
Destination address
Header
MAC
Source address
TPID (see Figure 2)
Refer to "Priority Tagging/VirtualLAN" section.
TCI (see Figure 2)
Refer to *******.
Link tagged
HSR Tag
See IEC 62439-3 (HSR optional field).
Redundancy
0x892F
header
Path – Size H
Sequence number H
Sequence Number L
Length Start
APPID
Ethertype PDU
Length (m + 8)
Refer to "Ethertype and Other Header Information" section.
Reserved 1 (see Figure 3)
APDU (of length m)
m + 32
(Pad bytes if necessary)
Frame check sequence
≤1527
Figure A.2 — ISO/IEC 8802-3 frame format — Link redundancy: HSR
IEC 1791/11
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2012-000331 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11
```

This diagram is a crucial reference for understanding the structure and components of an ISO/IEC 8802-3 frame used in high-speed ring networks, particularly for link redundancy and priority tagging.

---

## Page 31

![Image 1 from page 31](images/iec61850-9-2{ed2.0}b_page31_img24_59376b9e.png)

**Image Type:** Diagram

**Description:** The image is a detailed diagram illustrating the structure of an ISO/IEC 8802-3 frame format, specifically focusing on the Link Redundancy Protocol (PRP) section. The diagram is divided into octets, with each octet labeled and annotated with its corresponding field or component. The diagram is structured to show the sequence and length of each field within the frame, including the preamble, start of frame, header, and various subfields such as MAC addresses, priority, TPID, TCI, length, APPID, APDU, and sequence numbers.

**Key Elements:**
- **Preamble:** The initial part of the frame.
- **Start of frame:** Marks the beginning of the frame.
- **Header:** Contains various fields such as MAC addresses, priority, TPID, TCI, length, APPID, APDU, and sequence numbers.
- **Destination address:** The address of the intended recipient.
- **Source address:** The address of the sender.
- **Priority:** Indicates the priority of the frame.
- **TPID (Tag Protocol Identifier):** Identifies the type of tag used in the frame.
- **TCI (Tag Control Information):** Contains control information for the tag.
- **Length Start:** Marks the start of the length field.
- **Length (m + 8):** Indicates the total length of the frame, including the header and data.
- **Ethertype PDU (Protocol Data Unit):** Identifies the type of data being transmitted.
- **Reserved fields:** Fields that are reserved for future use.
- **Sequence number H and L:** Used for sequence control in the PRP protocol.
- **Path – Size H and L:** Indicates the size of the path field.
- **Frame check sequence:** Ensures the integrity of the frame.

**Extracted Text:**
```
61850-9-2 © IEC:2011 —29—
Octets
8 7 6 5 4 3 2 1
Notes
Preamble
Start of frame
Destination address
Header
Refer to *******.
MAC
Source address
3 TPID (see Figure 2) Refer to 'Priority
99 TCI (see Figure 2) Tagging/VirtualLAN'
8 Length Start APPID
15 99 TCI (see Figure 2) section.
9 Length (m + 8) Refer to 'Ethertype
18 Ethertype PDU and Other Header
19 Length Start APPID Information' section.
20 Ethertype PDU
21 Length (m + 8) Refer to 'Ethertype
22 Length Start APPID and Other Header
23 Length (m + 8) Information' section.
24 APDU (of length m)
m + 26 (Pad bytes if necessary)
Link See IEC 62439-3
1523 redundancy Sequence Number L (PRP optional field)
trailer Path — Size H
1527 Frame check sequence
IEC 1792/11
Figure A.3 — ISO/IEC 8802-3 frame format — Link redundancy: PRP
```

This detailed diagram and text provide a comprehensive understanding of the ISO/IEC 8802-3 frame format, particularly the PRP section, which is crucial for understanding the structure and functionality of frames in a redundant network environment.

---

## Page 32

![Image 1 from page 32](images/iec61850-9-2{ed2.0}b_page32_img25_8900b62e.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that explains the ASN.1 basic encoding rules (BER) for encoding and decoding of sampled values. It outlines the structure of the BER transfer syntax, which is based on the triplet TLV (Type, Length, Value) or (Tag, Length, Value) format. The diagram also provides an overview of the encoding principles and the format of the tag octets.

**Key Elements:**
- **ASN.1 Basic Encoding Rules (BER):** The diagram explains how the BER transfer syntax is used for encoding and decoding of sampled values.
- **Triplet TLV Format:** The diagram shows the structure of a triplet TLV, which consists of a Tag, Length, and Value.
- **Tag Octets Format:** The diagram provides a detailed format for the tag octets, which are used to encode the tag of the value type. It includes the bit positions and their corresponding values for the class and format fields.
- **Bit Positions and Values:** The diagram specifies the bit positions and their corresponding values for the class and format fields in the tag octets.

**Extracted Text:**
```
-30- 61850-9-2 © IEC:2011
A.2 ASN.1 basic encoding rules (BER)
ASN.1 basic encoding rules (as specified in ISO/IEC 8825-1) will be used for encoding and decoding of sampled values. The main encoding principles are shown as an overview.
The BER transfer syntax has the format of a triplet TLV (Type, Length, Value) or (Tag, Length, Value) as shown in Figure A.4.
All fields (T, L, V) are series of octets. The value V can be a triplet TLV itself, if it is constructed.
The transfer syntax is octet-based and "big endian"-oriented. The length field L defines the length of each TLV triplet.
Figure A.4 - Basic encoding rules format
The tag octets correspond to the encoding of the tag of the value type. Figure A.5 shows the two formats of the tag octets T.
Tag octet
Bit7 Bit6 Bit5 Bit0
class P/C
Bit7 Bit6 Class
Bit5 Format
0 0 UNIVERSAL
0 0 Primitive
0 1 APPLICATION
0 1 Constructed
1 0 context-specific
1 1 PRIVATE
Figure A.5 - Format of the tag octets
```

This technical analysis provides a clear understanding of the content and structure of the image, which is useful for technical documentation and search.

---

## Page 34

![Image 1 from page 34](images/iec61850-9-2{ed2.0}b_page34_img26_4d0bae53.png)

**Image Type:** Technical Document Page
**Description:** This image is a page from a technical document, specifically from Annex B, which provides guidelines for the selection and use of multicast addresses in the context of IEC 61850-8-1 and IEC 61850-9-2 standards. The document discusses the importance of using media access controller (MAC) hardware for filtering multicast messages and recommends evaluating the impact of hash algorithms on multicast address assignment.

**Key Elements:**
- **Annex B (informative):** Provides recommendations for multicast address selection.
- **Multicast address selection:** Focuses on increasing the performance of multicast message reception.
- **MAC hardware filtering:** Recommends using MAC hardware for filtering.
- **Hash algorithms:** Discusses the variability of hash algorithms in integrated circuits.
- **IEC 61850-8-1 and IEC 61850-9-2 standards:** References for the types of messages that should be addressed.
- **Multicast address structure:** Describes the structure of multicast addresses (octet string of size 6).
- **IEEE assignment:** The first three octets are assigned by IEEE with "01-0C-CD".
- **Service-specific octets:** The fourth octet is assigned based on the service type (01 for GOOSE, 02 for GSSE, 04 for multicast sampled values).
- **Individual address range:** The last two octets are used as individual addresses assigned by a range defined in Table B.1.

**Extracted Text:**
```
-32- 61850-9-2 © IEC:2011
Annex B (informative)
Multicast address selection
In order to increase the overall performance of multicast message reception (for example GOOSE, GSSE, and Sampled Values), it is preferable to have the media access controller (MAC) hardware perform the filtering. The hash algorithms in the various integrated circuits do vary. It is recommended, as a system integrator, to evaluate the impact of these algorithms when assigning destination multicast addresses.
Vendors of IEC 61850-8-1 or IEC 61850-9-2 implementations that send these types of messages should provide recommendations of addressing based upon the MAC IC’s hash algorithms. One such recommendation might appear as follows:
The multicast addresses (octet string of size 6) used within this standard will have the following structure.
- The first three octets are assigned by IEEE with 01-0C-CD.
- The fourth octet will be 01 for GOOSE, 02 for GSSE, and 04 for multicast sampled values.
- The last two octets will be used as individual addresses assigned by range defined in Table B.1.
Table B.1 - Recommended multicast addressing example
Service Starting address Ending address (hexadecimal) (hexadecimal)
GOOSE 01-0C-CD-01-00-00 01-0C-CD-01-01-FF
GSSE 01-0C-CD-02-00-00 01-0C-CD-02-01-FF
Multicast sampled values 01-0C-CD-04-00-00 01-0C-CD-04-01-FF
```

This document is useful for technical professionals working with IEC 61850 standards, particularly those involved in the design and implementation of systems that utilize multicast addresses for message reception and transmission.

---

## Page 35

![Image 1 from page 35](images/iec61850-9-2{ed2.0}b_page35_img27_d1048e49.png)

**Image Type:** Blank Page

**Description:** The image appears to be a blank page, possibly from a technical document or a presentation slide. There are no visible diagrams, tables, charts, schematics, or any other technical elements present. The page is entirely white with no text or graphical content.

**Key Elements:** None

**Extracted Text:** None detected.

---

## Page 39

![Image 1 from page 39](images/iec61850-9-2{ed2.0}b_page39_img28_956fab52.png)

**Image Type:** Document Page

**Description:** This image is a page from a technical document, specifically from the CEI (Comité Electrotechnique International) series, numbered 61850-9-2, published in 2011. The document appears to be a revision or update to a previous edition published in 2004, detailing the changes and modifications made to the standard. The content is structured to provide information about the voting process that led to the approval of the norm, the revisions made, and the directives followed in the document's creation.

**Key Elements:**
- **Document Title and Edition:** CEI 61850-9-2, published in 2011.
- **Voting Approval:** The document outlines the voting process that led to the approval of the norm.
- **Revisions and Changes:** Highlights the main modifications compared to the previous edition, including the addition of an optional link redundancy layer, the redefinition of "reserved" fields in the link layer, the evolution of components USVCB and MSVCB, and the evolution of the encoding related to the transmission of sampled memory buffer values.
- **Directives and Standards:** The document adheres to ISO/CEI Part 2 directives.
- **Publication Stability:** The committee has decided that the content of this publication will not be modified before a specified date of stability, which can be found on the CEI website.

**Extracted Text:**
61850-9-2 © CEI:2011 -37- Le rapport de vote indiqué dans le tableau ci-dessus donne toute information sur le vote ayant abouti à l'approbation de cette norme. Cette deuxième édition annule et remplace la première édition publiée en 2004 et constitue une révision technique. Les modifications principales par rapport à la première édition sont les suivantes: 
- l'adjonction d'une couche optionnelle de redondance de liaison (Tableaux 3 à 6);
- la redéfinition des champs "reserved" dans la couche de liaison (*******);
- l'évolution des composants USVCB et MSVCB (Tableaux 9, 10, 12);
- l'évolution de l'encodage relatif à la transmission de la mémoire tampon des valeurs échantillonnées (Tableau 14).
Cette publication a été rédigée selon les Directives ISO/CEI, Partie 2.
Une liste de toutes les parties de la série CEI 61850, sous le titre général: Réseaux et systèmes de communication pour l'automatisation des systèmes électriques, peut être consultée sur le site web de la CEI.
Le comité a décidé que le contenu de cette publication ne sera pas modifié avant la date de stabilité indiquée sur le site web de la CEI sous "http://webstore.iec.ch" dans les données relatives à la publication recherchée. A cette date, la publication sera
- reconduite,
- supprimée,
- remplacée par une édition révisée, ou
- amendée.

---

## Page 44

![Image 1 from page 44](images/iec61850-9-2{ed2.0}b_page44_img29_a4408d32.png)

**Image Type:** Table
**Description:** The image is a table listing various abbreviations and their corresponding full forms, commonly used in the context of communication protocols and standards. The table is structured with two columns: the first column contains the abbreviations, and the second column provides their definitions. The table is part of a larger document, as indicated by the page number and copyright information at the top.

**Key Elements:**
- Abbreviations (e.g., ACSI, ASDU, ASN.1, etc.)
- Full forms of abbreviations (e.g., Abstract communication service interface, Application service data unit, Abstract syntax notation number one, etc.)
- Technical terms and concepts related to communication protocols and standards

**Extracted Text:**
```
-42- 61850-9-2 © CEI:2011
4 Abréviations
ACSI Abstract communication service interface
ASDU Application service data unit
ASN.1 Abstract syntax notation number one
APCI Application protocol control information
APDU Application protocol data unit
APPID Application identifier
AUI Attachment unit interface
BER ASN.1 basic encoding rules
BS Bitstring
c Conditional support. L’élément doit être mis en application si la condition établie existe
CFI Canonical format identifier
CSMA/CD Carrier sense multiple access/collision detection
DF Data frame
DO Data object
ECT Electronic current transformer
EVT Electronic voltage transformer
F/S Functional standard
GOOSE Generic object oriented substation event
GSSE Generic substation status event
i Out-of-scope: La mise en application de l’élément ne fait pas partie du domaine d’application de la présente norme
ICD IED configuration description
IED Intelligent electronic device
LSDU Link layer service data unit
m Mandatory support. L’élément doit être mis en application
MAC Media access control
MAU Medium attachment unit
MMS Manufacturing message specification (ISO 9506)
MSVCB Multicast sampled value control block
MU Merging unit
o Optional support. L’exécuteur peut décider de mettre en application l’élément
PDU Protocol data unit
PICS Protocol implementation conformance statement
SCSM Specific communication services mapping
r readable
SV Sampled value
TCI Tag control information
TPID Tag protocol identifier
```

This table is useful for technical documentation and search as it provides a quick reference for abbreviations commonly used in communication protocols and standards.

---

## Page 45

![Image 1 from page 45](images/iec61850-9-2{ed2.0}b_page45_img30_1936da82.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page that includes a table of abbreviations and definitions, followed by a detailed explanation of the OSI (Open Systems Interconnection) model and its profiles. It also includes a figure illustrating the OSI model and its profiles.

**Key Elements:**

1. **Table of Abbreviations and Definitions:**
   - USVCB: Unicast sampled value control block
   - VID: VLAN identifier
   - VLAN: Virtual local area network
   - VMD: Virtual manufacturing device
   - w: Writeable
   - x: Excluded: L'exécuteur ne doit pas mettre en application cet élément
   - XML: Extensible markup language

2. **Explanation of OSI Model:**
   - The OSI model is described as a reference model for interconnection of open systems, defined by ISO/CEI 7498-1.
   - It consists of 7 layers and specifies functional requirements for each layer to achieve a robust communication system.
   - The model does not specify the protocols to use for functionality and does not restrict the solution to a unique set of protocols.

3. **Figure 1: OSI Model and Profiles:**
   - The figure illustrates the OSI model with its 7 layers: Physical, DataLink, Network, Transport, Session, Presentation, and Application.
   - It also shows the A-Profile and T-Profile, which are subsets of the OSI model. The A-Profile includes the top three layers (Application, Presentation, and Session), while the T-Profile includes the bottom four layers (Transport, Network, DataLink, and Physical).

4. **Profiles and Their Usage:**
   - The A-Profile and T-Profile are defined to enable the transmission of sampled values, including access to the SV control block.
   - Two combinations of A-Profiles and T-Profiles are defined to support:
     - Client/server services based on MMS (Industrial Messaging Specification), conforming to CEI 61850-8-1.
     - SV services based on the DataLink layer (data link).

**Extracted Text:**

```plaintext
61850-9-2 © CEI:2011 -43-
USVCB Unicast sampled value control block
VID VLAN identifier
VLAN Virtual local area network
VMD Virtual manufacturing device
w Writeable
x Excluded: L'exécuteur ne doit pas mettre en application cet élément
XML Extensible markup language

5 Pile de communication
5.1. Vue d'ensemble de l'utilisation du protocole
Le modèle de référence d'interconnexion de systèmes ouverts (OSI) (ISO/CEI 7498-1) définit un modèle basé sur le concept de couches de fonctions de communication. Le modèle inclut 7 couches et spécifie les exigences fonctionnelles pour chaque couche, afin de réaliser un système de communication robuste. Le modèle ne spécifie pas les protocoles à utiliser pour réaliser la fonctionnalité; il ne restreint pas non plus la solution à un ensemble unique de protocoles.

Application
Presentation
Session
Transport
Network
DataLink
Physical

Figure 1 – Modèle de référence OSI et profils

Les profils ISO application ("A-Profile") et transport ("T-Profile") (voir Figure 1) décrivent les divers profils de pile. Un "A-Profile" ISO est l'ensemble des spécifications et des accords concernant les trois (3) couches supérieures du modèle de référence OSI ISO (par exemple, les couches "application", "presentation" et "session"). Un "T-Profile" ISO est l'ensemble des spécifications et des accords concernant les quatre (4) couches inférieures du modèle de référence OSI ISO (par exemple, les couches "transport", "network", "datalink" et "physical").

Deux combinaisons des "A-Profiles" et des "T-Profiles" sont définies afin de permettre la transmission des valeurs échantillonnées comprenant l'accès au bloc de contrôle des valeurs échantillonnées (SV control block) associé, comme cela est spécifié par la CEI 61850-7-2. Les deux combinaisons distinctes sont utilisées pour:
- les services client/serveur basés sur la MMS (spécification de messagerie industrielle), conformément à la CEI 61850-8-1;
- les services SV basés sur la couche "datalink" (liaison de données).
```

This technical document provides a comprehensive

---

## Page 46

![Image 1 from page 46](images/iec61850-9-2{ed2.0}b_page46_img31_2c3953c8.png)

**Image Type:** Table and Text

**Description:** The image is a technical document page from a standard or specification document related to communication profiles and services for a specific model (CEI 61850-7-2). It includes a table detailing services and protocols required for a client/server communication profile, and another table listing services and protocols for an "A-Profile." The document is structured to provide technical details for compliance with the CEI 61850 standard.

**Key Elements:**
- **Table 1:** Lists services and protocols required for a client/server communication profile.
- **Table 2:** Lists services and protocols for the "A-Profile."
- **Textual Information:** Describes the use of a client/server communication profile in conjunction with the SV profile, and the services and protocols required for compliance with the CEI 61850 standard.

**Extracted Text:**

```
-44- 61850-9-2 © CEI:2011
5.2 Services client/serveur et profils de communication
5.2.1 Services client/serveur
Ce profil de communication client/serveur doit être utilisé, en plus du profil de communication SV, conformément à 5.3, si un accès au bloc de contrôle des valeurs échantillonnées, par l'intermédiaire du client, est exigé. Ce profil doit être utilisé pour toute implémentation prétendant être en conformité avec la présente norme et déclarant supporter l'un des services de la CEI 61850-7-2 suivants, donnés par le Tableau 1.

Tableau 1 – Service exigeant un profil de communication client/serveur
Modèle CEI 61850-7-2 Service CEI 61850-7-2
Logical device GetLogicalDeviceDirectory
Logical node GetLogicalNodeDirectory
SV class model GetMSVCBValues

5.2.2 "A-Profile"
Le Tableau 2 présente les services et les protocoles client/serveur du "A-Profile".

Tableau 2 – Services et protocoles relatifs au profil de communication client/serveur du "A-Profile"
Spécification
Couche du modèle OSI Nom Spécification du service Spécification du protocole du service m/o²
Application Manufacturing ISO 9506-1:2003 ISO 9506-2:2003 m
message specification
2 Anglais: m/o (mandatory/optional) français: obligatoire/optionnel
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2012-000331 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel: +41 22 919 02 11
```

This document is part of a larger technical specification, likely used in the context of industrial automation or smart grid communication standards.

---

## Page 48

![Image 1 from page 48](images/iec61850-9-2{ed2.0}b_page48_img32_4d080df3.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically related to communication services and protocols. It outlines the services and protocols associated with the "A-Profile" and "T-Profile" for sampled value (SV) services. The table is structured to show the specifications for each layer of the OSI model, from the Application layer down to the Physical layer, for both the "A-Profile" and "T-Profile" services.

**Key Elements:**

- **Tableau 4:** Services requiring a communication profile SV.
- **Tableau 5:** Services and protocols related to the "A-Profile" communication profile SV.
- **Tableau 6:** "T-Profile" for SV services.
- **OSI Model Layers:** Application, Presentation, Session, Transport, Network, DataLink, Link, and Physical.
- **Specifications:** Detailed specifications for each service and protocol, including standards and amendments.
- **Example Specifications:** 
  - Application: SV service.
  - Presentation: Abstract syntax.
  - DataLink: Priority tagging/VLAN.
  - Physical: Fibre optic transmission system.

**Extracted Text:**

```plaintext
46 - 61850-9-2 © CEI:2011
Tableau 4 – Services exigeant un profil de communication SV
Modèle Service CEI 61850-7-2
Multicast sampled value class model Multicast SV message
Unicast sampled value class model Unicast SV message

5.3.2 "A-Profile"
Le Tableau 5 présente les services et les protocoles SV du "A-Profile".

Tableau 5 – Services et protocoles relatifs au profil de communication SV du "A-Profile"
Couche du Spécification mio modéle OSI Spécification du Spécification du service protocole
Couche "presentation": voir les définitions supplémentaires en 8.5.
Couche "application": voir les définitions supplémentaires en 8.5.

5.3.3 "T-Profile"
Le "T-Profile" pour les services SV est donné au Tableau 6.

Tableau 6 – "T-Profile" SV
Spécification
Couche du I modéle OSI Spécification du Spécification du mio service protocole
Link Parallel redundancy protocol and I CEI 62439-3, Amendement 1 Redundancy I high availability seamless ring
DataLink Priority tagging/VLAN IEEE 802.1Q
Carrier sense multiple access ISO/IEC 8802-3:2000 with collision detection (CSMA/CD)
Physical Fibre optic transmission system ISO/IEC 8802-3:2000
Basic optical fibre connector CEI 60874-10-1, CEI 60874-10-2 et CEI 60874-10-3
NOTE Il s'agit de la spécification pour le connecteur ST.
c1 – Recommandé, mais une technologie future pourrait être utilisée.
```

This table is part of a larger document that provides detailed technical specifications for communication profiles and services, likely used in the context of industrial automation or similar technical domains.

---

## Page 57

![Image 1 from page 57](images/iec61850-9-2{ed2.0}b_page57_img33_cd183763.png)

**Image Type:** Diagram/Table

**Description:** The image is a technical document page that includes a diagram illustrating the structure of an Application Protocol Data Unit (APDU) and a table detailing the encoding of a buffer format according to the CEI 61850-9-2 standard. The diagram shows the hierarchical structure of an APDU, which includes an Application Protocol Control Information (APCI) field and multiple Application Service Data Units (ASDUs). The table provides a detailed breakdown of the attributes and their types, along with the corresponding encoding rules and comments.

**Key Elements:**
- **Diagram:** 
  - APDU structure: Includes APCI and ASDUs.
  - ASDUs: Further divided into ASDU 1, ASDU 2, ..., ASDUn.
  - Tag, Length, Number of ASDUs (UI16), ASDU fields.
- **Table:**
  - Format of abstract buffer format according to CEI 61850-7-2.
  - Attributes: NoASDU, security, asdu.
  - Types: INTEGER, SEQUENCE, SEQUENCE OF.
  - Règles de codage de base ASN.1 (BER).
  - Comments: Specific attributes and their meanings.

**Extracted Text:**

```plaintext
61850-9-2 © CEI:2011 -55- réduire la complexité de l'implémentation. Lorsque plusieurs ASDU sont concaténés dans une méme trame, l'ASDU ayant les échantillons les plus anciens est le premier de la trame. De plus amples détails sont donnés à la Figure 4.

APCI (Application – Protocol Control Information)
ASDU's (Application – Service Data Unit)
No. of ASDUs (UI16)
ASDU 1
ASDU 2
ASDU n

APDU (Application – Protocol Data Unit)
IEC 1789/11

Figure 4 – Concaténation de plusieurs ASDU en une trame

La grammaire ASN.1, en relation avec les règles de codage de base (basic encoding rules - BER), est utilisée pour encoder les messages des valeurs échantillonnées en vue de la transmission sur l'ISO/CEI 8802-3.

8.5.2 Fonctionnalité de la couche présentation

Pour la transmission, la mémoire tampon des valeurs échantillonnées est encodée comme spécifié par le Tableau 14.

Tableau 14 – Encodage relatif à la transmission de la mémoire tampon des valeurs échantillonnées

IEC61850 DEFINITIONS::= BEGIN
IMPORTS Data FROM ISO-IEC-9506-2CEI 61850-9-2 Specific Protocol::= CHOICE {
savPdu [APPLICATION 0] IMPLICIT SavPdu,

Format de mémoire tampon abstrait (abstract buffer format) selon la CEI 61850-7-2
Nom d'attribut | Type d'attribut | Règles de codage de base ASN.1 (BER)
NoASDU [0] IMPLICIT INTEGER (1.65535), Attribut spécifique de mise en correspondance. Nombre d'ASDU qui seront concaténées dans une APDU.
security [1] ANY OPTIONAL, Attribut spécifique de mise en correspondance. Réservé pour future définition (par exemple, signature numérique).
asdu [2] IMPLICIT SEQUENCE OF [Nombre 1 à n d'ASDU comme spécifié précédemment]
ASDU::= SEQUENCE {
}
```

This technical document page provides a detailed explanation of the structure and encoding of APDUs and ASDUs in the context of the IEC 61850 standard, which is commonly used in smart grid communication protocols.

---

## Page 58

![Image 1 from page 58](images/iec61850-9-2{ed2.0}b_page58_img34_7c62449d.png)

**Image Type:** Table

**Description:** The image is a structured table that outlines the format of an abstract buffer format (ABF) as defined by the CEI 61850-7-2 standard. The table is divided into three columns: "Format de mémoire tampon abstrait (abstract buffer format) selon la CEI 61850-7-2," "Codage en CEI 61850-9-2," and "Commentaires." It provides detailed information about the attributes, their types, and the rules for encoding and decoding according to the CEI 61850-9-2 standard. The table also includes comments that provide additional context and explanations for each attribute.

**Key Elements:**
- **Attributes:** Names of the attributes such as SavPdu, MsvID or UsvID, DatSet, SmpCnt, ConfRev, RefrTm, SmpSynch, and SmpRate.
- **Types:** The data types of the attributes, such as INT16U, INT32U, OCTET STRING, and TimeStamp.
- **Rules of Coding:** The rules for encoding and decoding the attributes according to the CEI 61850-9-2 standard.
- **Comments:** Additional explanations and notes about the attributes and their usage.

**Extracted Text:**
```
— 56 – 61850-9-2 © CEI:2011
Format de mémoire tampon abstrait (abstract buffer format) selon la CEI 61850-7-2
Nom d'attribut | Type d'attribut | Régles de codage de base ASN.1 (BER)
SavPdu::= | SEQUENCE { | 
MsvID or UsvID | VISIBLE STRING | svID [0] IMPLICIT VisibleString,
| | Il convient que cela soit une identification unique à l'échelle du système.
DatSet | ObjectReference | datset [1] IMPLICIT VisibleString,
| | Valeur issue du MSVCB ou du USVCB.
SmpCnt | INT16U | smpCnt [2] IMPLICIT OCTET STRING (SIZE(2)),
| | Sera incrémenté chaque fois qu'une nouvelle valeur d'échantillonnage sera prélevée. Le compteur doit être mis à zéro si l'échantillonnage est synchronisé par le signal d'horloge et le signal de synchronisation se produit.
| | Lorsque des impulsions de synchro sont utilisées pour synchroniser des unités de fusion, le compteur doit être mis à zéro avec chaque impulsion de synchro. La valeur 0 doit être donnée à l'ensemble de données dans le cas où l'échantillonnage du courant primaire coïncide avec l'impulsion de synchro. OCTET STRING est interprété comme INT16U, tel que défini par le Tableau 15.
ConfRev | INT32U | confRev [3] IMPLICIT OCTET STRING (SIZE(4)),
| | Valeur issue du MSVCB ou du USVCB. OCTET STRING est interprété comme INT32U, tel que défini par le Tableau 15.
RefrTm | TimeStamp | refrTm [4] IMPLICIT UtcTime
| | RefrTm contient le temps de rafraîchissement de la mémoire tampon SV.
SmpSynch | INT8U | smpSynch [5] IMPLICIT OCTET STRING (SIZE(1)),
| | 0 = les SV ne sont pas synchronisées par un signal d'horloge externe.
| | 1 = les SV sont synchronisées par un signal d'horloge provenant d'une horloge d'une zone locale non spécifiée.
| | 2 = les SV sont synchronisées par un signal d'horloge de zone globale (traçabilité temporelle).
| | 5 & 254 = les SV sont synchronisées par un signal d'horloge provenant d'une horloge d'une zone locale identifiée par cette valeur.
| | 3; 4; 255 = valeurs réservées – Ne pas utiliser.
SmpRate | INT16U | smpRate [6] IMPLICIT OCTET STRING (SIZE(2)) OPTIONAL,
| | Valeur issue du MSVCB ou du USVCB. OCTET STRING est interprété comme INT16U, tel que défini par le Tableau

---

## Page 60

![Image 1 from page 60](images/iec61850-9-2{ed2.0}b_page60_img35_47e58b81.png)

**Image Type:** Table

**Description:** The image is a technical table from a standard or specification document, specifically related to data types and their encoding within the CEI 61850-7-2 and CEI 61850-8-1 standards. It outlines various data types and their corresponding encodings, along with comments for each type. Additionally, it includes tables for PICS (Profile Information and Conformance Statement) conformance profiles, detailing the client-server relationships and service declarations for the "A-Profile" and "T-Profile."

**Key Elements:**

- **Data Types and Encodings:**
  - OCTET STRING: 20 Bytes ASCII Text, Null terminated.
  - VISIBLE STRING: 35 Bytes ASCII Text, Null terminated.
  - UNICODE STRING: 20 Bytes ASCII Text, Null terminated.
  - ObjectName, ObjectReference: 20 Bytes ASCII Text, Null terminated.
  - TimeStamp: 64 Bit Timestamp as defined in CEI 61850-1.
  - EntryTime: 48 Bit Timestamp as defined in CEI 61850-1.
  - BITSTRING: 32 Bit Big Endian.

- **PICS Conformance Profiles:**
  - **Table 16: A-Profile**
    - Client/Server A-Profile: c1 (must be "m" if services specified in Table 1 are declared in the base conformance statement).
    - SV A-Profile: c2 (must be "m" if services specified in Table 4 are declared in the base conformance statement).
  - **Table 17: T-Profile**
    - TCP/IP T-Profile: c1 (must be "m" if A1 is declared; otherwise, "i").
    - SV T-Profile: c2 (must be "m" if A2 is declared; otherwise, "i").

**Extracted Text:**

```plaintext
—58— 61850-9-2 © CEI:2011
Types de données selon la CEI 61850-7-2
OCTET STRING 20 Bytes ASCII Text, Null terminated
VISIBLE STRING 35 Bytes ASCII Text, Null terminated
UNICODE STRING 20 Bytes ASCII Text, Null terminated
ObjectName 20 Bytes ASCII Text, Null terminated
ObjectReference 20 Bytes ASCII Text, Null terminated
Types de données selon la CEI 61850-8-1
BITSTRING 32 Bit Big Endian

9 Conformité
9.1 Notation
Le Paragraphe 9.2 à l’Article 11 font appel aux abréviations données à l’Article 4.
9.2 PICS
9.2.1 Conformité des profils
Le Tableau 16 et le Tableau 17 définissent la déclaration de conformité de base.

Tableau 16 — PICS prenant en charge le "A-Profile"
A1 Client/Server A-Profile c1
A2 SV A-Profile c2

c1 — Doit être "m" si les services pris en charge spécifiés par le Tableau 1 sont déclarés dans la déclaration de conformité de base ACSI.
c2 — Doit être "m" si les services pris en charge spécifiés par le Tableau 4 sont déclarés dans la déclaration de conformité de base ACSI.

Tableau 17 — PICS prenant en charge le "T-Profile"
T1 TCP/IP T-Profile c1
T2 SV T-Profile c2

c1 — Doit être "m" si la prise en charge pour A1 est déclarée. Sinon, doit être "i".
c2 — Doit être "m" si la prise en charge pour A2 est déclarée. Sinon, doit être "i".
```

This table and its associated text provide a structured overview of data types, their encodings, and the conformance requirements for specific profiles within the CEI 61850 standards.

---

## Page 63

![Image 1 from page 63](images/iec61850-9-2{ed2.0}b_page63_img36_435a8a34.png)

**Image Type:** Diagram

**Description:** The image is a detailed diagram illustrating the structure of an ISO/CEI 8802-3 frame, specifically focusing on the HSR (High-Speed Ring) redundancy link. The diagram is divided into octets, with each octet labeled and annotated with its corresponding field or component. The diagram is structured to show the sequence of fields within the frame, including headers, addresses, priorities, and sequence numbers.

**Key Elements:**
- **Octets:** The diagram is divided into 1527 octets, labeled from 1 to 1527.
- **Fields:** Each octet is associated with a specific field or component, such as the preamble, destination address, source address, priority, TPID, TCI, HSR label, HSR header, length, APPID, APDU, and sequence number.
- **Annotations:** The diagram includes annotations in French, providing descriptions and references to other figures or standards (e.g., CEI 62439-3, Figure 2, Figure 3).

**Extracted Text:**
```
61850-9-2 © CEI:2011 -61-
Octets
8 | 7 | 6 | 5 | 4 | 3 | 2 | 1
Notes
Début de la trame
Adresse de destination
Adresse source
TPID (voir Figure 2)
Priorité
TCI (voir Figure 2)
Entête de
Etiquette HSR
Voir la CEI 62439-3
(Champ facultatif HSR)
Ox892F
Chemin — Taille H HSR)
Taille L
Numéro de séquence H
Numéro de séquence L
Début
Longueur
APPID
Ethertype PDU
Longueur (m + 8)
Se référer à la
partie “Ethertype
and Other Header
Information”.
Reserved 1 (voir
Figure 3)
Reserved 2
APDU (de longueur m)
m + 32
(Octets de remplissage si
nécessaire)
Séquence de vérification
de la trame
IEC 1791/11
Figure A.2 - Format de la trame ISO/CEI 8802-3 — Redondance de liaison: HSR
```

This diagram is useful for technical documentation and search as it provides a clear and detailed representation of the frame structure, which is essential for understanding and implementing the ISO/CEI 8802-3 standard for high-speed ring redundancy.

---

## Page 64

![Image 1 from page 64](images/iec61850-9-2{ed2.0}b_page64_img37_91664cc5.png)

**Image Type:** Diagram

**Description:** The image is a detailed diagram illustrating the structure of an ISO/CEI 8802-3 frame, specifically focusing on the Redundancy of Link (PRP) mechanism. The diagram is divided into octets, with each octet labeled and annotated with its corresponding field or component. The diagram is structured to show the sequence of fields within the frame, starting from the preamble and ending with the frame check sequence.

**Key Elements:**
- **Preamble:** The initial part of the frame, which is not part of the data but is used for synchronization.
- **Destination Address:** The address of the destination device.
- **Source Address:** The address of the source device.
- **Type/Length Field (TPID):** Indicates the type of frame (e.g., Ethernet frame type).
- **Priority Field:** Specifies the priority of the frame.
- **Type/Length Field (TCI):** Indicates the type of frame and its length.
- **Ethertype Field:** Identifies the type of protocol data unit (PDU) being carried.
- **Length Field:** Indicates the length of the frame.
- **APPID Field:** Application Protocol Identifier.
- **Reserved Fields:** Fields reserved for future use.
- **Sequence Number Fields (H and L):** Used for sequence number and link redundancy.
- **Frame Check Sequence (FCS):** Used for error detection.

**Extracted Text:**
```
—62— 61850-9-2 © CEI:2011
Octets
8 7 6 5 4 3 2 1
Notes
Début de la trame
Adresse de destination
Se référer au
Entête
MAC
*******.
Adresse source
TPID (voir Figure 2)
Priorité
étiquetée
TCI (voir Figure 2)
Tagging/VirtualLAN
Début Longueur
APPID
Ethertype PDU
Longueur (m + 8)
Se référer à la
Reserved 1 (voir
partie “Ethertype
Figure 3) (voir
and Other Header
Figure 3)
Information”.
Reserved 2
APDU (de longueur m)
(m + 26)
(Octets de remplissage si
nécessaire)
Fin de
Numéro de séquence H
redondance
Numéro de séquence L
de liaison
Chemin — Taille H
Taille L
Séquence de vérification
de la trame
≤1527
IEC 1792/11
Figure A.3 – Format de la trame ISO/CEI 8802-3 — Redondance de liaison: PRP
```

This diagram is a crucial reference for understanding the structure and components of an ISO/CEI 8802-3 frame, particularly in the context of redundancy mechanisms like PRP.

---

## Page 65

![Image 1 from page 65](images/iec61850-9-2{ed2.0}b_page65_img38_6d3c22aa.png)

**Image Type:** Diagram

**Description:** The image is a technical diagram that explains the basic encoding rules for ASN.1 (Abstract Syntax Notation One) using Binary Encoding Rules (BER). It outlines the structure of the Transfer Syntax, which is based on the Tag-Length-Value (TLV) triplet format. The diagram includes a flowchart and a table to illustrate the encoding and decoding process of BER values.

**Key Elements:**
- **ASN.1 Encoding Rules:** The diagram describes the use of ASN.1 encoding rules for BER.
- **Transfer Syntax Format:** The diagram explains the format of the Transfer Syntax, which can be represented as a Tag-Length-Value triplet or a Tag-Length-Value triplet within a larger triplet.
- **Octet Representation:** The diagram shows how the Tag field is represented as an octet, with specific bit positions defining the class, primitive/composite, and format of the value.
- **Tag Octet Format:** The diagram provides a detailed breakdown of the Tag octet, showing the bit positions and their corresponding values for different types of tags (Universal, Application, Contextual, Private).

**Extracted Text:**

```
61850-9-2 © CEI:2011 -63- A.2. Règles de codage de base ASN.1 (BER)

Les règles de codage de base ASN.1 (telles que spécifiées par l'ISO/CEI 8825-1) seront utilisées pour coder et décoder des valeurs échantillonnées. Une présentation générale des principaux principes d'encodage est donnée ci-dessous.

La syntaxe de transfert des BER a le format d'un triplet TLV [Type (type), Length (longueur), Value (valeur)] ou [Tag (étiquette), Length (longueur), Value (valeur)], suivant les indications de la Figure A.4.

Tous les champs (T, L, V) sont des séries d'octets. La valeur V peut elle-même être un triplet TLV, si elle est composée.

La syntaxe de transfert est au niveau octet et orientée "gros-boutiste". Le champ "Length" (longueur) L définit la longueur de chaque triplet TLV.

Figure A.4 – Format des règles de codage de base

Les octets du champ "Tag" (étiquette) correspondent au codage de l'étiquette du type de valeur. La Figure A.5 montre les deux formats des octets "Tag" (étiquette).

Octet,,Tag“ (étiquette)
Bit7 Bit6 Bit5 Bit0
[ome [rc] id O i?) UNIVERSELLE O Primitif O 1 APPLICATIVE 1 Composé 1 i?) Contextuelle 1 1 PRIVEE

Figure A.5 – Format des octets "Tag" (étiquette)
```

This technical analysis provides a clear understanding of the image's content and its relevance to the encoding and decoding of BER values in ASN.1.

---

## Page 67

![Image 1 from page 67](images/iec61850-9-2{ed2.0}b_page67_img39_e4fad330.png)

**Image Type:** Technical Document Page

**Description:** The image is a page from a technical document, specifically an annex (Annexe B) that provides guidelines for the selection and addressing of multicast addresses in the context of CEI 61850-8-1 and CEI 61850-9-2 standards. The document discusses the importance of using media access controllers (MAC) to filter multicast messages and recommends evaluating the impact of hash algorithms used in integrated circuits for this purpose. It also outlines the structure of multicast addresses and provides an example of recommended addressing.

**Key Elements:**
- **Annexe B (informative):** Indicates the section of the document.
- **Sélection d'adresse multidiffusion (multicast):** Title of the section.
- **CEI 61850-8-1 and CEI 61850-9-2 standards:** References to the standards being discussed.
- **Media access controller (MAC):** Type of hardware recommended for filtering multicast messages.
- **Hash algorithms:** Algorithms used in integrated circuits for filtering.
- **Structure of multicast addresses:** Specifies the format of the addresses (6 octets).
- **Example of recommended addressing (Tableau B.1):** Provides a table with examples of multicast address ranges for different services (GOOSE, GSSE, and sampled values).

**Extracted Text:**

61850-9-2 © CEI:2011 -65- Annexe B (informative) Sélection d'adresse multidiffusion (multicast)

Dans le but d'augmenter les performances globales de réception des messages multidiffusion (par exemple "GOOSE", "GSSE", et "Sampled Values"), il est préférable d'avoir un matériel du type "media access controller (MAC)" (contréleur d'accès aux données) qui effectue le filtrage. Les algorithmes de hachage des divers circuits intégrés le font de manière variable. Il est recommandé, en tant qu'intégrateur système, d'évaluer l'impact de ces algorithmes lors de l'affectation des adresses multidiffusion de destination.

Il convient que les fournisseurs d'implémentations CEI 61850-8-1 ou CEI 61850-9-2 diffusant ces types de messages donnent des recommandations d'adressage basées sur les algorithmes de hachage des circuits intégrés MAC. Une telle recommandation pourrait être la suivante:

Les adresses multidiffusion (chaine d'octets de taille 6) utilisées dans la présente norme auront la structure suivante.

- Les trois premiers octets sont affectés par l'IEEE:01-OC-CD.
- Le quatrième octet sera 01 pour GOOSE, 02 pour GSSE, et 04 pour des valeurs échantillonnées multidiffusion.
- Les deux derniers octets seront utilisés en tant qu'adresses individuelles affectées dans les plages définies par le Tableau B.1.

Tableau B.1 – Exemple d'adressage multidiffusion recommandé

| Service | Début d'adresse (hexadécimal) | Fin d'adresse (hexadécimal) |
|---------|-------------------------------|-------------------------------|
| GOOSE   | 01-OC-CD-01-00-00            | 01-OC-CD-01-FF               |
| GSSE    | 01-OC-CD-02-00-00            | 01-OC-CD-02-FF               |
| Multicast sampled values | 01-OC-CD-04-00-00            | 01-OC-CD-04-FF               |

This document is useful for technical professionals working with the CEI 61850 standards, particularly those involved in the design and implementation of systems that utilize multicast addresses for communication.

---

## Page 68

![Image 1 from page 68](images/iec61850-9-2{ed2.0}b_page68_img40_e759d4ee.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank page with no visible content, diagrams, or text. It is likely a placeholder or an error in the document, as there is no discernible technical information or data present.

**Key Elements:** None

**Extracted Text:** None detected

The image does not contain any technical elements or data that can be analyzed or described. It seems to be an empty page or a placeholder in the document.

---

## Page 69

![Image 1 from page 69](images/iec61850-9-2{ed2.0}b_page69_img41_e759d4ee.png)

**Image Type:** Diagram

**Description:** The image appears to be a blank or placeholder diagram, possibly used in a technical document to indicate a section where a diagram should be inserted. It is likely part of a larger document, such as a technical manual or a presentation slide, where the actual diagram is meant to be placed but has not been added yet.

**Key Elements:** 
- The image is completely white, indicating no content or a placeholder.
- There are no technical elements, components, or data present in the image.
- The text at the bottom of the image provides information about the customer, order number, and copyright details.

**Extracted Text:**
Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2012-000331 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

This text is likely part of a footer or a note at the bottom of the page, providing copyright and contact information for the document.

---

## Page 70

![Image 1 from page 70](images/iec61850-9-2{ed2.0}b_page70_img42_b284b55e.png)

**Image Type:** Diagram
**Description:** The image appears to be a simple, blank white space with no discernible technical elements, components, or data. It lacks any text, symbols, or graphical elements that would typically be found in a technical diagram.
**Key Elements:** None
**Extracted Text:** No text detected

Since the image is completely blank, there is no technical analysis to provide. If you have another image or a different section of the document, please provide it for analysis.

![Image 2 from page 70](images/iec61850-9-2{ed2.0}b_page70_img43_4f1b94eb.png)

**Image Type:** Document

**Description:** The image is a scanned page from a technical document, specifically from the International Electrotechnical Commission (IEC). It appears to be a cover or introductory page, as it contains contact information and a header indicating the organization's name and address.

**Key Elements:**
- **Header:** INTERNATIONAL ELECTROTECHNICAL COMMISSION
- **Address:** 3, rue de Varembé, PO Box 131, CH-1211 Geneva 20, Switzerland
- **Contact Information:**
  - Telephone: +41 22 919 02 11
  - Fax: +41 22 919 03 00
  - Email: <EMAIL>
  - Website: www.iec.ch

**Extracted Text:**
INTERNATIONAL
ELECTROTECHNICAL
COMMISSION

3, rue de Varembé
PO Box 131
CH-1211 Geneva 20
Switzerland

Tel: +41 22 919 02 11
Fax: +41 22 919 03 00
<EMAIL>
www.iec.ch

Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2012-000331 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

---

