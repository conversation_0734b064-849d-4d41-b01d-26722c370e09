#!/usr/bin/env python3
"""
Test script for GPU OCR functionality

This script tests the availability and functionality of different OCR engines
and provides diagnostic information for troubleshooting.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if required packages are available."""
    print("🔍 Testing Package Imports...")
    
    results = {}
    
    # Test core packages
    try:
        import PyPDF2  # type: ignore
        results['PyPDF2'] = "✅ Available"
    except ImportError:
        results['PyPDF2'] = "❌ Missing"

    try:
        import fitz  # type: ignore
        results['PyMuPDF'] = "✅ Available"
    except ImportError:
        results['PyMuPDF'] = "❌ Missing"

    try:
        from PIL import Image  # type: ignore
        results['Pillow'] = "✅ Available"
    except ImportError:
        results['Pillow'] = "❌ Missing"

    # Test OCR engines
    try:
        import paddleocr  # type: ignore
        results['PaddleOCR'] = "✅ Available"
    except ImportError:
        results['PaddleOCR'] = "❌ Missing"

    try:
        import easyocr  # type: ignore
        results['EasyOCR'] = "✅ Available"
    except ImportError:
        results['EasyOCR'] = "❌ Missing"

    try:
        import pytesseract  # type: ignore
        results['Tesseract'] = "✅ Available"
    except ImportError:
        results['Tesseract'] = "❌ Missing"

    # Test GPU support
    try:
        import torch  # type: ignore
        gpu_available = torch.cuda.is_available()
        results['GPU (PyTorch)'] = f"✅ Available ({torch.cuda.device_count()} devices)" if gpu_available else "⚠️ No GPU"
    except ImportError:
        results['GPU (PyTorch)'] = "❌ PyTorch not installed"
    
    # Print results
    for package, status in results.items():
        print(f"  {package}: {status}")
    
    return results

def test_ocr_engines():
    """Test OCR engine initialization."""
    print("\n🧪 Testing OCR Engine Initialization...")
    
    # Test PaddleOCR
    try:
        import paddleocr  # type: ignore
        print("  PaddleOCR:")
        try:
            ocr = paddleocr.PaddleOCR(use_gpu=True, lang='en', show_log=False)
            print("    ✅ GPU initialization successful")
        except Exception as e:
            print(f"    ⚠️ GPU initialization failed: {e}")
            try:
                ocr = paddleocr.PaddleOCR(use_gpu=False, lang='en', show_log=False)
                print("    ✅ CPU initialization successful")
            except Exception as e2:
                print(f"    ❌ CPU initialization failed: {e2}")
    except ImportError:
        print("  PaddleOCR: ❌ Not installed")

    # Test EasyOCR
    try:
        import easyocr  # type: ignore
        print("  EasyOCR:")
        try:
            reader = easyocr.Reader(['en'], gpu=True)
            print("    ✅ GPU initialization successful")
        except Exception as e:
            print(f"    ⚠️ GPU initialization failed: {e}")
            try:
                reader = easyocr.Reader(['en'], gpu=False)
                print("    ✅ CPU initialization successful")
            except Exception as e2:
                print(f"    ❌ CPU initialization failed: {e2}")
    except ImportError:
        print("  EasyOCR: ❌ Not installed")

    # Test Tesseract
    try:
        import pytesseract  # type: ignore
        print("  Tesseract:")
        try:
            version = pytesseract.get_tesseract_version()
            print(f"    ✅ Available (version {version})")
        except Exception as e:
            print(f"    ❌ Not properly configured: {e}")
    except ImportError:
        print("  Tesseract: ❌ Not installed")

def test_enhanced_processor():
    """Test the enhanced PDF processor."""
    print("\n🔧 Testing Enhanced PDF Processor...")
    
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("pdf_processor", "pdf-to-markdown-processor.py")
        if spec and spec.loader:
            pdf_processor = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(pdf_processor)
            EnhancedPDFProcessor = pdf_processor.EnhancedPDFProcessor
            ProcessingConfig = pdf_processor.ProcessingConfig
        print("  ✅ Enhanced processor import successful")
        
        # Test configuration
        config = ProcessingConfig(
            use_gpu_ocr=True,
            ocr_engine="paddleocr",
            ocr_confidence_threshold=0.6
        )
        print("  ✅ Configuration creation successful")
        
        # Test processor initialization (without actual processing)
        print("  ⚠️ Processor initialization test skipped (requires input directory)")
        
    except ImportError as e:
        print(f"  ❌ Enhanced processor import failed: {e}")
    except Exception as e:
        print(f"  ❌ Configuration test failed: {e}")

def print_recommendations():
    """Print installation recommendations."""
    print("\n💡 Installation Recommendations:")
    print("  1. For best GPU performance: pip install paddlepaddle-gpu paddleocr")
    print("  2. For multi-language support: pip install easyocr")
    print("  3. For CPU-only environments: pip install pytesseract")
    print("  4. For complete functionality: pip install -r requirements.txt")
    print("\n🔗 Useful Links:")
    print("  • PaddleOCR: https://github.com/PaddlePaddle/PaddleOCR")
    print("  • EasyOCR: https://github.com/JaidedAI/EasyOCR")
    print("  • Tesseract: https://github.com/tesseract-ocr/tesseract")

def main():
    """Main test function."""
    print("🚀 GPU OCR Test Suite")
    print("=" * 50)
    
    # Run tests
    import_results = test_imports()
    test_ocr_engines()
    test_enhanced_processor()
    
    # Summary
    print("\n📊 Summary:")
    missing_core = [pkg for pkg, status in import_results.items() 
                   if pkg in ['PyPDF2', 'PyMuPDF', 'Pillow'] and '❌' in status]
    
    if missing_core:
        print(f"  ❌ Missing core packages: {', '.join(missing_core)}")
        print("  Run: pip install PyPDF2 pymupdf Pillow")
    else:
        print("  ✅ All core packages available")
    
    ocr_engines = [pkg for pkg, status in import_results.items() 
                  if pkg in ['PaddleOCR', 'EasyOCR', 'Tesseract'] and '✅' in status]
    
    if ocr_engines:
        print(f"  ✅ Available OCR engines: {', '.join(ocr_engines)}")
    else:
        print("  ⚠️ No OCR engines available - install at least one")
    
    if 'GPU' in str(import_results.get('GPU (PyTorch)', '')):
        print("  ✅ GPU support detected")
    else:
        print("  ⚠️ No GPU support - will use CPU fallback")
    
    print_recommendations()

if __name__ == "__main__":
    main()
