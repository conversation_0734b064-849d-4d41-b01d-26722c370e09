#!/usr/bin/env python3
"""
Test Ollama connection and available models
"""

import requests
import json
import sys

def test_ollama_connection(url="http://127.0.0.1:11434"):
    """Test connection to Ollama server."""
    
    print(f"🔍 Testing Ollama connection at {url}")
    
    try:
        # Test basic connection
        response = requests.get(f"{url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"✅ Connected to Ollama server successfully!")
            print(f"📊 Available models: {len(models)}")
            
            # List all models
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                size_mb = size / (1024 * 1024) if size > 0 else 0
                modified = model.get('modified_at', 'Unknown')
                
                print(f"   • {name} ({size_mb:.1f} MB) - Modified: {modified[:10]}")
            
            # Check for vision models
            vision_models = [m for m in models if 'llava' in m.get('name', '').lower() or 'vision' in m.get('name', '').lower()]
            
            if vision_models:
                print(f"\n🔍 Vision models found: {len(vision_models)}")
                for model in vision_models:
                    print(f"   ✅ {model.get('name', 'Unknown')}")
                return vision_models[0].get('name', 'llava')
            else:
                print(f"\n⚠️ No vision models found. Available models:")
                for model in models:
                    print(f"   • {model.get('name', 'Unknown')}")
                print(f"\nTo install a vision model, run:")
                print(f"   ollama pull llava:7b")
                print(f"   ollama pull llava:13b")
                return None
                
        else:
            print(f"❌ Ollama server returned status {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to Ollama server at {url}")
        print(f"Please ensure Ollama is running:")
        print(f"1. Install Ollama from https://ollama.ai/")
        print(f"2. Start server: ollama serve")
        print(f"3. Install vision model: ollama pull llava:7b")
        return None
        
    except Exception as e:
        print(f"❌ Error testing Ollama connection: {e}")
        return None

def test_vision_model(model_name, url="http://127.0.0.1:11434"):
    """Test vision model with a simple prompt."""
    
    print(f"\n🧠 Testing vision model: {model_name}")
    
    try:
        # Simple text-only test first
        payload = {
            "model": model_name,
            "prompt": "Hello! Can you see images?",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 50
            }
        }
        
        response = requests.post(
            f"{url}/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            
            print(f"✅ Model {model_name} is responding!")
            print(f"Response: {response_text[:100]}...")
            return True
        else:
            print(f"❌ Model test failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Main test function."""
    
    print("🔬 Ollama Connection and Vision Model Test")
    print("=" * 50)
    
    # Test connection and get available models
    vision_model = test_ollama_connection()
    
    if vision_model:
        # Test the vision model
        if test_vision_model(vision_model):
            print(f"\n🎉 Success! Ollama is ready for VLM image analysis")
            print(f"   Server: http://127.0.0.1:11434")
            print(f"   Vision Model: {vision_model}")
            print(f"\nNext steps:")
            print(f"   python vlm_image_analyzer.py image.png --backend ollama --model {vision_model}")
        else:
            print(f"\n⚠️ Vision model found but not responding correctly")
    else:
        print(f"\n❌ No vision models available")
        print(f"Install a vision model with: ollama pull llava:7b")

if __name__ == "__main__":
    main()
