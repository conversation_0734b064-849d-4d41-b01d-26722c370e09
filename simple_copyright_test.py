#!/usr/bin/env python3
"""
Simple test for copyright removal functions
"""

import re
import sys
from pathlib import Path

def remove_copyright_boilerplate(text: str) -> str:
    """Remove copyright boilerplate and legal notices from text."""
    if not text:
        return ""
    
    # Specific copyright patterns to remove
    copyright_patterns = [
        # IEC specific copyright notice
        r'Customer:\s*Phil Young\s*-\s*No\.\s*of\s*User\(s\):\s*\d+\s*-\s*Company:\s*TriangleMicroworks.*?Tel\.\:\s*\+41\s*22\s*919\s*02\s*11',
        
        # General IEC copyright patterns
        r'IMPORTANT:\s*This\s*file\s*is\s*copyright\s*of\s*IEC,\s*Geneva,\s*Switzerland\..*?Tel\.\:\s*\+\d+\s*\d+\s*\d+\s*\d+\s*\d+',
        r'This\s*file\s*is\s*subject\s*to\s*a\s*licence\s*agreement\..*?custserv@iec\.ch',
        r'All\s*rights\s*reserved\..*?custserv@iec\.ch',
        
        # Generic copyright patterns
        r'©\s*\d{4}.*?All\s*rights\s*reserved\.?',
        r'Copyright\s*©?\s*\d{4}.*?All\s*rights\s*reserved\.?',
        r'Copyright\s*\d{4}.*?All\s*rights\s*reserved\.?',
        
        # License and legal notices
        r'This\s*document\s*is\s*protected\s*by\s*copyright.*?',
        r'No\s*part\s*of\s*this\s*publication\s*may\s*be\s*reproduced.*?',
        r'Reproduction\s*or\s*distribution.*?without\s*permission.*?',
        
        # Order and customer information
        r'Order\s*No\.:\s*[\w\-\d]+',
        r'Customer:\s*[^-]*-\s*No\.\s*of\s*User\(s\):\s*\d+\s*-\s*Company:\s*[^-]*',
        
        # Contact information patterns
        r'Enquiries\s*to\s*Email:\s*[\w@\.]+\s*-\s*Tel\.:\s*[\+\d\s]+',
        r'Email:\s*[\w@\.]+\s*-\s*Tel\.:\s*[\+\d\s]+',
        
        # Publication info that's not content
        r'IEC\s*\d+\-\d+.*?Edition\s*\d+\.\d+',
        r'International\s*Electrotechnical\s*Commission.*?Geneva',
        
        # Page headers/footers with copyright
        r'^\s*–\s*\d+\s*–.*?IEC.*?$',
        r'^\s*IEC\s*\d+\-\d+.*?–\s*\d+\s*–\s*$',
    ]
    
    # Apply all patterns
    cleaned_text = text
    for pattern in copyright_patterns:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE | re.MULTILINE | re.DOTALL)
    
    # Remove empty lines and excessive whitespace left by removals
    lines = [line.strip() for line in cleaned_text.split('\n')]
    lines = [line for line in lines if line and not is_boilerplate_line(line)]
    
    return '\n'.join(lines)

def is_boilerplate_line(line: str) -> bool:
    """Check if a line is likely boilerplate text that should be removed."""
    if not line or len(line.strip()) < 3:
        return True
        
    line_lower = line.lower().strip()
    
    # Common boilerplate indicators
    boilerplate_indicators = [
        'all rights reserved',
        'copyright',
        'licence agreement',
        'license agreement', 
        '<EMAIL>',
        'trianglemicroworks',
        'phil young',
        'order no.',
        'important: this file',
        'enquiries to email',
        'geneva, switzerland',
        'international electrotechnical commission',
        'no part of this publication',
        'reproduction or distribution',
        'without permission',
        'protected by copyright'
    ]
    
    # Check if line contains boilerplate indicators
    for indicator in boilerplate_indicators:
        if indicator in line_lower:
            return True
    
    # Check for patterns like "– 12 –" or "IEC 61850-4 – 14 –"
    if re.match(r'^\s*[–\-]\s*\d+\s*[–\-]\s*$', line) or re.match(r'^\s*IEC\s*\d+\-\d+.*?[–\-]\s*\d+\s*[–\-]\s*$', line):
        return True
        
    return False

def test_copyright_removal():
    """Test the copyright removal functionality."""
    
    print("🧪 Testing Copyright Removal")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            "name": "IEC Specific Copyright",
            "text": """Customer: Phil Young - No. of User(s): 1 - Company: TriangleMicroworks
Order No.: WS-2011-009821 - IMPORTANT: This file is copyright of IEC, Geneva, Switzerland. All rights reserved.
This file is subject to a licence agreement. Enquiries to Email: <EMAIL> - Tel.: +41 22 919 02 11

This is the actual technical content that should be preserved.
It contains important information about electrical standards."""
        },
        {
            "name": "Generic Copyright",
            "text": """© 2024 International Electrotechnical Commission. All rights reserved.
Copyright 2024 IEC Geneva Switzerland. All rights reserved.

Section 4.2 Technical Requirements

The system shall comply with the following specifications:
- Voltage range: 100-240V AC
- Frequency: 50/60 Hz"""
        },
        {
            "name": "Page Headers/Footers",
            "text": """– 12 –    IEC 61850-4 – 14 –

4.3 Communication protocols

The communication shall use the following protocols:
- HTTP/HTTPS for web services
- TCP/IP for network communication

IEC 61850-4 – 16 –    – 17 –"""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print("-" * 30)
        
        original = test_case['text']
        cleaned = remove_copyright_boilerplate(original)
        
        print(f"📄 Original ({len(original)} chars):")
        print(f"   {repr(original[:100])}...")
        
        print(f"🧹 Cleaned ({len(cleaned)} chars):")
        print(f"   {repr(cleaned[:100])}...")
        
        reduction = ((len(original) - len(cleaned)) / len(original)) * 100 if original else 0
        print(f"📊 Reduction: {reduction:.1f}%")
        
        # Check if copyright terms are removed
        copyright_terms = ['phil young', 'trianglemicroworks', '<EMAIL>', 'all rights reserved']
        remaining_terms = [term for term in copyright_terms if term.lower() in cleaned.lower()]
        
        if remaining_terms:
            print(f"⚠️ Still contains: {remaining_terms}")
        else:
            print(f"✅ Copyright terms successfully removed")

def test_with_existing_files():
    """Test with existing markdown files."""
    
    print(f"\n📁 Testing with existing markdown files...")
    
    markdown_dir = Path("markdown_output")
    if not markdown_dir.exists():
        print(f"⚠️ No markdown_output directory found")
        return
    
    md_files = list(markdown_dir.glob("*.md"))
    if not md_files:
        print(f"⚠️ No markdown files found")
        return
    
    print(f"📊 Found {len(md_files)} markdown files")
    
    for md_file in md_files[:2]:  # Test first 2 files
        print(f"\n📄 Analyzing: {md_file.name}")
        
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for copyright issues
            copyright_terms = [
                'phil young', 'trianglemicroworks', '<EMAIL>', 
                'all rights reserved', 'order no.', 'important: this file'
            ]
            
            found_terms = []
            for term in copyright_terms:
                if term.lower() in content.lower():
                    count = content.lower().count(term.lower())
                    found_terms.append(f"{term} ({count}x)")
            
            print(f"   - File size: {len(content)} characters")
            
            if found_terms:
                print(f"   ⚠️ Copyright terms found: {found_terms}")
                print(f"   🔧 File needs copyright removal")
            else:
                print(f"   ✅ No copyright terms detected")
                
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")

def main():
    """Main test function."""
    
    test_copyright_removal()
    test_with_existing_files()
    
    print(f"\n🎯 **Summary:**")
    print(f"✅ Copyright removal patterns implemented")
    print(f"✅ Boilerplate detection active")
    print(f"✅ Pattern-based filtering working")
    
    print(f"\n🔧 **To apply to existing files:**")
    print(f"1. Re-run the PDF processor with updated code")
    print(f"2. Check logs for filtering warnings")
    print(f"3. Verify copyright removal in output files")

if __name__ == "__main__":
    main()
