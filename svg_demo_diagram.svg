<?xml version="1.0" ?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1224 1584" width="1224" height="1584">
  <g id="background"/>
  <g id="shapes" stroke="black" stroke-width="1" fill="none">
    <line x1="0" y1="807" x2="999" y2="808"/>
    <line x1="0" y1="970" x2="999" y2="971"/>
    <line x1="0" y1="425" x2="999" y2="426"/>
    <line x1="0" y1="1217" x2="999" y2="1218"/>
    <line x1="0" y1="427" x2="999" y2="428"/>
    <line x1="0" y1="567" x2="999" y2="568"/>
    <line x1="0" y1="569" x2="999" y2="570"/>
    <line x1="0" y1="599" x2="999" y2="600"/>
    <line x1="0" y1="1002" x2="999" y2="1003"/>
    <line x1="0" y1="1004" x2="999" y2="1005"/>
    <line x1="0" y1="1034" x2="999" y2="1035"/>
    <line x1="0" y1="1036" x2="999" y2="1037"/>
    <line x1="0" y1="1067" x2="999" y2="1068"/>
    <line x1="0" y1="1099" x2="999" y2="1100"/>
    <line x1="0" y1="1101" x2="999" y2="1102"/>
    <line x1="0" y1="1131" x2="999" y2="1132"/>
    <line x1="0" y1="1133" x2="999" y2="1134"/>
    <line x1="0" y1="1185" x2="999" y2="1186"/>
    <line x1="0" y1="1215" x2="999" y2="1216"/>
    <line x1="0" y1="601" x2="999" y2="602"/>
    <line x1="0" y1="479" x2="999" y2="480"/>
    <line x1="0" y1="805" x2="999" y2="806"/>
    <line x1="0" y1="972" x2="999" y2="973"/>
    <line x1="0" y1="1069" x2="999" y2="1070"/>
    <line x1="0" y1="1183" x2="999" y2="1184"/>
    <line x1="0" y1="477" x2="999" y2="478"/>
    <line x1="0" y1="96" x2="999" y2="97"/>
    <line x1="0" y1="1409" x2="999" y2="1410"/>
    <line x1="0" y1="84" x2="999" y2="85"/>
    <line x1="180" y1="1000" x2="180" y2="0"/>
    <line x1="178" y1="1000" x2="178" y2="0"/>
    <line x1="1059" y1="1000" x2="1059" y2="0"/>
    <line x1="1057" y1="1000" x2="1057" y2="0"/>
    <line x1="292" y1="1000" x2="292" y2="0"/>
    <line x1="294" y1="1000" x2="294" y2="0"/>
    <line x1="513" y1="1000" x2="513" y2="0"/>
    <line x1="0" y1="1398" x2="999" y2="1399"/>
    <line x1="0" y1="1421" x2="999" y2="1422"/>
    <line x1="515" y1="1000" x2="515" y2="0"/>
    <line x1="569" y1="1000" x2="569" y2="0"/>
    <line x1="571" y1="1000" x2="571" y2="0"/>
    <line x1="0" y1="1429" x2="999" y2="1430"/>
    <line x1="0" y1="233" x2="999" y2="234"/>
    <line x1="563" y1="1009" x2="598" y2="0"/>
    <line x1="0" y1="243" x2="999" y2="244"/>
    <line x1="620" y1="1000" x2="620" y2="0"/>
    <line x1="0" y1="258" x2="999" y2="259"/>
    <line x1="0" y1="193" x2="999" y2="194"/>
    <line x1="0" y1="267" x2="999" y2="268"/>
    <line x1="390" y1="1000" x2="390" y2="0"/>
    <rect x="174" y="1399" width="130" height="127"/>
    <rect x="178" y="971" width="882" height="248"/>
    <rect x="178" y="426" width="882" height="383"/>
  </g>
  <g id="text" font-family="Arial, sans-serif" font-size="12" fill="black">
    <text x="562" y="81">Page</text>
    <text x="611" y="77">7</text>
    <text x="627" y="80">/</text>
    <text x="642" y="77">31</text>
    <text x="181" y="158">7</text>
    <text x="224" y="159">SPECIFICATION</text>
    <text x="372" y="158">OF</text>
    <text x="401" y="158">THE</text>
    <text x="445" y="158">LOGICAL</text>
    <text x="533" y="158">DEVICE</text>
    <text x="609" y="158">&quot;MERGING</text>
    <text x="718" y="159">UNIT&quot;</text>
    <text x="181" y="196">IEC</text>
    <text x="223" y="196">61850</text>
    <text x="290" y="196">does</text>
    <text x="345" y="196">not</text>
    <text x="385" y="200">specify</text>
    <text x="459" y="200">logical</text>
    <text x="526" y="196">devices.</text>
    <text x="610" y="200">Logical</text>
    <text x="682" y="196">devices</text>
    <text x="760" y="200">may</text>
    <text x="809" y="196">be</text>
    <text x="843" y="196">described</text>
    <text x="940" y="196">in</text>
    <text x="967" y="196">SCL</text>
    <text x="1012" y="196">and</text>
    <text x="180" y="225">configured</text>
    <text x="284" y="225">through</text>
    <text x="365" y="221">several</text>
    <text x="438" y="221">services</text>
    <text x="519" y="221">of</text>
    <text x="548" y="221">IEC</text>
    <text x="589" y="221">61850.</text>
    <text x="660" y="221">To</text>
    <text x="695" y="221">reduce</text>
    <text x="765" y="221">the</text>
    <text x="804" y="221">first</text>
    <text x="850" y="225">implementations</text>
    <text x="1005" y="221">to</text>
    <text x="1034" y="221">a</text>
    <text x="181" y="245">minimum</text>
    <text x="271" y="245">of</text>
    <text x="297" y="249">required</text>
    <text x="378" y="245">services</text>
    <text x="456" y="245">without</text>
    <text x="531" y="249">loosing</text>
    <text x="601" y="249">interoperability,</text>
    <text x="746" y="245">this</text>
    <text x="786" y="249">guideline</text>
    <text x="875" y="249">provides</text>
    <text x="956" y="245">a</text>
    <text x="975" y="245">detailed</text>
    <text x="180" y="273">specification</text>
    <text x="295" y="269">of</text>
    <text x="318" y="269">the</text>
    <text x="354" y="273">logical</text>
    <text x="414" y="269">device</text>
    <text x="477" y="273">merging</text>
    <text x="556" y="269">unit</text>
    <text x="595" y="269">as</text>
    <text x="622" y="269">used</text>
    <text x="669" y="269">within</text>
    <text x="728" y="269">the</text>
    <text x="763" y="273">scope</text>
    <text x="819" y="269">of</text>
    <text x="842" y="269">this</text>
    <text x="880" y="273">guideline.</text>
    <text x="181" y="306">7.1</text>
    <text x="238" y="306">Definition</text>
    <text x="341" y="306">of</text>
    <text x="370" y="306">the</text>
    <text x="405" y="312">objects</text>
    <text x="477" y="312">according</text>
    <text x="573" y="306">to</text>
    <text x="599" y="306">IEC</text>
    <text x="644" y="306">61850-7-2</text>
    <text x="182" y="337">7.1.1</text>
    <text x="254" y="337">The</text>
    <text x="290" y="342">logical</text>
    <text x="346" y="338">device</text>
    <text x="397" y="337">instance</text>
    <text x="469" y="337">&quot;MU&quot;</text>
    <text x="536" y="342">([7-2]</text>
    <text x="586" y="342">8.1.1)</text>
    <text x="180" y="369">The</text>
    <text x="220" y="369">attributes</text>
    <text x="310" y="369">of</text>
    <text x="333" y="369">the</text>
    <text x="369" y="373">logical</text>
    <text x="430" y="369">device</text>
    <text x="493" y="369">MU</text>
    <text x="527" y="369">shall</text>
    <text x="573" y="369">have</text>
    <text x="621" y="369">the</text>
    <text x="655" y="373">following</text>
    <text x="741" y="369">values:</text>
    <text x="403" y="403">Table</text>
    <text x="469" y="403">4</text>
    <text x="489" y="398">—</text>
    <text x="510" y="407">Logical</text>
    <text x="593" y="403">device</text>
    <text x="669" y="403">instance</text>
    <text x="767" y="403">&quot;MU&quot;</text>
    <text x="196" y="460">Attribute</text>
    <text x="379" y="450">Value</text>
    <text x="774" y="450">Comment</text>
    <text x="213" y="469">Name</text>
    <text x="188" y="501">LDName</text>
    <text x="300" y="501">xxxxMUnn</text>
    <text x="522" y="501">m</text>
    <text x="578" y="501">xxxx</text>
    <text x="626" y="501">is</text>
    <text x="644" y="504">configurable</text>
    <text x="746" y="504">according</text>
    <text x="827" y="501">to</text>
    <text x="849" y="504">[6],</text>
    <text x="883" y="501">clause</text>
    <text x="938" y="501">8.4.2</text>
    <text x="984" y="501">and</text>
    <text x="579" y="521">MUnn</text>
    <text x="634" y="521">is</text>
    <text x="652" y="521">the</text>
    <text x="683" y="521">Attribute</text>
    <text x="758" y="521">Inst</text>
    <text x="794" y="521">of</text>
    <text x="815" y="521">the</text>
    <text x="846" y="521">element</text>
    <text x="915" y="521">LDevice</text>
    <text x="981" y="521">in</text>
    <text x="1000" y="521">the</text>
    <text x="579" y="540">IED</text>
    <text x="612" y="540">section</text>
    <text x="673" y="540">of</text>
    <text x="694" y="540">the</text>
    <text x="725" y="540">SCL</text>
    <text x="760" y="543">(nn</text>
    <text x="792" y="540">shall</text>
    <text x="834" y="543">identify</text>
    <text x="898" y="540">the</text>
    <text x="929" y="543">measuring</text>
    <text x="579" y="562">point</text>
    <text x="624" y="559">within</text>
    <text x="677" y="559">the</text>
    <text x="708" y="562">bay)</text>
    <text x="188" y="592">LDRef</text>
    <text x="301" y="592">xxxMUnn</text>
    <text x="522" y="592">m</text>
    <text x="188" y="627">LogicalNode</text>
    <text x="288" y="634">|</text>
    <text x="301" y="624">LLNO</text>
    <text x="522" y="624">m</text>
    <text x="555" y="634">|</text>
    <text x="580" y="624">1..5</text>
    <text x="621" y="624">is</text>
    <text x="638" y="624">the</text>
    <text x="669" y="624">attribute</text>
    <text x="743" y="624">Inst</text>
    <text x="779" y="624">of</text>
    <text x="800" y="624">the</text>
    <text x="831" y="624">element</text>
    <text x="900" y="624">LN</text>
    <text x="926" y="624">in</text>
    <text x="945" y="624">the</text>
    <text x="977" y="624">IED</text>
    <text x="301" y="643">LPHD</text>
    <text x="522" y="643">m</text>
    <text x="552" y="653">|</text>
    <text x="578" y="643">section</text>
    <text x="301" y="663">InnATCTR1</text>
    <text x="522" y="663">m</text>
    <text x="579" y="669">Unn</text>
    <text x="614" y="672">/</text>
    <text x="628" y="669">Inn</text>
    <text x="660" y="669">is</text>
    <text x="678" y="669">the</text>
    <text x="709" y="669">identification</text>
    <text x="816" y="669">of</text>
    <text x="837" y="669">the</text>
    <text x="868" y="672">Sensor;</text>
    <text x="932" y="672">A,</text>
    <text x="956" y="672">B,</text>
    <text x="976" y="669">Cand</text>
    <text x="1022" y="669">N</text>
    <text x="301" y="682">InnBTCTR2</text>
    <text x="522" y="682">m</text>
    <text x="578" y="688">are</text>
    <text x="609" y="688">the</text>
    <text x="640" y="691">phase</text>
    <text x="692" y="688">identification.</text>
    <text x="805" y="688">Both</text>
    <text x="845" y="688">values</text>
    <text x="901" y="688">are</text>
    <text x="932" y="691">part</text>
    <text x="969" y="688">of</text>
    <text x="990" y="688">the</text>
    <text x="301" y="701">InnCTCTR3</text>
    <text x="522" y="701">m</text>
    <text x="578" y="707">substation</text>
    <text x="664" y="707">section</text>
    <text x="725" y="707">of</text>
    <text x="746" y="707">the</text>
    <text x="778" y="707">SCL</text>
    <text x="812" y="707">and</text>
    <text x="847" y="707">are</text>
    <text x="878" y="707">used</text>
    <text x="920" y="707">to</text>
    <text x="942" y="707">build</text>
    <text x="985" y="707">the</text>
    <text x="301" y="721">InnNTCTR4</text>
    <text x="522" y="721">m</text>
    <text x="579" y="727">name</text>
    <text x="626" y="730">according</text>
    <text x="707" y="727">to</text>
    <text x="730" y="730">[6],</text>
    <text x="764" y="727">clause</text>
    <text x="818" y="727">8.4.2</text>
    <text x="301" y="740">UnnATVTR1</text>
    <text x="522" y="740">m</text>
    <text x="301" y="769">UnnBTVTR2</text>
    <text x="522" y="759">m</text>
    <text x="301" y="779">UnnCTVTR3</text>
    <text x="522" y="779">m</text>
    <text x="301" y="798">UnnNTVTR4</text>
    <text x="522" y="798">m</text>
    <text x="182" y="857">7.1.2</text>
    <text x="254" y="857">The</text>
    <text x="290" y="862">logical</text>
    <text x="346" y="857">node</text>
    <text x="388" y="857">instance</text>
    <text x="460" y="857">&quot;LLNO&quot;</text>
    <text x="600" y="862">9.1.1)</text>
    <text x="180" y="889">The</text>
    <text x="220" y="889">attributes</text>
    <text x="310" y="889">of</text>
    <text x="333" y="889">the</text>
    <text x="369" y="893">logical</text>
    <text x="431" y="889">node</text>
    <text x="481" y="889">LLNO</text>
    <text x="530" y="889">shall</text>
    <text x="577" y="889">have</text>
    <text x="624" y="889">the</text>
    <text x="659" y="893">following</text>
    <text x="744" y="889">values:</text>
    <text x="532" y="948">Table</text>
    <text x="599" y="948">5</text>
    <text x="618" y="943">—</text>
    <text x="639" y="948">LLNO</text>
    <text x="217" y="995">Attribute</text>
    <text x="304" y="995">Name</text>
    <text x="452" y="995">Value</text>
    <text x="573" y="998">M/O</text>
    <text x="798" y="995">Comment</text>
    <text x="188" y="1027">LNName</text>
    <text x="398" y="1027">LLNO</text>
    <text x="570" y="1027">m</text>
    <text x="188" y="1059">LNRef</text>
    <text x="570" y="1059">m</text>
    <text x="188" y="1092">Data</text>
    <text x="626" y="1092">As</text>
    <text x="651" y="1092">defined</text>
    <text x="715" y="1092">in</text>
    <text x="735" y="1092">61850-7-4</text>
    <text x="188" y="1124">DataSet</text>
    <text x="397" y="1124">PhsMeas1</text>
    <text x="570" y="1124">m</text>
    <text x="188" y="1159">MultiCastSampledValueC</text>
    <text x="386" y="1164">|</text>
    <text x="397" y="1156">MSVCB01</text>
    <text x="570" y="1156">cl</text>
    <text x="187" y="1175">ontrolBlock</text>
    <text x="397" y="1175">MSVCB02</text>
    <text x="570" y="1175">cl</text>
    <text x="187" y="1208">c1</text>
    <text x="210" y="1204">—</text>
    <text x="225" y="1208">At</text>
    <text x="249" y="1208">least</text>
    <text x="291" y="1208">one</text>
    <text x="325" y="1208">of</text>
    <text x="346" y="1208">the</text>
    <text x="377" y="1208">two</text>
    <text x="412" y="1211">MulticastSampledValueControlBlock</text>
    <text x="693" y="1208">shall</text>
    <text x="735" y="1208">be</text>
    <text x="760" y="1211">implemented</text>
    <text x="479" y="1431">n</text>
    <text x="493" y="1431">Guideline</text>
    <text x="573" y="1431">for</text>
    <text x="650" y="1431">I</text>
    <text x="659" y="1431">Interface</text>
    <text x="738" y="1431">to</text>
    <text x="760" y="1431">Instrument</text>
    <text x="856" y="1431">Transformers</text>
    <text x="969" y="1434">using</text>
    <text x="1017" y="1431">IEC</text>
    <text x="960" y="1451">61850-9-2</text>
    <text x="872" y="1489">Modification</text>
    <text x="961" y="1489">Index:</text>
    <text x="1012" y="1489">R2-1</text>
    <text x="812" y="1509">Last</text>
    <text x="845" y="1509">date</text>
    <text x="881" y="1509">of</text>
    <text x="900" y="1512">storage:</text>
    <text x="964" y="1509">2004-07-07</text>
  </g>
</svg>
