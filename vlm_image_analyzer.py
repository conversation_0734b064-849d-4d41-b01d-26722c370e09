#!/usr/bin/env python3
"""
VLM-Enhanced Image Analyzer and SVG Converter
Uses local Visual Language Models for intelligent image analysis and SVG generation.
"""

import sys
import os
import json
import base64
from pathlib import Path
from PIL import Image
import requests
from typing import Dict, List, Optional, Tuple, Any
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re

class VLMImageAnalyzer:
    """Visual Language Model-based image analyzer for intelligent SVG conversion."""
    
    def __init__(self, backend="ollama", model_name="llava:7b"):
        self.backend = backend
        self.model_name = model_name
        self.ollama_url = "http://localhost:11434"
        
        # Initialize the VLM backend
        self._initialize_backend()
    
    def _initialize_backend(self):
        """Initialize the VLM backend."""
        if self.backend == "ollama":
            self._test_ollama_connection()
        elif self.backend == "transformers":
            self._initialize_transformers()
        else:
            raise ValueError(f"Unsupported backend: {self.backend}")
    
    def _test_ollama_connection(self):
        """Test connection to Ollama server."""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = [model['name'] for model in response.json().get('models', [])]
                print(f"✅ Connected to Ollama. Available models: {models}")
                
                # Check if vision model is available
                vision_models = [m for m in models if 'llava' in m.lower() or 'vision' in m.lower()]
                if vision_models:
                    self.model_name = vision_models[0]
                    print(f"🔍 Using vision model: {self.model_name}")
                else:
                    print("⚠️ No vision models found. Please install one:")
                    print("   ollama pull llava:7b")
                    print("   ollama pull llava:13b")
                    print("   ollama pull llava:34b")
                    raise ConnectionError("No vision models available")
            else:
                raise ConnectionError(f"Ollama server returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Failed to connect to Ollama: {e}")
            print("Please ensure Ollama is running:")
            print("1. Install Ollama: https://ollama.ai/")
            print("2. Start server: ollama serve")
            print("3. Install vision model: ollama pull llava:7b")
            raise
    
    def _initialize_transformers(self):
        """Initialize local transformers-based VLM."""
        try:
            from transformers import LlavaNextProcessor, LlavaNextForConditionalGeneration
            import torch
            
            model_id = "llava-hf/llava-v1.6-mistral-7b-hf"
            self.processor = LlavaNextProcessor.from_pretrained(model_id)
            self.model = LlavaNextForConditionalGeneration.from_pretrained(
                model_id, 
                torch_dtype=torch.float16, 
                low_cpu_mem_usage=True
            )
            print(f"✅ Loaded local VLM: {model_id}")
        except ImportError:
            print("❌ Transformers not available. Install with:")
            print("pip install transformers torch torchvision")
            raise
    
    def _encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for API calls."""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def analyze_image_with_vlm(self, image_path: str) -> Dict[str, Any]:
        """Analyze image using VLM for comprehensive understanding."""
        
        analysis_prompt = """
Analyze this technical image in detail and provide a structured analysis:

1. **Image Type**: What type of image is this? (diagram, chart, table, schematic, etc.)
2. **Content Description**: Describe all visual elements, text, shapes, and their relationships
3. **Technical Elements**: Identify any technical components, measurements, scales, labels
4. **Spatial Layout**: Describe the organization and positioning of elements
5. **Text Content**: List all readable text with approximate positions
6. **Geometric Shapes**: Identify lines, rectangles, circles, grids, etc.
7. **SVG Suitability**: Rate 1-10 how suitable this is for SVG conversion and why
8. **Conversion Strategy**: Suggest the best approach for converting to SVG

Please be very detailed and technical in your analysis. Focus on elements that would be important for creating an accurate SVG representation.
"""
        
        if self.backend == "ollama":
            return self._analyze_with_ollama(image_path, analysis_prompt)
        elif self.backend == "transformers":
            return self._analyze_with_transformers(image_path, analysis_prompt)
    
    def _analyze_with_ollama(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """Analyze image using Ollama vision model."""
        try:
            # Encode image
            image_b64 = self._encode_image_base64(image_path)
            
            # Prepare request
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_b64],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 2000
                }
            }
            
            # Make request
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis_text = result.get('response', '')
                
                # Parse the structured response
                return self._parse_vlm_response(analysis_text)
            else:
                raise Exception(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ VLM analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_with_transformers(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """Analyze image using local transformers model."""
        try:
            import torch
            
            # Load and process image
            image = Image.open(image_path)
            inputs = self.processor(prompt, image, return_tensors="pt")
            
            # Generate response
            with torch.no_grad():
                output = self.model.generate(**inputs, max_new_tokens=2000)
            
            # Decode response
            response = self.processor.decode(output[0], skip_special_tokens=True)
            
            # Parse the structured response
            return self._parse_vlm_response(response)
            
        except Exception as e:
            print(f"❌ Local VLM analysis failed: {e}")
            return {"error": str(e)}
    
    def _parse_vlm_response(self, response_text: str) -> Dict[str, Any]:
        """Parse structured VLM response into components."""
        analysis = {
            "raw_response": response_text,
            "image_type": "",
            "content_description": "",
            "technical_elements": [],
            "spatial_layout": "",
            "text_content": [],
            "geometric_shapes": [],
            "svg_suitability": 0,
            "conversion_strategy": ""
        }
        
        # Simple parsing - look for numbered sections
        sections = {
            "image type": "image_type",
            "content description": "content_description", 
            "technical elements": "technical_elements",
            "spatial layout": "spatial_layout",
            "text content": "text_content",
            "geometric shapes": "geometric_shapes",
            "svg suitability": "svg_suitability",
            "conversion strategy": "conversion_strategy"
        }
        
        current_section = None
        current_content = []
        
        for line in response_text.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # Check if this line starts a new section
            section_found = False
            for section_key, field_name in sections.items():
                if section_key in line.lower() and ('**' in line or ':' in line):
                    # Save previous section
                    if current_section and current_content:
                        content = '\n'.join(current_content).strip()
                        if current_section == "svg_suitability":
                            # Extract numeric rating
                            import re
                            match = re.search(r'(\d+)', content)
                            analysis[current_section] = int(match.group(1)) if match else 5
                        else:
                            analysis[current_section] = content
                    
                    # Start new section
                    current_section = field_name
                    current_content = []
                    section_found = True
                    break
            
            if not section_found and current_section:
                current_content.append(line)
        
        # Save final section
        if current_section and current_content:
            content = '\n'.join(current_content).strip()
            if current_section == "svg_suitability":
                import re
                match = re.search(r'(\d+)', content)
                analysis[current_section] = int(match.group(1)) if match else 5
            else:
                analysis[current_section] = content
        
        return analysis
    
    def generate_svg_with_vlm(self, image_path: str, analysis: Dict[str, Any]) -> str:
        """Generate SVG code using VLM understanding."""
        
        svg_prompt = f"""
Based on this analysis of a technical image:

Image Type: {analysis.get('image_type', 'Unknown')}
Content: {analysis.get('content_description', '')}
Technical Elements: {analysis.get('technical_elements', '')}
Geometric Shapes: {analysis.get('geometric_shapes', '')}
Text Content: {analysis.get('text_content', '')}

Generate clean, semantic SVG code that recreates this image. Include:
1. Proper viewBox dimensions
2. Organized groups for different element types
3. All text elements with appropriate positioning
4. All geometric shapes (lines, rectangles, circles)
5. Semantic IDs and classes for better structure
6. Clean, readable code structure

Output only the SVG code, no explanations.
"""
        
        if self.backend == "ollama":
            return self._generate_svg_with_ollama(image_path, svg_prompt)
        elif self.backend == "transformers":
            return self._generate_svg_with_transformers(image_path, svg_prompt)
    
    def _generate_svg_with_ollama(self, image_path: str, prompt: str) -> str:
        """Generate SVG using Ollama."""
        try:
            image_b64 = self._encode_image_base64(image_path)
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_b64],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 3000
                }
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=180
            )
            
            if response.status_code == 200:
                result = response.json()
                svg_code = result.get('response', '')
                
                # Extract SVG code from response
                return self._extract_svg_code(svg_code)
            else:
                raise Exception(f"Ollama API error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ SVG generation failed: {e}")
            return self._generate_fallback_svg(image_path)
    
    def _generate_svg_with_transformers(self, image_path: str, prompt: str) -> str:
        """Generate SVG using local transformers model."""
        try:
            import torch
            
            image = Image.open(image_path)
            inputs = self.processor(prompt, image, return_tensors="pt")
            
            with torch.no_grad():
                output = self.model.generate(**inputs, max_new_tokens=3000)
            
            response = self.processor.decode(output[0], skip_special_tokens=True)
            return self._extract_svg_code(response)
            
        except Exception as e:
            print(f"❌ Local SVG generation failed: {e}")
            return self._generate_fallback_svg(image_path)
    
    def _extract_svg_code(self, response: str) -> str:
        """Extract SVG code from VLM response."""
        # Look for SVG tags
        svg_match = re.search(r'<svg[^>]*>.*?</svg>', response, re.DOTALL | re.IGNORECASE)
        if svg_match:
            return svg_match.group(0)
        
        # If no complete SVG found, try to construct one
        lines = response.split('\n')
        svg_lines = []
        in_svg = False
        
        for line in lines:
            if '<svg' in line.lower():
                in_svg = True
            if in_svg:
                svg_lines.append(line)
            if '</svg>' in line.lower():
                break
        
        if svg_lines:
            return '\n'.join(svg_lines)
        
        # Fallback: wrap content in SVG tags
        return f'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">\n{response}\n</svg>'
    
    def _generate_fallback_svg(self, image_path: str) -> str:
        """Generate basic fallback SVG."""
        img = Image.open(image_path)
        width, height = img.size
        
        return f'''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {width} {height}" width="{width}" height="{height}">
  <rect x="0" y="0" width="{width}" height="{height}" fill="white" stroke="black"/>
  <text x="{width//2}" y="{height//2}" text-anchor="middle" font-family="Arial" font-size="16">
    VLM Analysis Failed - Fallback SVG
  </text>
</svg>'''
    
    def convert_image_to_svg(self, image_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Complete VLM-based image to SVG conversion."""
        if output_path is None:
            output_path = Path(image_path).with_suffix('.svg')
        
        print(f"🔍 Analyzing image with VLM: {image_path}")
        
        # Step 1: Analyze image with VLM
        analysis = self.analyze_image_with_vlm(image_path)
        
        if "error" in analysis:
            return {"success": False, "error": analysis["error"]}
        
        print(f"📊 Image Type: {analysis.get('image_type', 'Unknown')}")
        print(f"🎯 SVG Suitability: {analysis.get('svg_suitability', 0)}/10")
        
        # Step 2: Generate SVG based on analysis
        print("🎨 Generating SVG with VLM...")
        svg_code = self.generate_svg_with_vlm(image_path, analysis)
        
        # Step 3: Save SVG
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(svg_code)
            
            # Calculate file sizes
            original_size = os.path.getsize(image_path)
            svg_size = os.path.getsize(output_path)
            reduction = (1 - svg_size / original_size) * 100
            
            print(f"✅ SVG created: {output_path}")
            print(f"📊 File size: {original_size:,} → {svg_size:,} bytes ({reduction:+.1f}%)")
            
            return {
                "success": True,
                "analysis": analysis,
                "svg_path": str(output_path),
                "original_size": original_size,
                "svg_size": svg_size,
                "size_reduction": reduction
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to save SVG: {e}"}

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='VLM-Enhanced Image to SVG Converter')
    parser.add_argument('image', help='Input image file')
    parser.add_argument('-o', '--output', help='Output SVG file')
    parser.add_argument('--backend', choices=['ollama', 'transformers'], default='ollama',
                       help='VLM backend to use')
    parser.add_argument('--model', default='llava:7b', help='Model name to use')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, don\'t convert')
    
    args = parser.parse_args()
    
    try:
        analyzer = VLMImageAnalyzer(backend=args.backend, model_name=args.model)
        
        if args.analyze_only:
            analysis = analyzer.analyze_image_with_vlm(args.image)
            print("\n🔍 VLM Analysis Results:")
            print("=" * 50)
            for key, value in analysis.items():
                if key != "raw_response":
                    print(f"{key.replace('_', ' ').title()}: {value}")
        else:
            result = analyzer.convert_image_to_svg(args.image, args.output)
            if result["success"]:
                print(f"\n🎉 Conversion successful!")
                print(f"Analysis: {result['analysis'].get('image_type', 'Unknown')}")
                print(f"Suitability: {result['analysis'].get('svg_suitability', 0)}/10")
            else:
                print(f"❌ Conversion failed: {result['error']}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
