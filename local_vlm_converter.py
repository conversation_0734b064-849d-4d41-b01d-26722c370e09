#!/usr/bin/env python3
"""
Local VLM Image Converter using Transformers
Fallback implementation for when Ollama is not available.
"""

import sys
import os
from pathlib import Path
from PIL import Image
import torch
from typing import Dict, List, Optional, Any
import json
import re

class LocalVLMConverter:
    """Local VLM converter using Hugging Face transformers."""
    
    def __init__(self, model_name="llava-hf/llava-1.5-7b-hf"):
        self.model_name = model_name
        self.processor = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        print(f"🔧 Initializing Local VLM on {self.device}")
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the local VLM model."""
        try:
            from transformers import LlavaProcessor, LlavaForConditionalGeneration
            
            print(f"📥 Loading model: {self.model_name}")
            print("   This may take a few minutes on first run...")
            
            # Load processor and model
            self.processor = LlavaProcessor.from_pretrained(self.model_name)
            self.model = LlavaForConditionalGeneration.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                low_cpu_mem_usage=True,
                device_map="auto" if self.device == "cuda" else None
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            print(f"✅ Model loaded successfully on {self.device}")
            
        except ImportError as e:
            print("❌ Required packages not installed. Please install:")
            print("pip install transformers torch torchvision accelerate")
            if "cuda" in str(e).lower():
                print("For CUDA support: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118")
            raise
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            print("Trying smaller model...")
            # Fallback to smaller model
            self.model_name = "llava-hf/llava-1.5-7b-hf"
            raise
    
    def analyze_image(self, image_path: str) -> Dict[str, Any]:
        """Analyze image using local VLM."""
        
        prompt = """USER: <image>
Analyze this technical image in detail. Provide a structured analysis covering:

1. Image Type: What type of technical content is this?
2. Visual Elements: Describe all shapes, lines, text, and components
3. Technical Content: Any measurements, scales, labels, or technical information
4. Layout: How elements are organized spatially
5. Text Content: List all readable text with approximate positions
6. SVG Suitability: Rate 1-10 how well this would convert to SVG format
7. Conversion Approach: Best strategy for SVG conversion

Be detailed and technical in your analysis.
ASSISTANT:"""
        
        try:
            # Load and process image
            image = Image.open(image_path).convert('RGB')
            
            # Prepare inputs
            inputs = self.processor(prompt, image, return_tensors="pt")
            
            # Move to device
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Generate response
            print("🧠 Analyzing image with VLM...")
            with torch.no_grad():
                output = self.model.generate(
                    **inputs,
                    max_new_tokens=1500,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.processor.decode(output[0], skip_special_tokens=True)
            
            # Extract just the assistant's response
            if "ASSISTANT:" in response:
                analysis_text = response.split("ASSISTANT:")[-1].strip()
            else:
                analysis_text = response
            
            # Parse the response
            return self._parse_analysis(analysis_text)
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            return {"error": str(e), "raw_response": ""}
    
    def _parse_analysis(self, text: str) -> Dict[str, Any]:
        """Parse VLM analysis response."""
        analysis = {
            "raw_response": text,
            "image_type": "",
            "visual_elements": "",
            "technical_content": "",
            "layout": "",
            "text_content": "",
            "svg_suitability": 5,
            "conversion_approach": ""
        }
        
        # Simple parsing based on numbered sections
        sections = {
            "image type": "image_type",
            "visual elements": "visual_elements",
            "technical content": "technical_content",
            "layout": "layout", 
            "text content": "text_content",
            "svg suitability": "svg_suitability",
            "conversion approach": "conversion_approach"
        }
        
        current_section = None
        current_content = []
        
        for line in text.split('\n'):
            line = line.strip()
            if not line:
                continue
            
            # Check for section headers
            section_found = False
            for section_key, field_name in sections.items():
                if section_key in line.lower() and any(char in line for char in [':', '.']):
                    # Save previous section
                    if current_section and current_content:
                        content = ' '.join(current_content).strip()
                        if current_section == "svg_suitability":
                            # Extract numeric rating
                            match = re.search(r'(\d+)', content)
                            analysis[current_section] = int(match.group(1)) if match else 5
                        else:
                            analysis[current_section] = content
                    
                    # Start new section
                    current_section = field_name
                    current_content = []
                    section_found = True
                    break
            
            if not section_found and current_section:
                current_content.append(line)
        
        # Save final section
        if current_section and current_content:
            content = ' '.join(current_content).strip()
            if current_section == "svg_suitability":
                match = re.search(r'(\d+)', content)
                analysis[current_section] = int(match.group(1)) if match else 5
            else:
                analysis[current_section] = content
        
        return analysis
    
    def generate_svg_description(self, image_path: str, analysis: Dict[str, Any]) -> str:
        """Generate SVG creation instructions using VLM."""
        
        prompt = f"""USER: <image>
Based on this analysis of the image:
- Type: {analysis.get('image_type', 'Technical diagram')}
- Elements: {analysis.get('visual_elements', '')}
- Technical: {analysis.get('technical_content', '')}
- Layout: {analysis.get('layout', '')}

Create detailed instructions for generating SVG code that recreates this image. Include:
1. Exact dimensions and viewBox
2. All geometric shapes with coordinates
3. All text elements with positions and content
4. Proper grouping and organization
5. Colors and styling

Be very specific about coordinates, sizes, and positioning.
ASSISTANT:"""
        
        try:
            image = Image.open(image_path).convert('RGB')
            inputs = self.processor(prompt, image, return_tensors="pt")
            
            if self.device == "cuda":
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            print("🎨 Generating SVG instructions...")
            with torch.no_grad():
                output = self.model.generate(
                    **inputs,
                    max_new_tokens=2000,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            response = self.processor.decode(output[0], skip_special_tokens=True)
            
            if "ASSISTANT:" in response:
                return response.split("ASSISTANT:")[-1].strip()
            return response
            
        except Exception as e:
            print(f"❌ SVG generation failed: {e}")
            return f"Error generating SVG instructions: {e}"
    
    def create_basic_svg(self, image_path: str, analysis: Dict[str, Any]) -> str:
        """Create a basic SVG based on analysis."""
        
        # Get image dimensions
        img = Image.open(image_path)
        width, height = img.size
        
        # Extract key information
        image_type = analysis.get('image_type', 'Technical Diagram')
        svg_rating = analysis.get('svg_suitability', 5)
        
        # Create basic SVG structure
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {width} {height}" width="{width}" height="{height}">
  <defs>
    <style>
      .title-text {{ font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }}
      .content-text {{ font-family: Arial, sans-serif; font-size: 12px; }}
      .analysis-text {{ font-family: Arial, sans-serif; font-size: 10px; fill: #666; }}
    </style>
  </defs>
  
  <!-- Background -->
  <rect x="0" y="0" width="{width}" height="{height}" fill="white" stroke="#ccc" stroke-width="1"/>
  
  <!-- Title -->
  <text x="{width//2}" y="30" text-anchor="middle" class="title-text">
    VLM-Analyzed: {image_type}
  </text>
  
  <!-- SVG Suitability Score -->
  <text x="20" y="60" class="content-text">
    SVG Suitability: {svg_rating}/10
  </text>
  
  <!-- Analysis Summary -->
  <text x="20" y="90" class="analysis-text">
    Visual Elements: {analysis.get('visual_elements', 'Not analyzed')[:100]}...
  </text>
  
  <text x="20" y="110" class="analysis-text">
    Technical Content: {analysis.get('technical_content', 'Not analyzed')[:100]}...
  </text>
  
  <!-- Placeholder for actual content -->
  <rect x="20" y="130" width="{width-40}" height="{height-160}" 
        fill="none" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  
  <text x="{width//2}" y="{height//2}" text-anchor="middle" class="content-text" fill="#999">
    [Original image content would be vectorized here]
  </text>
  
  <!-- Footer -->
  <text x="{width//2}" y="{height-20}" text-anchor="middle" class="analysis-text">
    Generated by Local VLM Converter - {analysis.get('conversion_approach', 'Basic conversion')}
  </text>
</svg>'''
        
        return svg_content
    
    def convert_image(self, image_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Convert image to SVG using local VLM analysis."""
        
        if output_path is None:
            output_path = Path(image_path).with_suffix('.svg')
        
        print(f"🔍 Converting with Local VLM: {image_path}")
        
        # Analyze image
        analysis = self.analyze_image(image_path)
        
        if "error" in analysis:
            return {"success": False, "error": analysis["error"]}
        
        print(f"📊 Analysis complete:")
        print(f"   Type: {analysis.get('image_type', 'Unknown')}")
        print(f"   SVG Suitability: {analysis.get('svg_suitability', 0)}/10")
        
        # Generate SVG
        svg_content = self.create_basic_svg(image_path, analysis)
        
        # Save SVG
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            # Calculate sizes
            original_size = os.path.getsize(image_path)
            svg_size = os.path.getsize(output_path)
            reduction = (1 - svg_size / original_size) * 100
            
            print(f"✅ SVG created: {output_path}")
            print(f"📊 File size: {original_size:,} → {svg_size:,} bytes ({reduction:+.1f}%)")
            
            return {
                "success": True,
                "analysis": analysis,
                "svg_path": str(output_path),
                "original_size": original_size,
                "svg_size": svg_size,
                "size_reduction": reduction
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to save SVG: {e}"}

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Local VLM Image to SVG Converter')
    parser.add_argument('image', help='Input image file')
    parser.add_argument('-o', '--output', help='Output SVG file')
    parser.add_argument('--model', default='llava-hf/llava-1.5-7b-hf', help='Hugging Face model to use')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, don\'t convert')
    
    args = parser.parse_args()
    
    try:
        converter = LocalVLMConverter(model_name=args.model)
        
        if args.analyze_only:
            analysis = converter.analyze_image(args.image)
            print("\n🔍 Local VLM Analysis Results:")
            print("=" * 50)
            for key, value in analysis.items():
                if key != "raw_response":
                    print(f"{key.replace('_', ' ').title()}: {value}")
        else:
            result = converter.convert_image(args.image, args.output)
            if result["success"]:
                print(f"\n🎉 Conversion successful!")
            else:
                print(f"❌ Conversion failed: {result['error']}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Install required packages: pip install transformers torch torchvision accelerate")
        print("2. For CUDA: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118")
        print("3. Ensure sufficient RAM/VRAM (8GB+ recommended)")
        sys.exit(1)

if __name__ == "__main__":
    main()
