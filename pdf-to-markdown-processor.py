#!/usr/bin/env python3
"""
Enhanced PDF to Markdown Processor with Image and Diagram Extraction

This script processes PDF files using local Ollama LLMs and retains visual content:
1. Text extraction from PDFs
2. Image and diagram extraction
3. Language detection and filtering with LLM assistance
4. Content cleaning and structuring
5. Markdown formatting with embedded images

Requirements:
pip install PyPDF2 requests langdetect python-magic-bin markdownify beautifulsoup4 pymupdf Pillow fitz

Ollama Setup:
1. Install Ollama: https://ollama.ai/
2. Pull a model: ollama pull llama2 (or mistral, codellama, etc.)
3. Start Ollama server: ollama serve
"""

import os
import sys
import re
import requests
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import logging
from dataclasses import dataclass
import time
import hashlib

try:
    import PyPDF2  # type: ignore
    import fitz  # type: ignore  # PyMuPDF for better image extraction
    from langdetect import detect, DetectorFactory, LangDetectException  # type: ignore
    from PIL import Image  # type: ignore
    import io
    import numpy as np  # type: ignore
    from difflib import SequenceMatcher

    # GPU OCR imports (optional)
    try:
        import paddleocr  # type: ignore
        PADDLEOCR_AVAILABLE = True
    except ImportError:
        PADDLEOCR_AVAILABLE = False

    try:
        import easyocr  # type: ignore
        EASYOCR_AVAILABLE = True
    except ImportError:
        EASYOCR_AVAILABLE = False

    try:
        import pytesseract  # type: ignore
        TESSERACT_AVAILABLE = True
    except ImportError:
        TESSERACT_AVAILABLE = False

except ImportError as e:
    print(f"Missing required package: {e}")
    print("Install with: pip install PyPDF2 requests langdetect python-magic-bin markdownify beautifulsoup4 pymupdf Pillow")
    sys.exit(1)

# Set seed for consistent language detection
DetectorFactory.seed = 0

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """Configuration for the PDF processor."""
    chunk_size: int = 10000  # Larger chunks for 131k context window
    ollama_url: str = "http://************:11434"  # Second remote Ollama server (faster)
    model_name: str = "llama3.1:latest"  # Reliable model with 128k context, better instruction following
    vision_model_name: str = "qwen2.5vl:7b"  # Vision model for intelligent image analysis
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 2.0
    use_parallel_processing: bool = True
    max_workers: int = 2  # Lower for local processing
    timeout: int = 180  # Longer timeout for larger model
    min_image_size: int = 50  # Minimum image dimension to extract
    image_quality: int = 85  # JPEG quality for extracted images
    extract_tables: bool = True  # Whether to attempt table extraction

    # OCR Configuration
    use_gpu_ocr: bool = True  # Use GPU-accelerated OCR when available
    ocr_engine: str = "paddleocr"  # Options: "paddleocr", "easyocr", "tesseract"
    ocr_languages: Optional[List[str]] = None  # OCR languages, defaults to ['en']
    ocr_confidence_threshold: float = 0.8  # Minimum confidence for OCR text (increased for better quality)
    ocr_fallback_to_cpu: bool = True  # Fallback to CPU if GPU fails
    text_overlap_threshold: float = 0.8  # Similarity threshold to detect text overlap
    prefer_native_text: bool = True  # Prefer native PDF text over OCR when available
    extract_text_from_images: bool = True  # Extract text from images using OCR


class ImageInfo:
    """Container for image information."""

    def __init__(self, image_data: bytes, page_num: int, bbox: Tuple[float, float, float, float],
                 img_type: str = "png"):
        self.image_data = image_data
        self.page_num = page_num
        self.bbox = bbox  # (x0, y0, x1, y1)
        self.img_type = img_type
        self.hash = hashlib.md5(image_data).hexdigest()
        self.filename: Optional[str] = None
        self.ocr_text: Optional[str] = None  # OCR extracted text from image


class OllamaClient:
    """Client for Ollama local LLM API."""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama2"):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.generate_url = f"{self.base_url}/api/generate"
        
        # Test connection
        self._test_connection()
    
    def _test_connection(self):
        """Test connection to Ollama server."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = [model['name'] for model in response.json().get('models', [])]
                if self.model not in models:
                    logger.warning(f"Model {self.model} not found. Available models: {models}")
                    if models:
                        self.model = models[0]
                        logger.info(f"Using model: {self.model}")
                logger.info(f"Connected to Ollama server. Using model: {self.model}")
            else:
                raise ConnectionError(f"Ollama server returned status {response.status_code}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama server at {self.base_url}: {e}")
            raise ConnectionError("Ensure Ollama is running: 'ollama serve'")
    
    def call_llm(self, prompt: str, system_prompt: Optional[str] = None, **kwargs) -> str:
        """Make a call to Ollama with retry logic."""
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\nUser: {prompt}\n\nAssistant:"
        
        payload = {
            "model": self.model,
            "prompt": full_prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get('temperature', 0.1),
                "num_predict": kwargs.get('max_tokens', 1500)
            }
        }
        
        for attempt in range(kwargs.get('max_retries', 3)):
            try:
                response = requests.post(
                    self.generate_url,
                    json=payload,
                    timeout=kwargs.get('timeout', 60)
                )
                response.raise_for_status()

                result = response.json()
                return result.get('response', '').strip()

            except Exception as e:
                logger.warning(f"Ollama call failed (attempt {attempt + 1}): {e}")
                if attempt < kwargs.get('max_retries', 3) - 1:
                    time.sleep(kwargs.get('retry_delay', 2.0))
                    continue
                raise

        # This should never be reached due to the raise above, but added for mypy
        return ""


class VisionModel:
    """Ollama Vision Model client for intelligent image analysis."""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.base_url = config.ollama_url.rstrip('/')
        self.model = config.vision_model_name
        self.generate_url = f"{self.base_url}/api/generate"

    def analyze_image(self, image_path: str, ocr_text: str = "", page_num: int = 0) -> Dict[str, Any]:
        """Analyze image using vision model with OCR context."""
        try:
            # Convert image to base64
            with open(image_path, 'rb') as img_file:
                import base64
                image_data = base64.b64encode(img_file.read()).decode('utf-8')

            # Create comprehensive prompt
            system_prompt = """You are an expert technical document analyst. Analyze the provided image and determine if it contains useful technical content.

CRITICAL: Be very specific about identifying useless images that should be filtered out.

INSTRUCTIONS:
1. First determine if this image has any useful technical value
2. Identify if it's just: blank space, decorative elements, logos, headers/footers, page numbers, or minimal content
3. If it contains useful content: describe the technical elements, data, diagrams, charts, tables, etc.
4. Be explicit about content quality and usefulness

USELESS IMAGES TO IDENTIFY:
- Blank or mostly empty images
- Company logos, branding, emblems
- Decorative borders, frames, dividers
- Page headers/footers with just page numbers
- Watermarks or background patterns
- Images with illegible or tiny text
- Pure whitespace or minimal content

USEFUL IMAGES TO KEEP:
- Technical diagrams, schematics, flowcharts
- Data tables, charts, graphs
- Code examples, configuration samples
- Network architectures, protocol diagrams
- Measurement data, specifications
- Screenshots of interfaces or systems

Format your response as:
**Content Value:** [USEFUL/USELESS - be decisive]
**Image Type:** [specific type: diagram/table/logo/blank/etc.]
**Description:** [detailed analysis focusing on technical value]
**Key Elements:** [bullet points of important technical elements, or "None" if useless]
**Extracted Text:** [OCR text if meaningful, or "None/Minimal" if not useful]"""

            prompt = f"""Analyze this technical image from page {page_num + 1}.

OCR Text detected in image:
{ocr_text if ocr_text.strip() else "No text detected"}

Please provide a comprehensive technical analysis of this image."""

            payload = {
                "model": self.model,
                "prompt": f"{system_prompt}\n\nUser: {prompt}\n\nAssistant:",
                "images": [image_data],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 1000
                }
            }

            response = requests.post(self.generate_url, json=payload, timeout=self.config.timeout)
            response.raise_for_status()

            result = response.json()
            analysis = result.get('response', '').strip()

            return {
                "analysis": analysis,
                "ocr_text": ocr_text,
                "success": True
            }

        except Exception as e:
            logger.warning(f"Vision model analysis failed for {image_path}: {e}")
            return {
                "analysis": f"**Image Type:** Technical Image\n**Description:** Image from page {page_num + 1} (analysis unavailable)\n**Extracted Text:** {ocr_text if ocr_text.strip() else 'No text detected'}",
                "ocr_text": ocr_text,
                "success": False
            }


class GPUOCREngine:
    """GPU-accelerated OCR engine with multiple backend support."""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.engine: Any = None  # OCR engine instance
        self.engine_type = config.ocr_engine.lower()
        self.languages = config.ocr_languages or ['en']

        # Initialize the selected OCR engine
        self._initialize_engine()

    def _initialize_engine(self):
        """Initialize the OCR engine based on configuration."""
        try:
            if self.engine_type == "paddleocr" and PADDLEOCR_AVAILABLE:
                self._init_paddleocr()
            elif self.engine_type == "easyocr" and EASYOCR_AVAILABLE:
                self._init_easyocr()
            elif self.engine_type == "tesseract" and TESSERACT_AVAILABLE:
                self._init_tesseract()
            else:
                # Fallback to available engines
                if PADDLEOCR_AVAILABLE:
                    self.engine_type = "paddleocr"
                    self._init_paddleocr()
                elif EASYOCR_AVAILABLE:
                    self.engine_type = "easyocr"
                    self._init_easyocr()
                elif TESSERACT_AVAILABLE:
                    self.engine_type = "tesseract"
                    self._init_tesseract()
                else:
                    raise ImportError("No OCR engine available. Install paddleocr, easyocr, or pytesseract")

            logger.info(f"Initialized {self.engine_type} OCR engine with GPU support: {self.config.use_gpu_ocr}")

        except Exception as e:
            logger.error(f"Failed to initialize OCR engine: {e}")
            if self.config.ocr_fallback_to_cpu:
                logger.info("Falling back to CPU-based OCR")
                self.config.use_gpu_ocr = False
                self._initialize_engine()
            else:
                raise

    def _init_paddleocr(self):
        """Initialize PaddleOCR engine."""
        self.engine = paddleocr.PaddleOCR(
            use_gpu=self.config.use_gpu_ocr,
            lang='en',  # PaddleOCR uses different language codes
            show_log=False
        )

    def _init_easyocr(self):
        """Initialize EasyOCR engine."""
        self.engine = easyocr.Reader(
            self.languages,
            gpu=self.config.use_gpu_ocr
        )

    def _init_tesseract(self):
        """Initialize Tesseract engine."""
        # Tesseract doesn't have direct GPU support, but we can optimize it
        self.engine = "tesseract"
        # Set Tesseract configuration for better performance
        self.tesseract_config = '--oem 3 --psm 6'  # Use LSTM OCR Engine Mode with uniform text block

    def extract_text_from_image(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text from image using the configured OCR engine."""
        try:
            if self.engine_type == "paddleocr":
                return self._paddleocr_extract(image)
            elif self.engine_type == "easyocr":
                return self._easyocr_extract(image)
            elif self.engine_type == "tesseract":
                return self._tesseract_extract(image)
            else:
                return "", 0.0

        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            return "", 0.0

    def _paddleocr_extract(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text using PaddleOCR."""
        # Convert PIL image to numpy array
        img_array = np.array(image)

        results = self.engine.ocr(img_array, cls=True)

        if not results or not results[0]:
            return "", 0.0

        text_lines = []
        confidences = []

        for line in results[0]:
            if line and len(line) >= 2:
                text = line[1][0]  # Extract text
                confidence = line[1][1]  # Extract confidence

                if confidence >= self.config.ocr_confidence_threshold:
                    text_lines.append(text)
                    confidences.append(confidence)

        combined_text = '\n'.join(text_lines)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0

        return combined_text, avg_confidence

    def _easyocr_extract(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text using EasyOCR."""
        # Convert PIL image to numpy array
        img_array = np.array(image)

        results = self.engine.readtext(img_array)

        text_lines = []
        confidences = []

        for (bbox, text, confidence) in results:
            if confidence >= self.config.ocr_confidence_threshold:
                text_lines.append(text)
                confidences.append(confidence)

        combined_text = '\n'.join(text_lines)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0

        return combined_text, avg_confidence

    def _tesseract_extract(self, image: Image.Image) -> Tuple[str, float]:
        """Extract text using Tesseract."""
        text = pytesseract.image_to_string(image, config=self.tesseract_config)

        # Tesseract doesn't provide confidence per character easily, so we estimate
        # based on text quality heuristics
        confidence = self._estimate_text_quality(text)

        return text.strip(), confidence

    def _estimate_text_quality(self, text: str) -> float:
        """Estimate text quality for Tesseract results."""
        if not text.strip():
            return 0.0

        # Simple heuristics for text quality
        total_chars = len(text)
        alpha_chars = sum(1 for c in text if c.isalpha())
        space_chars = sum(1 for c in text if c.isspace())

        # Quality based on ratio of alphabetic characters
        alpha_ratio = alpha_chars / total_chars if total_chars > 0 else 0

        # Penalize excessive spaces or special characters
        if space_chars > total_chars * 0.5:
            alpha_ratio *= 0.5

        return min(alpha_ratio * 1.2, 1.0)  # Cap at 1.0


class TextOverlapDetector:
    """Detects and handles overlap between native PDF text and OCR text."""

    @staticmethod
    def calculate_similarity(text1: str, text2: str) -> float:
        """Calculate similarity between two text strings."""
        if not text1.strip() or not text2.strip():
            return 0.0

        # Normalize texts for comparison
        norm_text1 = TextOverlapDetector._normalize_text(text1)
        norm_text2 = TextOverlapDetector._normalize_text(text2)

        # Use SequenceMatcher for similarity calculation
        matcher = SequenceMatcher(None, norm_text1, norm_text2)
        return matcher.ratio()

    @staticmethod
    def _normalize_text(text: str) -> str:
        """Normalize text for comparison."""
        # Convert to lowercase, remove extra whitespace, and normalize line breaks
        normalized = re.sub(r'\s+', ' ', text.lower().strip())
        # Remove common OCR artifacts
        normalized = re.sub(r'[^\w\s]', '', normalized)
        return normalized

    @staticmethod
    def _evaluate_text_quality(text: str) -> float:
        """
        Evaluate text quality based on various metrics.
        Returns a score from 0.0 (poor) to 1.0 (excellent).
        """
        if not text or not text.strip():
            return 0.0

        # Split into lines and words for analysis
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        words = text.split()

        if len(words) == 0:
            return 0.0

        # Check for excessive single-word lines (major fragmentation indicator)
        single_word_lines = sum(1 for line in lines if len(line.split()) == 1)
        single_word_line_ratio = single_word_lines / max(len(lines), 1)

        # Check for very short words (fragmentation)
        short_words = sum(1 for word in words if len(word.strip()) <= 2)
        short_word_ratio = short_words / len(words)

        # Check average word length
        avg_word_length = sum(len(word.strip()) for word in words) / len(words)

        # Check for complete sentences (presence of punctuation and reasonable structure)
        sentence_endings = text.count('.') + text.count('!') + text.count('?')
        sentence_ratio = sentence_endings / max(len(words) / 15, 1)  # Expect ~1 sentence per 15 words

        # Check for proper capitalization patterns
        capitalized_words = sum(1 for word in words if word and word[0].isupper())
        capitalization_ratio = capitalized_words / len(words)

        # Calculate quality score starting from 1.0
        quality_score = 1.0

        # Major penalty for excessive single-word lines (fragmentation)
        if single_word_line_ratio > 0.5:
            quality_score -= 0.6  # Very fragmented
        elif single_word_line_ratio > 0.3:
            quality_score -= 0.4
        elif single_word_line_ratio > 0.15:
            quality_score -= 0.2

        # Penalty for high short word ratio
        if short_word_ratio > 0.5:
            quality_score -= 0.4
        elif short_word_ratio > 0.3:
            quality_score -= 0.2
        elif short_word_ratio > 0.15:
            quality_score -= 0.1

        # Penalty for very short average word length
        if avg_word_length < 2.5:
            quality_score -= 0.3
        elif avg_word_length < 3.5:
            quality_score -= 0.1

        # Penalty for poor sentence structure
        if sentence_ratio < 0.02:  # Very few sentence endings
            quality_score -= 0.3
        elif sentence_ratio < 0.05:
            quality_score -= 0.1

        # Penalty for unusual capitalization (too many or too few capitals)
        if capitalization_ratio > 0.8:  # Too many capitals (fragmented)
            quality_score -= 0.3
        elif capitalization_ratio < 0.05:  # Too few capitals
            quality_score -= 0.1

        return max(0.0, min(1.0, quality_score))

    @staticmethod
    def merge_texts(native_text: str, ocr_text: str, similarity_threshold: float = 0.8) -> Tuple[str, str]:
        """
        Merge native PDF text and OCR text, preferring the higher quality text.

        Returns:
            Tuple of (primary_text, supplementary_text)
        """
        if not native_text.strip():
            return ocr_text, ""

        if not ocr_text.strip():
            return native_text, ""

        similarity = TextOverlapDetector.calculate_similarity(native_text, ocr_text)

        # Evaluate text quality for both sources
        native_quality = TextOverlapDetector._evaluate_text_quality(native_text)
        ocr_quality = TextOverlapDetector._evaluate_text_quality(ocr_text)

        logger.debug(f"Text similarity: {similarity:.2f}, Native quality: {native_quality:.2f}, OCR quality: {ocr_quality:.2f}")

        if similarity >= similarity_threshold:
            # High similarity - prefer the higher quality text
            if ocr_quality > native_quality:
                logger.debug(f"High similarity - using OCR text (better quality)")
                return ocr_text, ""
            else:
                logger.debug(f"High similarity - using native text")
                return native_text, ""
        else:
            # Low similarity - choose the best quality text as primary
            if ocr_quality > native_quality + 0.2:  # OCR significantly better
                logger.debug(f"OCR text significantly better quality - using OCR as primary")
                return ocr_text, native_text if native_quality > 0.3 else ""
            elif native_quality > ocr_quality + 0.2:  # Native significantly better
                logger.debug(f"Native text significantly better quality - using native as primary")
                return native_text, ocr_text if ocr_quality > 0.3 else ""
            else:
                # Similar quality - prefer native text but keep both
                logger.debug(f"Similar quality texts - keeping both")
                return native_text, ocr_text


class EnhancedPDFProcessor:
    def __init__(self, input_dir: str, output_dir: Optional[str] = None, config: Optional[ProcessingConfig] = None):
        """
        Initialize the enhanced PDF processor with image extraction and OCR.

        Args:
            input_dir: Directory containing PDF files
            output_dir: Directory to save markdown files and images
            config: Processing configuration
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir) if output_dir else self.input_dir / "markdown_output"
        self.config = config or ProcessingConfig()

        # Create output directories
        self.output_dir.mkdir(exist_ok=True)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)

        # Initialize Ollama client
        self.llm = self._initialize_ollama()

        # Initialize Vision Model
        self.vision_model = self._initialize_vision_model()

        # Initialize OCR engine
        self.ocr_engine = self._initialize_ocr_engine()
        
    def _initialize_ollama(self) -> Optional[OllamaClient]:
        """Initialize Ollama client."""
        try:
            return OllamaClient(self.config.ollama_url, self.config.model_name)
        except Exception as e:
            logger.error(f"Failed to initialize Ollama client: {e}")
            logger.info("Falling back to basic processing without LLM assistance")
            return None

    def _initialize_vision_model(self) -> Optional[VisionModel]:
        """Initialize Vision Model for image analysis."""
        try:
            return VisionModel(self.config)
        except Exception as e:
            logger.error(f"Failed to initialize Vision Model: {e}")
            logger.info("Image analysis will use basic OCR only")
            return None

    def _initialize_ocr_engine(self) -> Optional[GPUOCREngine]:
        """Initialize OCR engine."""
        try:
            return GPUOCREngine(self.config)
        except Exception as e:
            logger.error(f"Failed to initialize OCR engine: {e}")
            logger.info("OCR functionality will be disabled")
            return None
    
    def extract_content_from_pdf(self, pdf_path: Path) -> Tuple[Optional[str], List[ImageInfo]]:
        """Extract both text and images from PDF using PyMuPDF with OCR integration."""
        text_content = []
        images = []

        try:
            # Open PDF with PyMuPDF for better image extraction
            pdf_document = fitz.open(str(pdf_path))  # type: ignore

            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]

                # Log progress every 50 pages
                if page_num % 50 == 0 or page_num == len(pdf_document) - 1:
                    logger.info(f"Processing page {page_num + 1}/{len(pdf_document)} ({((page_num + 1) / len(pdf_document) * 100):.1f}%)")

                # Extract native text with proper reading order
                native_text = self._extract_text_with_reading_order(page)

                # Extract OCR text if OCR engine is available
                ocr_text = ""
                if self.ocr_engine:
                    ocr_text = self._extract_ocr_text_from_page(page, page_num)

                # Merge texts avoiding overlap
                primary_text, supplementary_text = TextOverlapDetector.merge_texts(
                    native_text,
                    ocr_text,
                    self.config.text_overlap_threshold
                )

                # Log text quality decisions every 100 pages
                if page_num % 100 == 0:
                    native_quality = TextOverlapDetector._evaluate_text_quality(native_text)
                    ocr_quality = TextOverlapDetector._evaluate_text_quality(ocr_text)
                    logger.info(f"Page {page_num + 1} text quality - Native: {native_quality:.2f}, OCR: {ocr_quality:.2f}, Primary source: {'OCR' if len(ocr_text) > len(native_text) and primary_text == ocr_text else 'Native'}")

                # Combine texts for this page
                page_text_parts = []
                if primary_text.strip():
                    page_text_parts.append(primary_text)

                # Only add OCR supplementary text if it's high quality and adds substantial value
                if supplementary_text.strip():
                    is_valuable = self._is_ocr_supplementary_valuable(primary_text, supplementary_text)
                    if is_valuable:
                        page_text_parts.append(f"\n--- OCR Supplementary Text ---\n{supplementary_text}")
                        if page_num % 100 == 0:
                            logger.info(f"Page {page_num + 1}: Including supplementary text ({len(supplementary_text)} chars)")
                    else:
                        if page_num % 100 == 0:
                            logger.info(f"Page {page_num + 1}: Filtered out supplementary text ({len(supplementary_text)} chars) - quality too low")
                        logger.debug(f"Skipping low-quality supplementary text on page {page_num + 1}")

                if page_text_parts:
                    combined_page_text = '\n'.join(page_text_parts)
                    text_content.append(f"--- Page {page_num + 1} ---\n{combined_page_text}")

                # Extract images
                page_images = self._extract_images_from_page(page, page_num)
                images.extend(page_images)

                # Extract vector graphics and drawings as images
                if self.config.extract_tables:
                    drawings = self._extract_drawings_from_page(page, page_num, pdf_path.stem)
                    images.extend(drawings)

            pdf_document.close()

            combined_text = '\n\n'.join(text_content) if text_content else None
            logger.info(f"Extracted text from {len(text_content)} pages and {len(images)} images from {pdf_path.name}")

            # Log text extraction effectiveness
            if combined_text and len(text_content) > 0:
                avg_chars_per_page = len(combined_text) / len(text_content)
                logger.info(f"Text extraction stats: {len(combined_text):,} total characters, {avg_chars_per_page:.1f} avg chars/page")

            return combined_text, images
            
        except Exception as e:
            logger.error(f"Error reading PDF {pdf_path}: {e}")
            # Fallback to PyPDF2 for text only
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)  # type: ignore
                    for page_num, page in enumerate(pdf_reader.pages):
                        text = page.extract_text()
                        if text.strip():
                            text_content.append(f"--- Page {page_num + 1} ---\n{text}")
                
                return '\n\n'.join(text_content) if text_content else None, []
            except Exception as e2:
                logger.error(f"Fallback extraction also failed: {e2}")
                return None, []

    def _extract_ocr_text_from_page(self, page, page_num: int) -> str:
        """Extract text from a PDF page using OCR."""
        try:
            # Convert page to image for OCR
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better OCR quality
            pix = page.get_pixmap(matrix=mat)

            # Convert to PIL Image
            img_data = pix.tobytes("png")
            pil_image = Image.open(io.BytesIO(img_data))

            # Extract text using OCR
            if self.ocr_engine is not None:
                ocr_text, confidence = self.ocr_engine.extract_text_from_image(pil_image)
            else:
                return ""

            if confidence >= self.config.ocr_confidence_threshold:
                # Additional quality checks for OCR text
                if self._is_ocr_text_quality_good(ocr_text):
                    logger.debug(f"OCR extracted text from page {page_num + 1} with confidence {confidence:.2f}")
                    return ocr_text
                else:
                    logger.debug(f"OCR text quality too poor on page {page_num + 1}, skipping")
                    return ""
            else:
                logger.debug(f"OCR confidence {confidence:.2f} below threshold {self.config.ocr_confidence_threshold}")
                return ""

        except Exception as e:
            logger.warning(f"OCR extraction failed for page {page_num + 1}: {e}")
            return ""

    def _is_ocr_text_quality_good(self, ocr_text: str) -> bool:
        """Check if OCR text quality is good enough to use."""
        if not ocr_text or len(ocr_text.strip()) < 10:
            return False

        # Check for excessive fragmentation (too many single characters or very short words)
        words = ocr_text.split()
        if len(words) == 0:
            return False

        # Count very short words (1-2 characters)
        short_words = sum(1 for word in words if len(word.strip()) <= 2)
        short_word_ratio = short_words / len(words)

        # If more than 60% of words are very short, likely poor OCR quality
        if short_word_ratio > 0.6:
            return False

        # Check for reasonable average word length
        avg_word_length = sum(len(word.strip()) for word in words) / len(words)
        if avg_word_length < 2.5:  # Very short average word length indicates fragmentation
            return False

        # Check for reasonable character variety (not just repeated characters)
        unique_chars = len(set(ocr_text.lower().replace(' ', '')))
        if unique_chars < 5:  # Too few unique characters
            return False

        return True

    def _is_ocr_supplementary_valuable(self, primary_text: str, supplementary_text: str) -> bool:
        """Determine if supplementary text adds valuable information."""
        if not supplementary_text or not supplementary_text.strip():
            return False

        # Evaluate quality of supplementary text
        supplementary_quality = TextOverlapDetector._evaluate_text_quality(supplementary_text)
        primary_quality = TextOverlapDetector._evaluate_text_quality(primary_text)

        # Be very strict - if supplementary text quality is not excellent (< 0.7), don't use it
        if supplementary_quality < 0.7:
            return False

        # If primary text is good quality (> 0.6), be extremely strict about supplementary
        if primary_quality > 0.6:
            # Supplementary must be significantly better quality to be worth including
            if supplementary_quality < primary_quality + 0.2:
                return False

        # Check for content overlap - if any significant similarity, supplementary is redundant
        similarity = TextOverlapDetector.calculate_similarity(primary_text, supplementary_text)
        if similarity > 0.3:  # Even moderate similarity means supplementary is likely redundant
            return False

        # Check if supplementary contains unique technical information
        primary_word_count = len(primary_text.split())
        supplementary_word_count = len(supplementary_text.split())

        # If primary text is substantial (>30 words), supplementary must add exceptional value
        if primary_word_count > 30:
            # Supplementary must add substantial content (at least 30% of primary text length)
            if supplementary_word_count < primary_word_count * 0.3:
                return False

            # Check if supplementary contains unique technical information
            supplementary_words = set(supplementary_text.lower().split())
            primary_words = set(primary_text.lower().split())
            unique_supplementary_words = supplementary_words - primary_words

            # Supplementary must contribute at least 40% unique words to be valuable
            if len(unique_supplementary_words) < len(supplementary_words) * 0.4:
                return False

            # Additional check: supplementary must not be mostly fragmented
            supplementary_lines = [line.strip() for line in supplementary_text.split('\n') if line.strip()]
            single_word_lines = sum(1 for line in supplementary_lines if len(line.split()) == 1)
            if len(supplementary_lines) > 0 and (single_word_lines / len(supplementary_lines)) > 0.3:
                return False  # Too fragmented

        return True

    def _extract_text_with_reading_order(self, page) -> str:
        """
        Extract text from PDF page with proper reading order for multi-column layouts.
        """
        try:
            logger.debug("Extracting text with reading order analysis...")
            # Get text blocks with position information
            text_dict = page.get_text("dict")

            # Extract text blocks with their bounding boxes
            text_blocks = []
            for block in text_dict["blocks"]:
                if "lines" in block:  # Text block
                    block_text = ""
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]
                        if line_text.strip():
                            block_text += line_text + "\n"

                    if block_text.strip():
                        # Get bounding box
                        bbox = block["bbox"]  # (x0, y0, x1, y1)
                        text_blocks.append({
                            "text": block_text.strip(),
                            "bbox": bbox,
                            "x0": bbox[0],
                            "y0": bbox[1],
                            "x1": bbox[2],
                            "y1": bbox[3]
                        })

            if not text_blocks:
                logger.debug("No text blocks found, falling back to simple extraction")
                return page.get_text()

            logger.debug(f"Found {len(text_blocks)} text blocks, sorting for reading order...")
            # Sort blocks for proper reading order
            # First by vertical position (top to bottom), then by horizontal position (left to right)
            text_blocks.sort(key=lambda block: (round(block["y0"] / 20) * 20, block["x0"]))

            # Group blocks by approximate vertical position (same row)
            rows = []
            current_row = []
            current_y = None
            tolerance = 15  # Vertical tolerance for grouping blocks in same row

            for block in text_blocks:
                if current_y is None or abs(block["y0"] - current_y) <= tolerance:
                    current_row.append(block)
                    current_y = block["y0"] if current_y is None else current_y
                else:
                    if current_row:
                        # Sort current row by horizontal position
                        current_row.sort(key=lambda b: b["x0"])
                        rows.append(current_row)
                    current_row = [block]
                    current_y = block["y0"]

            # Add the last row
            if current_row:
                current_row.sort(key=lambda b: b["x0"])
                rows.append(current_row)

            logger.debug(f"Organized text into {len(rows)} rows for proper reading order")

            # Combine text from all rows
            combined_text = []
            for row in rows:
                row_text = " ".join(block["text"] for block in row)
                combined_text.append(row_text)

            result = "\n".join(combined_text)

            # Clean up excessive whitespace
            result = re.sub(r'\n\s*\n\s*\n', '\n\n', result)  # Reduce multiple newlines
            result = re.sub(r'[ \t]+', ' ', result)  # Normalize spaces

            return result

        except Exception as e:
            logger.warning(f"Error in reading order extraction: {e}, falling back to simple extraction")
            return page.get_text()

    def _extract_ocr_from_image(self, image_data: bytes) -> Optional[str]:
        """Extract text from image data using OCR."""
        if not self.ocr_engine:
            return None

        try:
            # Convert image bytes to PIL Image
            pil_image = Image.open(io.BytesIO(image_data))

            # Skip very small images (likely decorative)
            width, height = pil_image.size
            if width < 50 or height < 20:
                return None

            # Extract text using OCR
            ocr_text, confidence = self.ocr_engine.extract_text_from_image(pil_image)

            # Only return text if confidence is above threshold
            if confidence >= self.config.ocr_confidence_threshold and ocr_text.strip():
                # Clean and format the OCR text
                cleaned_text = self._clean_ocr_text(ocr_text)
                if len(cleaned_text.strip()) > 3:  # Minimum meaningful text length
                    return cleaned_text

        except Exception as e:
            logger.debug(f"Failed to extract OCR from image: {e}")

        return None

    def _clean_ocr_text(self, text: str) -> str:
        """Clean and format OCR extracted text."""
        if not text:
            return ""

        # Remove copyright boilerplate first
        text = self._remove_copyright_boilerplate(text)

        # Remove excessive whitespace
        text = ' '.join(text.split())

        # Remove common OCR artifacts
        text = text.replace('|', 'I')  # Common OCR mistake
        text = text.replace('0', 'O')  # In text contexts

        # Fix common spacing issues
        text = text.replace(' ,', ',')
        text = text.replace(' .', '.')
        text = text.replace(' ;', ';')
        text = text.replace(' :', ':')

        return text.strip()

    def _remove_copyright_boilerplate(self, text: str) -> str:
        """Remove copyright boilerplate and legal notices from text."""
        if not text:
            return ""

        import re

        # Very specific copyright patterns to remove (conservative approach)
        copyright_patterns = [
            # Remove specific customer information lines
            r'Customer:\s*Phil Young\s*-\s*No\.\s*of\s*User\(s\):\s*\d+\s*-\s*Company:\s*TriangleMicroworks.*?(?=\n|$)',
            r'^Customer:\s*Phil Young\s*-\s*No\.\s*of\s*User\(s\):\s*\d+\s*-\s*Company:\s*TriangleMicroworks.*?$',

            # Only remove complete copyright blocks, not partial matches
            r'IMPORTANT:\s*This\s*file\s*is\s*copyright\s*of\s*IEC,\s*Geneva,\s*Switzerland\..*?custserv@iec\.ch',
            r'This\s*file\s*is\s*subject\s*to\s*a\s*licence\s*agreement\..*?custserv@iec\.ch',

            # Only remove standalone order information
            r'^Order\s*No\.:\s*[\w\-\d]+\s*$',

            # Only remove standalone contact lines
            r'^Enquiries\s*to\s*Email:\s*[\w@\.]+\s*-\s*Tel\.:\s*[\+\d\s]+\s*$',

            # Only remove standalone page footers (not if part of larger content)
            r'^\s*–\s*\d+\s*–\s*$',
            r'^\s*IEC\s*\d+\-\d+\s*–\s*\d+\s*–\s*$',
        ]

        # Apply all patterns
        cleaned_text = text
        for pattern in copyright_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE | re.MULTILINE | re.DOTALL)

        # Remove empty lines and excessive whitespace left by removals
        lines = [line.strip() for line in cleaned_text.split('\n')]
        lines = [line for line in lines if line and not self._is_boilerplate_line(line)]

        return '\n'.join(lines)

    def _is_boilerplate_line(self, line: str) -> bool:
        """Check if a line is likely boilerplate text that should be removed."""
        if not line or len(line.strip()) < 3:
            return True

        line_lower = line.lower().strip()

        # Very specific boilerplate indicators (conservative approach)
        boilerplate_indicators = [
            '<EMAIL>',
            'trianglemicroworks',
            'phil young',
            'customer: phil young',  # Add this specific pattern
            'important: this file is copyright of iec',  # More specific
            'enquiries to email:',  # More specific with colon
            'order no.:',  # More specific with colon
            'all rights reserved. no part of this publication',  # More specific combination
        ]

        # Check if line contains boilerplate indicators
        for indicator in boilerplate_indicators:
            if indicator in line_lower:
                return True

        # Check for patterns like "– 12 –" or "IEC 61850-4 – 14 –"
        if re.match(r'^\s*[–\-]\s*\d+\s*[–\-]\s*$', line) or re.match(r'^\s*IEC\s*\d+\-\d+.*?[–\-]\s*\d+\s*[–\-]\s*$', line):
            return True

        return False

    def _translate_to_english(self, text: str) -> str:
        """Translate non-English text to English using LLM."""
        if not self.llm or not text.strip():
            return text

        # Quick check if text is likely already English
        english_indicators = ['the', 'and', 'of', 'to', 'in', 'for', 'with', 'shall', 'should', 'may', 'can', 'will']
        words = text.lower().split()
        english_word_count = sum(1 for word in words if word in english_indicators)

        # If more than 20% of words are common English words, assume it's already English
        if len(words) > 0 and (english_word_count / len(words)) > 0.2:
            return text

        # Check for non-English patterns that need translation (more conservative)
        non_english_patterns = [
            r'[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿäöüßÄÖÜ]{3,}',  # Multiple accented characters in sequence
            r'\b(dans|pour|avec|sur|sous|par|sans|vers|chez|depuis|pendant|avant|après|entre|parmi|selon|contre|malgré|sauf|hormis|excepté|outre|moyennant|nonobstant|touchant|concernant|suivant|durant|lors)\b',  # French
            r'\b(durch|über|unter|zwischen|während|wegen|trotz|statt|anstatt|außer|binnen|dank|kraft|laut|mangels|mittels|namens|seitens|vermöge|zufolge|obwohl|nachdem|bevor|seit)\b',  # German
            r'\b(porque|cuando|donde|quien|cual|cuyo|este|esta|estos|estas|aquel|aquella|aquellos|aquellas|sino)\b',  # Spanish
            r'\b(però|perché|quando|dove|questo|questa|questi|queste|quello|quella|quelli|quelle)\b'  # Italian
        ]

        has_non_english = any(re.search(pattern, text, re.IGNORECASE) for pattern in non_english_patterns)

        if not has_non_english:
            return text

        try:
            system_prompt = """You are a professional translator. Translate the given text to English while preserving all technical terms, standards references, and document structure.

INSTRUCTIONS:
1. Translate non-English text to clear, professional English
2. PRESERVE all technical terms, model numbers, standards (like IEC 61850)
3. PRESERVE all proper nouns, company names, and technical specifications
4. PRESERVE document formatting and structure
5. If text is already in English, return it unchanged
6. Focus on accuracy and technical precision

Respond with only the translated text, no explanations."""

            prompt = f"""Translate this text to English:

{text}"""

            response = self.llm.call_llm(
                prompt,
                system_prompt,
                temperature=0.1,
                max_retries=2,
                retry_delay=1.0,
                timeout=30
            )

            return response.strip() if response.strip() else text

        except Exception as e:
            logger.warning(f"Translation failed: {e}")
            return text

    def _verify_english_content(self, text: str) -> Dict[str, Any]:
        """Verify that text is primarily English and log filtering results."""
        if not text:
            return {"is_english": True, "confidence": 0.0, "issues": []}

        import re

        issues = []

        # Check for non-English patterns (more conservative to avoid false positives)
        non_english_patterns = [
            (r'[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]', 'French/Spanish/Portuguese accented characters'),
            (r'[äöüßÄÖÜ]', 'German characters'),
            (r'[àèéìòù]', 'Italian accented characters'),
            # More specific patterns that are less likely to match English technical terms
            (r'\b(dans|pour|avec|sur|sous|par|sans|vers|chez|depuis|pendant|avant|après|entre|parmi|selon|contre)\b', 'French articles/prepositions'),
            (r'\b(durch|über|unter|zwischen|während|wegen|trotz|statt|anstatt|außer|binnen|dank|kraft|laut|mangels|mittels|namens|seitens|vermöge|zufolge)\b', 'German articles/prepositions'),
            (r'\b(porque|cuando|donde|quien|cual|cuyo|este|esta|estos|estas|aquel|aquella|aquellos|aquellas)\b', 'Spanish articles/prepositions'),
            (r'\b(perché|quando|dove|questo|questa|questi|queste|quello|quella|quelli|quelle)\b', 'Italian articles/prepositions'),
        ]

        for pattern, description in non_english_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                issues.append(f"{description}: {len(matches)} occurrences")

        # Check for copyright boilerplate that should have been removed
        copyright_indicators = [
            'phil young',
            'trianglemicroworks',
            'order no.',
            '<EMAIL>',
            'all rights reserved',
            'copyright of iec',
            'licence agreement',
            'geneva, switzerland'
        ]

        for indicator in copyright_indicators:
            if indicator.lower() in text.lower():
                issues.append(f"Copyright boilerplate found: '{indicator}'")

        # Calculate confidence (higher is better)
        confidence = max(0.0, 1.0 - (len(issues) * 0.1))
        is_english = len(issues) == 0

        if issues:
            logger.warning(f"Text filtering issues detected: {issues}")

        return {
            "is_english": is_english,
            "confidence": confidence,
            "issues": issues,
            "text_length": len(text),
            "issues_count": len(issues)
        }

    def _text_overlaps_with_existing(self, ocr_text: str, existing_text: str, threshold: float = 0.7) -> bool:
        """Check if OCR text significantly overlaps with existing text to avoid duplication."""
        if not ocr_text.strip() or not existing_text.strip():
            return False

        # Clean both texts for comparison
        ocr_clean = ' '.join(ocr_text.lower().split())
        existing_clean = ' '.join(existing_text.lower().split())

        # If OCR text is very short, be more lenient
        if len(ocr_clean) < 20:
            threshold = 0.9

        # Check for substring matches (OCR text in existing text)
        if ocr_clean in existing_clean:
            return True

        # Use sequence matcher for similarity
        from difflib import SequenceMatcher
        similarity = SequenceMatcher(None, ocr_clean, existing_clean).ratio()

        return similarity > threshold

    def _should_skip_image(self, analysis: str, ocr_text: str) -> bool:
        """Determine if an image should be skipped (logos, decorative elements, empty content)."""
        analysis_lower = analysis.lower()
        ocr_lower = (ocr_text or "").lower()

        # Skip images with no useful content
        useless_content_indicators = [
            # Empty or minimal content
            "blank", "empty", "white space", "whitespace", "nothing", "no content",
            "no text", "no information", "minimal content", "sparse content",

            # Decorative elements
            "decorative", "ornamental", "border", "frame", "divider", "separator",
            "header", "footer", "watermark", "background pattern",

            # Logos and branding
            "logo", "branding", "emblem", "icon", "trademark", "copyright symbol",
            "international usersgroup", "company logo", "organizational logo",
            "letters \"c\", \"u\", and \"a\"", "diamond shape containing the letters",
            "circular element with a grid pattern", "blue and white color scheme",
            "stylized design", "brand mark",

            # Page elements with no technical value
            "page number", "page header", "page footer", "running header",
            "chapter heading only", "section divider", "line break",

            # Very small or unclear content
            "tiny text", "illegible", "blurry", "unclear", "pixelated",
            "low quality", "poor resolution", "unreadable"
        ]

        # Skip if OCR text is too short (likely not useful)
        if ocr_text and len(ocr_text.strip()) < 10:
            return True

        # Check if vision model explicitly marked it as useless
        if "content value:** useless" in analysis_lower or "content value: useless" in analysis_lower:
            return True

        # Skip if analysis indicates useless content
        useless_score = sum(1 for indicator in useless_content_indicators if indicator in analysis_lower)

        # Skip if multiple indicators suggest useless content
        if useless_score >= 2:
            return True

        # Skip if it's clearly just a logo (single strong indicator)
        strong_logo_indicators = [
            "logo", "branding", "emblem", "company logo", "organizational logo",
            "international usersgroup", "trademark", "brand mark"
        ]

        if any(indicator in analysis_lower for indicator in strong_logo_indicators):
            return True

        # Keep images that likely contain useful technical content
        useful_content_indicators = [
            "diagram", "chart", "graph", "table", "figure", "schematic",
            "flowchart", "technical drawing", "circuit", "wiring", "network",
            "architecture", "protocol", "specification", "standard",
            "measurement", "data", "values", "parameters", "configuration",
            "code", "syntax", "example", "illustration", "screenshot"
        ]

        # If it has useful technical content, definitely keep it
        if any(indicator in analysis_lower for indicator in useful_content_indicators):
            return False

        # If OCR contains substantial technical text, keep it
        if ocr_text and len(ocr_text.strip()) > 50:
            technical_terms = [
                "protocol", "standard", "specification", "configuration",
                "parameter", "value", "data", "network", "system",
                "interface", "communication", "message", "frame",
                "iec", "ieee", "iso", "tcp", "udp", "ethernet"
            ]
            if any(term in ocr_lower for term in technical_terms):
                return False

        # Default: if we're not sure, skip it (be conservative about storage)
        return True

    def _extract_images_from_page(self, page, page_num: int) -> List[ImageInfo]:
        """Extract images from a PDF page."""
        images = []

        try:
            image_list = page.get_images(full=True)

            for img_index, img in enumerate(image_list):
                try:
                    # Get image reference
                    xref = img[0]

                    # Extract image data using the document reference
                    try:
                        base_image = page.parent.extract_image(xref)
                        image_bytes = base_image["image"]
                        image_ext = base_image["ext"]
                    except Exception as e:
                        logger.debug(f"Failed to extract image {img_index} from page {page_num}: {e}")
                        continue

                    # Filter small images (likely decorative elements)
                    try:
                        pil_image = Image.open(io.BytesIO(image_bytes))
                        width, height = pil_image.size

                        if width < self.config.min_image_size or height < self.config.min_image_size:
                            continue

                    except Exception:
                        continue

                    # Get image position (use image dimensions as fallback)
                    try:
                        # Try to get image bbox from the image list
                        if len(img) >= 7:
                            img_bbox = (img[1], img[2], img[3], img[4])  # x0, y0, x1, y1
                        else:
                            img_bbox = (0, 0, width, height)
                    except Exception:
                        img_bbox = (0, 0, width, height)

                    image_info = ImageInfo(
                        image_data=image_bytes,
                        page_num=page_num,
                        bbox=img_bbox,
                        img_type=image_ext
                    )

                    images.append(image_info)
                    logger.debug(f"Successfully extracted image {img_index} from page {page_num}")

                except Exception as e:
                    logger.debug(f"Failed to extract image {img_index} from page {page_num}: {e}")
                    continue

        except Exception as e:
            logger.warning(f"Failed to get image list from page {page_num}: {e}")

        return images
    
    def _extract_drawings_from_page(self, page, page_num: int, doc_name: str) -> List[ImageInfo]:
        """Extract vector drawings, diagrams, and tables as images."""
        drawings = []
        
        try:
            # Get page as image for vector content
            mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            pil_image = Image.open(io.BytesIO(img_data))
            
            # Check if page contains significant non-text content
            # This is a heuristic - pages with drawings typically have different characteristics
            drawings_detected = self._detect_drawings_in_image(pil_image, page)
            
            if drawings_detected:
                # Crop to content area
                cropped_image = self._crop_to_content(pil_image, page)
                
                if cropped_image:
                    # Convert back to bytes
                    img_buffer = io.BytesIO()
                    cropped_image.save(img_buffer, format='PNG', quality=self.config.image_quality)
                    img_bytes = img_buffer.getvalue()
                    
                    drawing_info = ImageInfo(
                        image_data=img_bytes,
                        page_num=page_num,
                        bbox=(0, 0, page.rect.width, page.rect.height),
                        img_type="png"
                    )
                    
                    drawings.append(drawing_info)
            
        except Exception as e:
            logger.warning(f"Failed to extract drawings from page {page_num}: {e}")
        
        return drawings
    
    def _detect_drawings_in_image(self, image: Image.Image, page) -> bool:
        """Detect if a page contains significant drawings or diagrams."""
        try:
            # Get page text coverage
            text_blocks = page.get_text("dict")
            total_text_area = 0
            page_area = page.rect.width * page.rect.height

            for block in text_blocks.get("blocks", []):
                if "lines" in block:
                    bbox = block["bbox"]
                    block_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                    total_text_area += block_area

            text_coverage = total_text_area / page_area if page_area > 0 else 0

            # If text coverage is low, likely contains diagrams
            # Also check image dimensions as a factor
            img_width, img_height = image.size
            has_significant_size = img_width > 100 and img_height > 100

            return text_coverage < 0.3 and has_significant_size

        except Exception:
            return False
    
    def _crop_to_content(self, image: Image.Image, page) -> Optional[Image.Image]:
        """Crop image to remove excessive whitespace."""
        try:
            # Simple implementation - can be enhanced with edge detection
            bbox = image.getbbox()
            if bbox:
                return image.crop(bbox)
            return image
        except Exception:
            return image
    
    def save_images(self, images: List[ImageInfo], doc_name: str) -> Dict[str, str]:
        """Save extracted images and return mapping of positions to filenames, filtering out logos."""
        image_references = {}
        saved_count = 0

        for i, img_info in enumerate(images):
            try:
                # Create unique filename
                filename = f"{doc_name}_page{img_info.page_num + 1}_img{i + 1}_{img_info.hash[:8]}.{img_info.img_type}"
                filepath = self.images_dir / filename

                # Save image temporarily for analysis
                with open(filepath, 'wb') as f:
                    f.write(img_info.image_data)

                # Extract OCR text from image if enabled
                if self.config.extract_text_from_images and self.config.use_gpu_ocr and self.ocr_engine:
                    img_info.ocr_text = self._extract_ocr_from_image(img_info.image_data)
                    logger.debug(f"Extracted OCR text from image {filename}: {len(img_info.ocr_text) if img_info.ocr_text else 0} characters")

                # Check if image should be filtered out using vision model
                should_skip = False
                if self.vision_model:
                    try:
                        analysis_result = self.vision_model.analyze_image(
                            str(filepath),
                            img_info.ocr_text or "",
                            img_info.page_num
                        )

                        if analysis_result["success"]:
                            should_skip = self._should_skip_image(analysis_result["analysis"], img_info.ocr_text or "")

                    except Exception as e:
                        logger.debug(f"Vision analysis failed for {filename}: {e}")

                if should_skip:
                    # Remove the temporarily saved image
                    try:
                        filepath.unlink()
                        logger.debug(f"Filtered out logo image: {filename}")
                    except Exception as e:
                        logger.warning(f"Failed to remove filtered image {filename}: {e}")
                    continue  # Skip this image entirely

                # Keep the image - set filename and create reference
                img_info.filename = filename
                saved_count += 1

                # Create reference key based on page and position
                ref_key = f"page_{img_info.page_num + 1}_{saved_count}"
                image_references[ref_key] = filename

                logger.debug(f"Saved image: {filename}")

            except Exception as e:
                logger.warning(f"Failed to save image {i}: {e}")

        filtered_count = len(images) - saved_count
        filter_percentage = (filtered_count / len(images) * 100) if len(images) > 0 else 0
        logger.info(f"Image filtering results: Saved {saved_count} images, filtered out {filtered_count} useless images ({filter_percentage:.1f}% reduction)")
        return image_references
    
    def llm_filter_english_content(self, text_chunk: str) -> Dict[str, Any]:
        """Use Ollama to filter and identify English content."""
        if not self.llm:
            # Apply basic copyright removal even without LLM
            cleaned_text = self._remove_copyright_boilerplate(text_chunk)
            return {"english_text": cleaned_text, "confidence": 0.5}

        # Pre-clean the text to remove obvious copyright boilerplate
        pre_cleaned = self._remove_copyright_boilerplate(text_chunk)

        system_prompt = """You are an extremely conservative text processing assistant. Your ONLY job is to remove obvious copyright boilerplate while preserving ALL technical content.

ABSOLUTE RULES:
1. PRESERVE EVERYTHING by default - assume all content is valuable
2. PRESERVE ALL technical content, specifications, standards, procedures, definitions
3. PRESERVE ALL section numbers, headings, figure references, table references, page numbers
4. PRESERVE ALL document structure, formatting, and organization
5. PRESERVE ALL measurements, values, technical terms, and engineering information
6. PRESERVE introductory text, scope statements, and document descriptions
7. PRESERVE table of contents, appendices, and reference sections
8. PRESERVE author names, contributor lists, and acknowledgments
9. PRESERVE revision history and change information

REMOVE ONLY these EXACT patterns:
- Complete lines that say "Customer: Phil Young - No. of User(s): [number] - Company: TriangleMicroworks"
- Complete lines that say "<EMAIL>"
- Complete lines that say "Order No.: [number]"
- Standalone lines that are just "– [number] –"

CRITICAL: If you see ANY technical content, standards references, document structure, or meaningful information mixed with potential boilerplate, KEEP EVERYTHING. Only remove lines that are 100% pure boilerplate with zero technical value.

When in doubt, ALWAYS preserve the content."""

        prompt = f"""Please review this text and remove ONLY obvious copyright boilerplate while preserving ALL technical content. Be very conservative - when in doubt, keep the content:

{pre_cleaned}"""

        try:
            response = self.llm.call_llm(
                prompt,
                system_prompt,
                temperature=0.1,  # Lower temperature for more consistent filtering
                max_retries=self.config.max_retries,
                retry_delay=self.config.retry_delay,
                timeout=self.config.timeout
            )

            # Additional post-processing to ensure copyright removal
            final_text = self._remove_copyright_boilerplate(response.strip())

            # Safety check: if LLM removed too much content, fall back to basic filtering
            original_length = len(pre_cleaned.strip())
            final_length = len(final_text.strip())

            # If we lost more than 80% of content, use basic filtering instead (more lenient)
            if original_length > 100 and final_length < (original_length * 0.2):
                logger.warning(f"LLM filtering too aggressive ({final_length}/{original_length} chars), using basic filtering")
                fallback_text = self._basic_english_filter(text_chunk)
                # Apply translation to fallback text as well
                translated_fallback = self._translate_to_english(fallback_text)
                return {
                    "english_text": translated_fallback,
                    "confidence": 0.6
                }

            # Apply translation to any remaining non-English content
            translated_text = self._translate_to_english(final_text)

            return {
                "english_text": translated_text,
                "confidence": 0.9 if translated_text.strip() else 0.1
            }

        except Exception as e:
            logger.error(f"Ollama filtering failed: {e}")
            # Fallback to basic copyright removal
            fallback_text = self._remove_copyright_boilerplate(text_chunk)
            return {"english_text": fallback_text, "confidence": 0.3}
    
    def create_markdown_with_images(self, text: str, images: List[ImageInfo],
                                   image_references: Dict[str, str], title: str) -> str:
        """Create markdown content with embedded images."""
        if not text:
            markdown_content = f"# {title}\n\nNo text content extracted.\n\n"
        else:
            # First create basic markdown structure
            if self.llm:
                markdown_content = self.llm_structure_content(text, title)
            else:
                markdown_content = self.basic_text_to_markdown(text, title)

        # Add images section at the end if we have images
        if images:
            markdown_content += "\n\n---\n\n# Images and Diagrams\n\n"

            # Group images by page for better organization
            pages_with_images = {}
            for img in images:
                page_num = img.page_num + 1  # Convert to 1-based page numbering
                if page_num not in pages_with_images:
                    pages_with_images[page_num] = []
                pages_with_images[page_num].append(img)

            # Add images organized by page
            for page_num in sorted(pages_with_images.keys()):
                page_images = pages_with_images[page_num]
                markdown_content += f"## Page {page_num}\n\n"

                # Filter and process images, skipping logos entirely
                valid_images = []
                for j, img in enumerate(page_images):
                    if img.filename:
                        # Use vision model for intelligent image analysis
                        if self.vision_model:
                            try:
                                image_path = self.images_dir / img.filename
                                analysis_result = self.vision_model.analyze_image(
                                    str(image_path),
                                    img.ocr_text or "",
                                    img.page_num
                                )

                                if analysis_result["success"]:
                                    # Filter out repetitive logos and branding images - skip completely
                                    if self._should_skip_image(analysis_result["analysis"], img.ocr_text):
                                        continue  # Skip this image entirely
                                    else:
                                        valid_images.append((img, analysis_result["analysis"]))
                                else:
                                    # Fallback to basic OCR display
                                    ocr_content = ""
                                    if img.ocr_text and img.ocr_text.strip():
                                        if not self._text_overlaps_with_existing(img.ocr_text, text):
                                            ocr_content = f"**Text extracted from image:** {img.ocr_text.strip()}"
                                        else:
                                            ocr_content = "*[Text from this image is already included in the main content]*"
                                    valid_images.append((img, ocr_content))

                            except Exception as e:
                                logger.warning(f"Vision analysis failed for {img.filename}: {e}")
                                # Fallback to basic OCR display
                                ocr_content = ""
                                if img.ocr_text and img.ocr_text.strip():
                                    if not self._text_overlaps_with_existing(img.ocr_text, text):
                                        ocr_content = f"**Text extracted from image:** {img.ocr_text.strip()}"
                                    else:
                                        ocr_content = "*[Text from this image is already included in the main content]*"
                                valid_images.append((img, ocr_content))
                        else:
                            # No vision model available - use basic OCR display
                            ocr_content = ""
                            if img.ocr_text and img.ocr_text.strip():
                                if not self._text_overlaps_with_existing(img.ocr_text, text):
                                    ocr_content = f"**Text extracted from image:** {img.ocr_text.strip()}"
                                else:
                                    ocr_content = "*[Text from this image is already included in the main content]*"
                            valid_images.append((img, ocr_content))

                # Add valid images to markdown
                for j, (img, analysis) in enumerate(valid_images):
                    img_alt = f"Image {j+1} from page {page_num}"
                    markdown_content += f"![{img_alt}](images/{img.filename})\n\n"
                    if analysis:
                        markdown_content += f"{analysis}\n\n"

                markdown_content += "---\n\n"

        return markdown_content
    
    def llm_structure_content(self, text: str, title: str) -> str:
        """Use Ollama to structure content into proper markdown."""
        if not self.llm:
            return self.basic_text_to_markdown(text, title)
        
        system_prompt = """You are a technical document formatter. Convert text to well-structured markdown:

Rules:
1. Use the provided title as the main heading (# Title)
2. Create logical section headings (## ###) based on content
3. Format lists and technical content properly
4. Preserve important numbering and technical details
5. Keep references to figures and tables (e.g., "see Figure 1", "Table 2 shows")
6. Remove excessive whitespace
7. Preserve page structure indicators for image placement

Output only the formatted markdown, no explanations."""

        # Limit text for processing
        text_sample = text[:4000] if len(text) > 4000 else text
        
        prompt = f"""Format this content as markdown with title "{title}":

{text_sample}"""

        try:
            response = self.llm.call_llm(
                prompt,
                system_prompt,
                temperature=self.config.temperature,
                max_tokens=2500,
                timeout=self.config.timeout
            )
            
            # Ensure title is present
            if not response.startswith('#'):
                response = f"# {title}\n\n{response}"
            
            # Add remaining text if it was truncated
            if len(text) > 4000:
                remaining_text = text[4000:]
                response += f"\n\n## Additional Content\n\n{remaining_text}"
            
            return response
            
        except Exception as e:
            logger.error(f"Ollama structuring failed: {e}")
            return self.basic_text_to_markdown(text, title)
    
    def basic_text_to_markdown(self, text: str, title: str) -> str:
        """Fallback method for basic markdown conversion."""
        if not text:
            return f"# {title}\n\nNo content extracted.\n"
        
        markdown_content = [f"# {title}\n"]
        paragraphs = text.split('\n\n')
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph or paragraph.startswith('---'):
                continue
            
            # Simple heading detection
            if (len(paragraph) < 100 and 
                (paragraph.isupper() or paragraph.istitle() or 
                 re.match(r'^\d+\.?\s+[A-Z]', paragraph))):
                level = 2 if re.match(r'^\d+\.?\s+', paragraph) else 3
                heading = '#' * level + ' ' + paragraph
                markdown_content.append(heading)
            else:
                markdown_content.append(paragraph)
        
        return '\n\n'.join(markdown_content)
    
    def process_pdf(self, pdf_path: Path) -> bool:
        """Process a single PDF file with image extraction."""
        logger.info(f"Processing: {pdf_path.name}")
        
        # Extract text and images
        text, images = self.extract_content_from_pdf(pdf_path)
        if not text and not images:
            logger.warning(f"No content extracted from {pdf_path.name}")
            return False
        
        logger.info(f"Extracted {len(text) if text else 0} characters and {len(images)} images from {pdf_path.name}")
        
        # Process text with LLM if available
        english_text = ""
        if text:
            try:
                logger.info(f"Original text length: {len(text)} characters")

                if self.llm:
                    # Process text in larger chunks to take advantage of 131k context window
                    chunk_size = 50000  # Much larger chunks for phi4-reasoning
                    processed_chunks = []

                    total_chunks = len(text)//chunk_size + 1
                    logger.info(f"Processing text in {total_chunks} chunks of {chunk_size:,} characters each")

                    for i in range(0, len(text), chunk_size):
                        chunk = text[i:i + chunk_size]
                        chunk_num = i//chunk_size + 1

                        progress_percent = (chunk_num / total_chunks * 100) if total_chunks > 0 else 0
                        logger.info(f"Processing chunk {chunk_num}/{total_chunks} ({len(chunk):,} characters) - {progress_percent:.1f}% complete")

                        result = self.llm_filter_english_content(chunk)
                        filtered_chunk = result["english_text"]

                        if filtered_chunk.strip():
                            # Verify the filtering quality
                            verification = self._verify_english_content(filtered_chunk)
                            if verification["issues"]:
                                logger.warning(f"Chunk {chunk_num} filtering issues: {verification['issues']}")
                            else:
                                logger.info(f"Chunk {chunk_num} processed successfully - no issues detected")

                            processed_chunks.append(filtered_chunk)
                        else:
                            logger.info(f"Chunk {chunk_num} was completely filtered out (likely all boilerplate)")

                    english_text = "\n\n".join(processed_chunks)
                else:
                    english_text = self._basic_english_filter(text)

                # Final verification of the complete processed text
                if english_text:
                    final_verification = self._verify_english_content(english_text)
                    logger.info(f"Final text length: {len(english_text)} characters")
                    logger.info(f"Text filtering quality - English: {final_verification['is_english']}, "
                              f"Confidence: {final_verification['confidence']:.2f}, "
                              f"Issues: {final_verification['issues_count']}")

                    if final_verification["issues"]:
                        logger.warning(f"Final text still contains issues: {final_verification['issues']}")

            except Exception as e:
                logger.error(f"Error processing text: {e}")
                english_text = self._basic_english_filter(text) if text else ""
        
        # Save images
        doc_name = pdf_path.stem
        image_references = self.save_images(images, doc_name)
        
        # Create markdown with images
        title = doc_name.replace('_', ' ').replace('-', ' ').title()
        markdown_content = self.create_markdown_with_images(
            english_text, images, image_references, title
        )
        
        # Save markdown file
        output_path = self.output_dir / f"{doc_name}.md"
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"Saved markdown with {len(images)} images to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving markdown file {output_path}: {e}")
            return False
    
    def _basic_english_filter(self, text: str) -> str:
        """Conservative English filtering fallback with minimal copyright removal."""
        if not text:
            return ""

        # Only remove very specific copyright boilerplate
        text = self._remove_copyright_boilerplate(text)

        # Split into paragraphs but be more lenient
        paragraphs = text.split('\n\n')
        english_paragraphs = []

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if len(paragraph) < 10:  # Reduced from 20 to be less aggressive
                continue

            # Only skip if it's definitely boilerplate
            if self._is_boilerplate_line(paragraph):
                continue

            # Be more inclusive - assume English unless clearly not
            try:
                detected_lang = detect(paragraph)
                # Accept English or if detection fails
                if detected_lang == 'en' or detected_lang in ['en', 'cy', 'da', 'nl', 'no', 'sv']:  # Include similar languages
                    cleaned_paragraph = self._remove_copyright_boilerplate(paragraph)
                    if cleaned_paragraph.strip():
                        english_paragraphs.append(cleaned_paragraph)
            except (LangDetectException, Exception):
                # If language detection fails, be conservative and include it
                # Check for basic English indicators or technical content
                english_indicators = ['the', 'and', 'of', 'to', 'in', 'for', 'with', 'shall', 'should', 'may', 'can', 'will']
                technical_indicators = ['iec', 'standard', 'specification', 'protocol', 'interface', 'data', 'system', 'device']
                words = paragraph.lower().split()

                if (any(indicator in words for indicator in english_indicators) or
                    any(indicator in words for indicator in technical_indicators) or
                    any(char.isdigit() for char in paragraph)):  # Include if contains numbers (likely technical)
                    english_paragraphs.append(paragraph)

        result_text = '\n\n'.join(english_paragraphs)

        # Apply translation to the final result
        translated_text = self._translate_to_english(result_text)

        return translated_text
    
    def process_directory(self) -> None:
        """Process all PDF files in the input directory."""
        pdf_files = list(self.input_dir.glob("*.pdf"))
        
        if not pdf_files:
            logger.warning(f"No PDF files found in {self.input_dir}")
            return
        
        logger.info(f"Found {len(pdf_files)} PDF files to process")
        logger.info(f"Using Ollama LLM: {'Yes' if self.llm else 'No (basic processing)'}")
        logger.info(f"Images will be saved to: {self.images_dir}")
        
        if self.llm:
            logger.info(f"Ollama model: {self.llm.model}")
        
        successful = 0
        for pdf_file in pdf_files:
            try:
                if self.process_pdf(pdf_file):
                    successful += 1
            except Exception as e:
                logger.error(f"Error processing {pdf_file.name}: {e}")
        
        logger.info(f"Successfully processed {successful}/{len(pdf_files)} PDF files")
        logger.info(f"Output files saved to: {self.output_dir}")

        if successful > 0:
            logger.info("🎉 Processing completed with enhanced features:")
            logger.info("   ✅ Multi-column text extraction with proper reading order")
            logger.info("   ✅ Intelligent text quality evaluation and source selection")
            logger.info("   ✅ Aggressive filtering of poor quality supplementary text")
            logger.info("   ✅ Smart image filtering (logos and useless content removed)")
            logger.info("   ✅ Conservative content preservation with safety fallbacks")


def load_config_from_env() -> ProcessingConfig:
    """Load configuration from .env file."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.warning("python-dotenv not installed. Install with: pip install python-dotenv")
        logger.info("Using default configuration values")
    
    config = ProcessingConfig()
    
    # Load configuration from environment variables
    config.chunk_size = int(os.getenv('CHUNK_SIZE', config.chunk_size))
    config.ollama_url = os.getenv('OLLAMA_URL', config.ollama_url)
    config.model_name = os.getenv('MODEL_NAME', config.model_name)
    config.temperature = float(os.getenv('TEMPERATURE', config.temperature))
    config.max_retries = int(os.getenv('MAX_RETRIES', config.max_retries))
    config.retry_delay = float(os.getenv('RETRY_DELAY', config.retry_delay))
    config.use_parallel_processing = os.getenv('USE_PARALLEL_PROCESSING', 'true').lower() == 'true'
    config.max_workers = int(os.getenv('MAX_WORKERS', config.max_workers))
    config.timeout = int(os.getenv('TIMEOUT', config.timeout))
    config.min_image_size = int(os.getenv('MIN_IMAGE_SIZE', config.min_image_size))
    config.image_quality = int(os.getenv('IMAGE_QUALITY', config.image_quality))
    config.extract_tables = os.getenv('EXTRACT_TABLES', 'true').lower() == 'true'

    # OCR Configuration
    config.use_gpu_ocr = os.getenv('USE_GPU_OCR', 'true').lower() == 'true'
    config.ocr_engine = os.getenv('OCR_ENGINE', config.ocr_engine)
    config.ocr_confidence_threshold = float(os.getenv('OCR_CONFIDENCE_THRESHOLD', config.ocr_confidence_threshold))
    config.ocr_fallback_to_cpu = os.getenv('OCR_FALLBACK_TO_CPU', 'true').lower() == 'true'
    config.text_overlap_threshold = float(os.getenv('TEXT_OVERLAP_THRESHOLD', config.text_overlap_threshold))
    config.prefer_native_text = os.getenv('PREFER_NATIVE_TEXT', 'true').lower() == 'true'

    # OCR Languages
    ocr_langs = os.getenv('OCR_LANGUAGES', 'en')
    config.ocr_languages = [lang.strip() for lang in ocr_langs.split(',')]

    return config


def create_sample_env_file():
    """Create a sample .env file with default values."""
    sample_env_content = """# PDF to Markdown Processor Configuration

# Input/Output Directories
INPUT_DIR=./pdfs
OUTPUT_DIR=./markdown_output

# Ollama Configuration
OLLAMA_URL=http://localhost:11434
MODEL_NAME=llama2

# Processing Configuration
CHUNK_SIZE=2000
TEMPERATURE=0.1
MAX_RETRIES=3
RETRY_DELAY=2.0
USE_PARALLEL_PROCESSING=true
MAX_WORKERS=2
TIMEOUT=60

# Image Processing
MIN_IMAGE_SIZE=50
IMAGE_QUALITY=85
EXTRACT_TABLES=true

# OCR Configuration
USE_GPU_OCR=true
OCR_ENGINE=paddleocr
OCR_LANGUAGES=en
OCR_CONFIDENCE_THRESHOLD=0.5
OCR_FALLBACK_TO_CPU=true
TEXT_OVERLAP_THRESHOLD=0.8
PREFER_NATIVE_TEXT=true

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
"""
    
    env_path = Path('.env')
    if not env_path.exists():
        with open(env_path, 'w') as f:
            f.write(sample_env_content)
        print(f"Created sample .env file: {env_path}")
        print("Please edit the .env file with your preferred settings.")
        return True
    return False


def setup_logging():
    """Setup logging based on environment configuration."""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level, logging.INFO),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('pdf_processor.log')
        ]
    )


def main():
    """Main function to run the enhanced PDF processor."""
    print("Enhanced PDF to Markdown Processor with Image Extraction")
    print("Loading configuration from .env file...")
    
    # Create sample .env if it doesn't exist
    if create_sample_env_file():
        return
    
    # Setup logging
    setup_logging()
    
    # Load configuration
    config = load_config_from_env()
    
    # Get input/output directories from environment
    input_dir = os.getenv('INPUT_DIR')
    output_dir = os.getenv('OUTPUT_DIR')
    
    if not input_dir:
        logger.error("INPUT_DIR not specified in .env file")
        print("Please set INPUT_DIR in your .env file")
        sys.exit(1)
    
    if not os.path.exists(input_dir):
        logger.error(f"Input directory '{input_dir}' does not exist")
        sys.exit(1)
    
    # Log configuration
    logger.info("Configuration loaded:")
    logger.info(f"  Input directory: {input_dir}")
    logger.info(f"  Output directory: {output_dir or 'auto-generated'}")
    logger.info(f"  Ollama URL: {config.ollama_url}")
    logger.info(f"  Model: {config.model_name}")
    logger.info(f"  Chunk size: {config.chunk_size}")
    logger.info(f"  Parallel processing: {config.use_parallel_processing}")
    logger.info(f"  Extract images: {config.min_image_size}px minimum")
    
    print("\nSetup Instructions:")
    print("1. Install Ollama: https://ollama.ai/")
    print("2. Pull a model: ollama pull", config.model_name)
    print("3. Start server: ollama serve")
    print("4. Install OCR dependencies:")
    print("   - For PaddleOCR: pip install paddlepaddle-gpu paddleocr")
    print("   - For EasyOCR: pip install easyocr")
    print("   - For Tesseract: pip install pytesseract")
    print("\nFeatures:")
    print("- Extracts text and images from PDFs")
    print("- GPU-accelerated OCR for scanned documents")
    print("- Intelligent text overlap detection")
    print("- Filters for English content using LLM")
    print("- Preserves diagrams, figures, and tables")
    print("- Creates markdown optimized for RAG applications")
    print("- Embedded images with proper references")
    print(f"\nOCR Engine: {config.ocr_engine} (GPU: {config.use_gpu_ocr})")
    print(f"Processing PDFs from: {input_dir}")
    
    # Initialize and run processor
    try:
        processor = EnhancedPDFProcessor(input_dir, output_dir, config)
        processor.process_directory()
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        print("\nProcessing stopped by user.")
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
