#!/usr/bin/env python3
"""
Real VLM-Enhanced SVG Converter using Local Ollama
"""

import sys
import os
import base64
import json
import time
from pathlib import Path
from PIL import Image
import requests
from typing import Dict, Any, Optional
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re

class RealVLMSVGConverter:
    """Real VLM-based SVG converter using local Ollama server."""
    
    def __init__(self, model_name: str = "llava:7b", ollama_url: str = "http://127.0.0.1:11434"):
        self.model_name = model_name
        self.ollama_url = ollama_url
        self._test_connection()
    
    def _test_connection(self):
        """Test connection to Ollama server."""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = [m['name'] for m in response.json().get('models', [])]
                if self.model_name in models:
                    print(f"✅ Connected to Ollama. Using model: {self.model_name}")
                else:
                    print(f"⚠️ Model {self.model_name} not found. Available: {models}")
                    if models:
                        self.model_name = models[0]
                        print(f"Using: {self.model_name}")
            else:
                raise ConnectionError(f"Ollama returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Cannot connect to Ollama: {e}")
            raise
    
    def analyze_image_with_vlm(self, image_path: str) -> Dict[str, Any]:
        """Analyze image using real VLM."""
        
        print(f"🧠 Analyzing image with {self.model_name}...")
        
        try:
            # Encode image
            with open(image_path, "rb") as f:
                image_b64 = base64.b64encode(f.read()).decode('utf-8')
            
            # Detailed analysis prompt
            prompt = """Analyze this technical image in detail for SVG conversion. Provide:

1. **Image Type**: What specific type of technical content is this? (nomogram, circuit diagram, flowchart, table, etc.)

2. **Technical Elements**: List all technical components, measurements, scales, labels, and values you can identify.

3. **Visual Structure**: Describe the layout, organization, and spatial relationships of elements.

4. **Text Content**: List all readable text, numbers, labels, and their approximate positions.

5. **Geometric Elements**: Identify lines, grids, shapes, arrows, and their purposes.

6. **SVG Suitability**: Rate 1-10 how well this would convert to SVG and explain why.

7. **Conversion Strategy**: Suggest the best approach for creating an accurate SVG representation.

Be very specific about technical details, measurements, and spatial relationships."""
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "images": [image_b64],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 1000
                }
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=300  # 5 minutes
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                analysis_text = result.get('response', '')
                
                print(f"✅ VLM analysis complete ({processing_time:.1f}s)")
                
                # Parse the structured response
                parsed = self._parse_vlm_analysis(analysis_text)
                parsed['processing_time'] = processing_time
                parsed['raw_response'] = analysis_text
                
                return parsed
            else:
                raise Exception(f"VLM request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ VLM analysis failed: {e}")
            return {"error": str(e)}
    
    def _parse_vlm_analysis(self, text: str) -> Dict[str, Any]:
        """Parse VLM analysis response."""
        
        analysis = {
            "image_type": "",
            "technical_elements": "",
            "visual_structure": "",
            "text_content": "",
            "geometric_elements": "",
            "svg_suitability": 5,
            "conversion_strategy": ""
        }
        
        # Extract sections using patterns
        sections = {
            r"image type[:\s]*(.*?)(?=\n\d+\.|$)": "image_type",
            r"technical elements[:\s]*(.*?)(?=\n\d+\.|$)": "technical_elements",
            r"visual structure[:\s]*(.*?)(?=\n\d+\.|$)": "visual_structure",
            r"text content[:\s]*(.*?)(?=\n\d+\.|$)": "text_content",
            r"geometric elements[:\s]*(.*?)(?=\n\d+\.|$)": "geometric_elements",
            r"svg suitability[:\s]*(.*?)(?=\n\d+\.|$)": "svg_suitability",
            r"conversion strategy[:\s]*(.*?)(?=\n\d+\.|$)": "conversion_strategy"
        }
        
        for pattern, field in sections.items():
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                content = match.group(1).strip()
                if field == "svg_suitability":
                    # Extract numeric rating
                    rating_match = re.search(r'(\d+)', content)
                    analysis[field] = int(rating_match.group(1)) if rating_match else 5
                else:
                    analysis[field] = content
        
        return analysis
    
    def generate_svg_with_vlm(self, image_path: str, analysis: Dict[str, Any]) -> str:
        """Generate SVG using VLM understanding."""
        
        print(f"🎨 Generating SVG with VLM guidance...")
        
        try:
            # Get image dimensions
            img = Image.open(image_path)
            width, height = img.size
            
            # Create SVG prompt based on analysis
            svg_prompt = f"""Based on this analysis of a technical image:

Image Type: {analysis.get('image_type', 'Technical diagram')}
Technical Elements: {analysis.get('technical_elements', '')}
Visual Structure: {analysis.get('visual_structure', '')}
Text Content: {analysis.get('text_content', '')}
Geometric Elements: {analysis.get('geometric_elements', '')}

Create SVG code that recreates this image. The SVG should:
1. Have viewBox="0 0 {width} {height}"
2. Include all text elements with proper positioning
3. Recreate all geometric shapes and lines
4. Use semantic grouping (e.g., <g id="grid">, <g id="labels">)
5. Be clean and well-structured

Generate only the SVG code, no explanations."""
            
            # Encode image again for SVG generation
            with open(image_path, "rb") as f:
                image_b64 = base64.b64encode(f.read()).decode('utf-8')
            
            payload = {
                "model": self.model_name,
                "prompt": svg_prompt,
                "images": [image_b64],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 2000
                }
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                svg_response = result.get('response', '')
                
                # Extract SVG code
                svg_code = self._extract_svg_code(svg_response)
                
                # If no valid SVG, create intelligent fallback
                if not svg_code or len(svg_code) < 100:
                    svg_code = self._create_intelligent_fallback_svg(image_path, analysis)
                
                return svg_code
            else:
                raise Exception(f"SVG generation failed: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ VLM SVG generation failed: {e}")
            return self._create_intelligent_fallback_svg(image_path, analysis)
    
    def _extract_svg_code(self, response: str) -> str:
        """Extract SVG code from VLM response."""
        
        # Look for SVG tags
        svg_match = re.search(r'<svg[^>]*>.*?</svg>', response, re.DOTALL | re.IGNORECASE)
        if svg_match:
            return svg_match.group(0)
        
        # Look for partial SVG content
        if '<svg' in response.lower() and '</svg>' in response.lower():
            start = response.lower().find('<svg')
            end = response.lower().find('</svg>') + 6
            return response[start:end]
        
        return ""
    
    def _create_intelligent_fallback_svg(self, image_path: str, analysis: Dict[str, Any]) -> str:
        """Create intelligent fallback SVG based on VLM analysis."""
        
        img = Image.open(image_path)
        width, height = img.size
        
        image_type = analysis.get('image_type', 'Technical Diagram')
        svg_rating = analysis.get('svg_suitability', 5)
        technical_elements = analysis.get('technical_elements', '')
        
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {width} {height}" width="{width}" height="{height}">
  <defs>
    <style>
      .title {{ font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }}
      .analysis {{ font-family: Arial, sans-serif; font-size: 12px; fill: #666; }}
      .rating {{ font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2196f3; }}
    </style>
  </defs>
  
  <!-- Background -->
  <rect x="0" y="0" width="{width}" height="{height}" fill="white" stroke="#ddd" stroke-width="2"/>
  
  <!-- VLM Analysis Results -->
  <text x="{width//2}" y="40" text-anchor="middle" class="title">
    VLM-Analyzed: {image_type}
  </text>
  
  <text x="20" y="80" class="rating">
    SVG Suitability: {svg_rating}/10
  </text>
  
  <!-- Technical Elements Summary -->
  <text x="20" y="110" class="analysis">
    Technical Elements: {technical_elements[:80]}...
  </text>
  
  <!-- Placeholder for actual content -->
  <rect x="20" y="140" width="{width-40}" height="{height-180}" 
        fill="none" stroke="#999" stroke-width="2" stroke-dasharray="10,5"/>
  
  <text x="{width//2}" y="{height//2}" text-anchor="middle" class="analysis" font-size="16">
    [VLM-Enhanced SVG Conversion]
  </text>
  
  <text x="{width//2}" y="{height//2 + 30}" text-anchor="middle" class="analysis">
    Original image content would be vectorized here
  </text>
  
  <!-- Footer -->
  <text x="{width//2}" y="{height-20}" text-anchor="middle" class="analysis">
    Generated by Real VLM Converter - Processing Time: {analysis.get('processing_time', 0):.1f}s
  </text>
</svg>'''
        
        return svg_content
    
    def convert_image(self, image_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Convert image to SVG using real VLM."""
        
        if output_path is None:
            base_path = Path(image_path)
            output_path = base_path.parent / f"{base_path.stem}_real_vlm.svg"
        
        print(f"🎨 Real VLM Conversion: {image_path}")
        
        # Analyze with VLM
        analysis = self.analyze_image_with_vlm(image_path)
        
        if "error" in analysis:
            return {"success": False, "error": analysis["error"]}
        
        print(f"📊 VLM Analysis Results:")
        print(f"   Type: {analysis.get('image_type', 'Unknown')}")
        print(f"   SVG Suitability: {analysis.get('svg_suitability', 0)}/10")
        print(f"   Processing Time: {analysis.get('processing_time', 0):.1f}s")
        
        # Generate SVG
        svg_content = self.generate_svg_with_vlm(image_path, analysis)
        
        # Save SVG
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            # Calculate sizes
            original_size = os.path.getsize(image_path)
            svg_size = os.path.getsize(output_path)
            reduction = (1 - svg_size / original_size) * 100
            
            print(f"✅ Real VLM SVG created: {output_path}")
            print(f"📊 File size: {original_size:,} → {svg_size:,} bytes ({reduction:+.1f}%)")
            
            # Save detailed analysis
            analysis_file = Path(output_path).with_suffix('.analysis.txt')
            with open(analysis_file, 'w', encoding='utf-8') as f:
                f.write(f"Real VLM Analysis - {Path(image_path).name}\n")
                f.write("=" * 60 + "\n")
                f.write(f"Model: {self.model_name}\n")
                f.write(f"Processing Time: {analysis.get('processing_time', 0):.1f} seconds\n")
                f.write(f"SVG Suitability: {analysis.get('svg_suitability', 0)}/10\n\n")
                f.write("Detailed Analysis:\n")
                f.write(analysis.get('raw_response', ''))
            
            print(f"📄 Analysis saved: {analysis_file}")
            
            return {
                "success": True,
                "analysis": analysis,
                "svg_path": str(output_path),
                "analysis_path": str(analysis_file),
                "original_size": original_size,
                "svg_size": svg_size,
                "size_reduction": reduction,
                "method": "Real VLM with Ollama"
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to save SVG: {e}"}

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Real VLM Image to SVG Converter')
    parser.add_argument('image', help='Input image file')
    parser.add_argument('-o', '--output', help='Output SVG file')
    parser.add_argument('--model', default='llava:7b', help='Ollama model to use')
    parser.add_argument('--url', default='http://127.0.0.1:11434', help='Ollama server URL')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, don\'t convert')
    
    args = parser.parse_args()
    
    try:
        converter = RealVLMSVGConverter(model_name=args.model, ollama_url=args.url)
        
        if args.analyze_only:
            analysis = converter.analyze_image_with_vlm(args.image)
            if "error" not in analysis:
                print("\n🔍 Real VLM Analysis Results:")
                print("=" * 50)
                for key, value in analysis.items():
                    if key not in ["raw_response", "processing_time"]:
                        print(f"{key.replace('_', ' ').title()}: {value}")
        else:
            result = converter.convert_image(args.image, args.output)
            if result["success"]:
                print(f"\n🎉 Real VLM conversion successful!")
                print(f"   Method: {result['method']}")
                print(f"   SVG Suitability: {result['analysis'].get('svg_suitability', 0)}/10")
                print(f"   File Reduction: {result['size_reduction']:.1f}%")
            else:
                print(f"❌ Conversion failed: {result['error']}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
