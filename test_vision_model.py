#!/usr/bin/env python3
"""
Test script for Vision Model integration with qwen2.5vl:7b
"""

import sys
import os
import requests
import base64
from pathlib import Path
from dataclasses import dataclass

@dataclass
class ProcessingConfig:
    """Simple config for testing."""
    vision_model_name: str = "qwen2.5vl:7b"
    ollama_url: str = "http://************:11434"
    timeout: int = 180

class VisionModel:
    """Simple Vision Model client for testing."""

    def __init__(self, config):
        self.config = config
        self.base_url = config.ollama_url.rstrip('/')
        self.model = config.vision_model_name
        self.generate_url = f"{self.base_url}/api/generate"

    def analyze_image(self, image_path: str, ocr_text: str = "", page_num: int = 0):
        """Analyze image using vision model with OCR context."""
        try:
            # Convert image to base64
            with open(image_path, 'rb') as img_file:
                image_data = base64.b64encode(img_file.read()).decode('utf-8')

            # Create comprehensive prompt
            system_prompt = """You are an expert technical document analyst. Analyze the provided image and create a comprehensive description.

INSTRUCTIONS:
1. Describe what type of image this is (diagram, table, chart, schematic, etc.)
2. Identify key technical elements, components, or data
3. If OCR text is provided, format it properly and integrate it into your description
4. Focus on technical accuracy and detail
5. Make the description useful for technical documentation and search

Format your response as:
**Image Type:** [type of image]
**Description:** [detailed technical description]
**Key Elements:** [bullet points of important elements]
**Extracted Text:** [properly formatted OCR text if available]"""

            prompt = f"""Analyze this technical image from page {page_num + 1}.

OCR Text detected in image:
{ocr_text if ocr_text.strip() else "No text detected"}

Please provide a comprehensive technical analysis of this image."""

            payload = {
                "model": self.model,
                "prompt": f"{system_prompt}\n\nUser: {prompt}\n\nAssistant:",
                "images": [image_data],
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 1000
                }
            }

            response = requests.post(self.generate_url, json=payload, timeout=self.config.timeout)
            response.raise_for_status()

            result = response.json()
            analysis = result.get('response', '').strip()

            return {
                "analysis": analysis,
                "ocr_text": ocr_text,
                "success": True
            }

        except Exception as e:
            return {
                "analysis": f"**Image Type:** Technical Image\n**Description:** Image from page {page_num + 1} (analysis unavailable: {e})\n**Extracted Text:** {ocr_text if ocr_text.strip() else 'No text detected'}",
                "ocr_text": ocr_text,
                "success": False
            }

def test_vision_model():
    """Test the vision model with a sample image."""
    
    # Create config
    config = ProcessingConfig()
    config.vision_model_name = "qwen2.5vl:7b"
    config.ollama_url = "http://************:11434"
    
    print(f"🔍 Testing Vision Model: {config.vision_model_name}")
    print(f"📡 Server: {config.ollama_url}")
    
    try:
        # Initialize vision model
        vision_model = VisionModel(config)
        print("✅ Vision model initialized successfully")
        
        # Find a test image
        images_dir = Path("markdown_output/images")
        if not images_dir.exists():
            print("❌ No images directory found. Run PDF processor first.")
            return
            
        # Get first available image
        image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpeg")) + list(images_dir.glob("*.jpg"))
        if not image_files:
            print("❌ No image files found in markdown_output/images/")
            return
            
        test_image = image_files[0]
        print(f"🖼️ Testing with image: {test_image.name}")
        
        # Test with sample OCR text
        sample_ocr = "Sample OCR text from image processing"
        
        # Analyze the image
        print("🧠 Analyzing image with vision model...")
        result = vision_model.analyze_image(str(test_image), sample_ocr, 0)
        
        if result["success"]:
            print("✅ Vision analysis successful!")
            print("\n📋 **ANALYSIS RESULT:**")
            print("=" * 50)
            print(result["analysis"])
            print("=" * 50)
        else:
            print("⚠️ Vision analysis failed, using fallback")
            print(f"Fallback result: {result['analysis']}")
            
    except Exception as e:
        print(f"❌ Error testing vision model: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_vision_model()
